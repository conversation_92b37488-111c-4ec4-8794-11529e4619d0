<?php
session_start();
?>



<?php
include_once 'Nav.php';
$sql = "SELECT * FROM Sy_pages";
$result = mysqli_query($connect, $sql);
// 检查查询结果是否为空
if (mysqli_num_rows($result) > 0) {
    // 获取第一行数据作为结果集
    $row = mysqli_fetch_assoc($result);
}
?>
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">发帖页设置</h4>
                <form class="needs-validation" action="postPagePost.php" method="post" onsubmit="return check()" novalidate>
                    <div class="form-group mb-3">
                        <label for="Admin">限制发帖圈子mid：</label><span class="badge badge-success-lighten"style="font-size: 0.8rem;">仅管理员可发帖</span>
                        <input name="Admin" class="form-control" type="text" required="" id="Admin" placeholder="多个请用英文逗号隔开" value="<?php echo $row['Admin']; ?>">
                    </div>
                    <div class="form-group mb-3">
                        <script>
                            function myOnClickHandler11(obj) {
                                var input = document.getElementById("switch11");
                                console.log(input);
                                if (obj.checked) {
                                    console.log("打开");
                                    input.value = "1";
                                } else {
                                    console.log("关闭");
                                    input.value = "0";
                                }
                            }
                        </script>
                        <label for="validationCustom01">免费图库</label>
                            <?php
                            if ($row['Gallery']==1) {
                                echo '<input type="checkbox" name="Gallery" id="switch11" value="1" data-switch="success"
                               onclick="myOnClickHandler11(this)" checked>';
                            }else{
                                echo '<input type="checkbox" name="Gallery" id="switch11" value="0" data-switch="success"
                               onclick="myOnClickHandler11(this)">';
                            }
                            ?>
                        
                        <label id="switchurl" style="display:block;" for="switch11" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                    <div class="form-group mb-3">
                        <script>
                            function myOnClickHandler12(obj) {
                                var input = document.getElementById("switch12");
                                console.log(input);
                                if (obj.checked) {
                                    console.log("打开");
                                    input.value = "1";
                                } else {
                                    console.log("关闭");
                                    input.value = "0";
                                }
                            }
                        </script>
                        <label for="validationCustom01">代码片段</label>
                            <?php
                            if ($row['Code']==1) {
                                echo '<input type="checkbox" name="Code" id="switch12" value="1" data-switch="success"
                               onclick="myOnClickHandler12(this)" checked>';
                            }else{
                                echo '<input type="checkbox" name="Code" id="switch12" value="0" data-switch="success"
                               onclick="myOnClickHandler12(this)">';
                            }
                            ?>
                        
                        <label id="switchurl" style="display:block;" for="switch12" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                    <div class="form-group mb-3">
                        <script>
                            function myOnClickHandler13(obj) {
                                var input = document.getElementById("switch13");
                                console.log(input);
                                if (obj.checked) {
                                    console.log("打开");
                                    input.value = "1";
                                } else {
                                    console.log("关闭");
                                    input.value = "0";
                                }
                            }
                        </script>
                        <label for="validationCustom01">超链接</label>
                            <?php
                            if ($row['Hyperlinks']==1) {
                                echo '<input type="checkbox" name="Hyperlinks" id="switch13" value="1" data-switch="success"
                               onclick="myOnClickHandler13(this)" checked>';
                            }else{
                                echo '<input type="checkbox" name="Hyperlinks" id="switch13" value="0" data-switch="success"
                               onclick="myOnClickHandler13(this)">';
                            }
                            ?>
                        
                        <label id="switchurl" style="display:block;" for="switch13" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                    <div class="form-group mb-3">
                        <script>
                            function myOnClickHandler14(obj) {
                                var input = document.getElementById("switch14");
                                console.log(input);
                                if (obj.checked) {
                                    console.log("打开");
                                    input.value = "1";
                                } else {
                                    console.log("关闭");
                                    input.value = "0";
                                }
                            }
                        </script>
                        <label for="validationCustom01">回复可见</label>
                            <?php
                            if ($row['Comments']==1) {
                                echo '<input type="checkbox" name="Comments" id="switch14" value="1" data-switch="success"
                               onclick="myOnClickHandler14(this)" checked>';
                            }else{
                                echo '<input type="checkbox" name="Comments" id="switch14" value="0" data-switch="success"
                               onclick="myOnClickHandler14(this)">';
                            }
                            ?>
                        
                        <label id="switchurl" style="display:block;" for="switch14" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                    <div class="form-group mb-3">
                        <script>
                            function myOnClickHandler15(obj) {
                                var input = document.getElementById("switch15");
                                console.log(input);
                                if (obj.checked) {
                                    console.log("打开");
                                    input.value = "1";
                                } else {
                                    console.log("关闭");
                                    input.value = "0";
                                }
                            }
                        </script>
                        <label for="validationCustom01">上传图片</label>
                            <?php
                            if ($row['Image']==1) {
                                echo '<input type="checkbox" name="Image" id="switch15" value="1" data-switch="success"
                               onclick="myOnClickHandler15(this)" checked>';
                            }else{
                                echo '<input type="checkbox" name="Image" id="switch15" value="0" data-switch="success"
                               onclick="myOnClickHandler15(this)">';
                            }
                            ?>
                        
                        <label id="switchurl" style="display:block;" for="switch15" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                    <div class="form-group mb-3">
                        <script>
                            function myOnClickHandler16(obj) {
                                var input = document.getElementById("switch16");
                                console.log(input);
                                if (obj.checked) {
                                    console.log("打开");
                                    input.value = "1";
                                } else {
                                    console.log("关闭");
                                    input.value = "0";
                                }
                            }
                        </script>
                        <label for="validationCustom01">上传视频</label>
                            <?php
                            if ($row['Video']==1) {
                                echo '<input type="checkbox" name="Video" id="switch16" value="1" data-switch="success"
                               onclick="myOnClickHandler16(this)" checked>';
                            }else{
                                echo '<input type="checkbox" name="Video" id="switch16" value="0" data-switch="success"
                               onclick="myOnClickHandler16(this)">';
                            }
                            ?>
                        
                        <label id="switchurl" style="display:block;" for="switch16" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                    <div class="form-group mb-3">
                        <script>
                            function myOnClickHandler17(obj) {
                                var input = document.getElementById("switch17");
                                console.log(input);
                                if (obj.checked) {
                                    console.log("打开");
                                    input.value = "1";
                                } else {
                                    console.log("关闭");
                                    input.value = "0";
                                }
                            }
                        </script>
                        <label for="validationCustom01">插入话题</label>
                            <?php
                            if ($row['Topic']==1) {
                                echo '<input type="checkbox" name="Topic" id="switch17" value="1" data-switch="success"
                               onclick="myOnClickHandler17(this)" checked>';
                            }else{
                                echo '<input type="checkbox" name="Topic" id="switch17" value="0" data-switch="success"
                               onclick="myOnClickHandler17(this)">';
                            }
                            ?>
                        
                        <label id="switchurl" style="display:block;" for="switch17" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                    <div class="form-group mb-3">
                        <script>
                            function myOnClickHandler18(obj) {
                                var input = document.getElementById("switch18");
                                console.log(input);
                                if (obj.checked) {
                                    console.log("打开");
                                    input.value = "1";
                                } else {
                                    console.log("关闭");
                                    input.value = "0";
                                }
                            }
                        </script>
                        <label for="validationCustom01">插入商品</label>
                            <?php
                            if ($row['Shop']==1) {
                                echo '<input type="checkbox" name="Shop" id="switch18" value="1" data-switch="success"
                               onclick="myOnClickHandler18(this)" checked>';
                            }else{
                                echo '<input type="checkbox" name="Shop" id="switch18" value="0" data-switch="success"
                               onclick="myOnClickHandler18(this)">';
                            }
                            ?>
                        
                        <label id="switchurl" style="display:block;" for="switch18" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                    <div class="form-group mb-3">
                        <script>
                            function myOnClickHandler19(obj) {
                                var input = document.getElementById("switch19");
                                console.log(input);
                                if (obj.checked) {
                                    console.log("打开");
                                    input.value = "1";
                                } else {
                                    console.log("关闭");
                                    input.value = "0";
                                }
                            }
                        </script>
                        <label for="validationCustom01">VIP可见内容</label>
                            <?php
                            if ($row['Viptext']==1) {
                                echo '<input type="checkbox" name="Viptext" id="switch19" value="1" data-switch="success"
                               onclick="myOnClickHandler19(this)" checked>';
                            }else{
                                echo '<input type="checkbox" name="Viptext" id="switch19" value="0" data-switch="success"
                               onclick="myOnClickHandler19(this)">';
                            }
                            ?>
                        
                        <label id="switchurl" style="display:block;" for="switch19" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                    
                    
                    <div class="form-group mb-3">
                        <script>
                            function myOnClickHandler1(obj) {
                                var input = document.getElementById("switch1");
                                var PaymentMethods = document.getElementById("PaymentMethods");
                                console.log(input);
                                if (obj.checked) {
                                    console.log("打开");
                                    input.value = "1";
                                    PaymentMethods.style.display = "block";
                                } else {
                                    console.log("关闭");
                                    input.value = "0";
                                    PaymentMethods.style.display = "none";
                                }
                            }
                        </script>
                        <label for="validationCustom01">插入音乐</label>
                            <?php
                            if ($row['Music']==1) {
                                echo '<input type="checkbox" name="Music" id="switch1" value="1" data-switch="success"
                               onclick="myOnClickHandler1(this)" checked>';
                            }else{
                                echo '<input type="checkbox" name="Music" id="switch1" value="0" data-switch="success"
                               onclick="myOnClickHandler1(this)">';
                            }
                            ?>
                        
                        <label id="switchurl" style="display:block;" for="switch1" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                    <?php
                    if ($row['Music']==1) {
                        echo '<div id="PaymentMethods" style="display: block;">';
                    } else {
                        echo '<div id="PaymentMethods" style="display: none;">';
                    }
                    ?>
                    <div class="form-group mb-3">
                          <label for="Banner">随机封面图1：</label>
                          <input name="Musicimg1" class="form-control" type="url" required="" id="Banner" placeholder="图片链接" value="<?php echo $row['Musicimg1']; ?>">
                    <?php
                    if ($row['Musicimg1'] !== '') {
                        echo '<label for="yl" style="margin-top:.5rem">预览图：</label><br><span class="dtr-data" id="yl"><img style="width: 130px;height: 130px;object-fit: cover;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" src="' . $row['Musicimg1'] . '" class="spotlight"></span></div>';
                    } else {
                        echo '</div>';
                    }
                    ?>
                    
                    <div class="form-group mb-3">
                          <label for="Banner">随机封面图2：</label>
                          <input name="Musicimg2" class="form-control" type="url" required="" id="Banner" placeholder="图片链接" value="<?php echo $row['Musicimg2']; ?>">
                         
                    <?php
                    if ($row['Musicimg2'] !== '') {
                        echo '<label for="yl" style="margin-top:.5rem">预览图：</label><br><span class="dtr-data" id="yl"><img style="width: 130px;height: 130px;object-fit: cover;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" src="' . $row['Musicimg2'] . '" class="spotlight"></span></div>';
                    } else {
                        echo '</div>';
                    }
                    ?>
                    <div class="form-group mb-3">
                          <label for="Banner">随机封面图3：</label>
                          <input name="Musicimg3" class="form-control" type="url" required="" id="Banner" placeholder="图片链接" value="<?php echo $row['Musicimg3']; ?>">
                          
                    <?php
                    if ($row['Musicimg3'] !== '') {
                        echo '<label for="yl" style="margin-top:.5rem">预览图：</label><br><span class="dtr-data" id="yl"><img style="width: 130px;height: 130px;object-fit: cover;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" src="' . $row['Musicimg3'] . '" class="spotlight"></span></div>';
                    } else {
                        echo '</div>';
                    }
                    ?>
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success" type="submit" id="postPagePost">保存修改</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->




<?php
include_once 'Footer.php';
?>

</body>
</html>