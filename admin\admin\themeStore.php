<?php
session_start();
include_once 'connect.php';
include_once 'Nav.php';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主题商城</title>
    <link rel="stylesheet" href="/admin/assets/css/bootstrap.min.css">
    <style>
        .card-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            gap: 60px;
        }
        .theme-card {
            width: 330px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            background-color: #fff;
            transition: transform 0.3s ease;
        }
        .theme-card:hover {
            transform: translateY(-10px);
        }
        .theme-card img {
             width: 100%;
            height: 200px;
            object-position: center;
            object-fit: cover;
        }
        .theme-card-body {
            padding: 15px;
        }
        .theme-card-title {
            font-size: 1.25rem;
            color:#494949;
            font-weight: bold;
            margin-bottom: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .theme-card-author {
            font-size: 0.9rem;
            color: #888;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .card-author {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .card-price {
            font-size: 16px;
            font-weight: bold;
        }
        .price-buy {
            color: #ff6a6a;
        }
        .price-free {
            color: #0acf97;
        }
        .mt-5, .my-5 {
            margin-top: 1.5rem !important;
        }
        .mb-3, .my-3 {
            margin-bottom: 0.5rem !important;
        }
        .theme-card-description {
            font-size: 0.9rem;
            color: #9a9a9a;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
        }
        .content-page {
            overflow: hidden;
            padding: 0 0px 50px 0px;
            position: relative;
            margin-right: -15px;
            width: 100%;
            padding-bottom: 60px;
        }
        .title-h1 {
            width: 100%;
            margin-right: auto;
        }
        h2.header-title.mb-3 {
            text-align: center;
            font-size: 2.1rem;
            font-family: 'Noto Serif SC', serif;
            font-weight: 700;
        }
        .text-center {
            text-align: center !important;
        }
        .btn-success2 {
                        box-shadow: 0 2px 6px 0 #32b48f59;
                        color: #fff;
                        background-color: #50d5b0;
                        border-color: #0acf97;
                        border-radius: 50px;
                    }
        @media (max-width: 992px) {
            .card-container {
                display: flex;
                flex-wrap: wrap;
                justify-content: flex-start;
                gap: 30px;
            }
            .theme-card {
                width: 330px;
                border-radius: 10px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                overflow: hidden;
                background-color: #fff;
                transition: transform 0.3s ease;
            }
        }
        @media (max-width: 767.98px) {
            .card-container {
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                gap: 20px;
            }
            .theme-card {
                border-radius: 10px;
            }
            .theme-card img {
                width: 330px;
                height: 190px;
                object-fit: cover;
                object-position: center;
            }
            .theme-card-body {
                padding: 15px;
            }
            .theme-card-title {
                font-size: 1.25rem;
                color:#494949;
                font-weight: bold;
                margin-bottom: 5px;
            }
            .theme-card-author {
                font-size: 0.9rem;
                color: #888;
                margin-bottom: 5px;
            }
            .mt-5, .my-5 {
                margin-top: 5rem !important; 
            }
            .container {
                width: 100%;
                padding-right: 0px;
                padding-left: 0px;
                margin-right: auto;
                margin-left: auto;
            }
            .content-page {
                margin-left: 0 !important;
                padding: 0px 0px 90px 0px;
            }
            .container-fluid {
                width: 100%;
                padding-right: 15px;
                padding-left: 15px;
                margin-right: auto;
                margin-left: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h2 class="header-title mb-3 text-center title-h1">主题商城</h2>
        <br>
        <a class="fabu" target="_blank" href='https://www.yuque.com/senyun-ev0j3/starpro/ptxnq4wcfkf6uhco#dDXl9'>
            <button type="button" class="btn btn-success2 btn-sm btn-rounded" style="margin-bottom: 20px;">
                <i class="dripicons-trophy mr-1"></i><span>成为开发者</span>
            </button>
        </a>

        <div class="alert alert-danger alert-dismissible bg-danger text-white border-0 fade show" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <strong>注意</strong> 主题作者除’森云‘外的主题都属于第三方主题，官方不保证所有第三方主题的更新、售后服务，并且付费主题可能存在未知BUG、部分官方功能失效的风险，购买前请自己谨慎辨别，购买后不支持退款！建议购买前体验对应的演示站。
        </div>
        <div class="card-container" id="pluginContainer">
        </div>
        <div id="loader" class="text-center" style="display: none;">
            <div class="spinner-border" role="status">
                <span class="sr-only">Loading...</span>
            </div>
        </div>
        <div class="text-center" style="color:#a9a9a9;margin-top:30px" id="noMoreContent" style="display: none;">
            暂无更多主题，期待你一起参与开发~
        </div>
    </div>

    <?php include_once 'Footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.3.3/js/bootstrap.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let page = 1;
            const limit = 9;
            let isLoading = false;
            let allLoaded = false;

            function loadPlugins() {
                if (isLoading || allLoaded) return;
                isLoading = true;
                document.getElementById('loader').style.display = 'block';

                fetch(`https://store.starpro.site/get.php?ac=getThemeList&order=time&limit=${limit}&page=${page}`)
                    .then(response => response.json())
                    .then(response => {
                        if (response.code === 200) {
                            const plugins = response.data;
                            const container = document.getElementById('pluginContainer');
                        
                            if (plugins.length > 0) {
                                plugins.forEach(plugin => {
                                    const card = `
                                        <a class="fabu" href="StoreInfo.php?type=theme&fid=${plugin.fid}&imageurl=${plugin.imageurl}&author=${plugin.author}&cp=${plugin.cp}&price=${plugin.price}">
                                        <div class="theme-card">
                                            <img src="${plugin.imageurl || 'https://store.starpro.site/logo.png'}" alt="${plugin.cp}">
                                            <div class="theme-card-body">
                                                <div class="theme-card-title">${plugin.cp === 'starpro' ? 'StarPro 2.0' : plugin.cp}</div>
                                                <div class="theme-card-author">
                                                    <div class="card-author">作者: ${plugin.author}</div>
                                                    <div class="card-price ${plugin.price === '0' ? 'price-free' : 'price-buy'}">
                                                        ${plugin.price === '0' ? '免费' : plugin.price + '￥'}
                                                    </div>
                                                </div>
                                                <div class="theme-card-description">${plugin.ms || '暂无描述'}</div>
                                            </div>
                                        </div>
                                        </a>`;
                                    container.insertAdjacentHTML('beforeend', card);
                                });
                        
                                page++;
                            } else {
                                allLoaded = true;
                                document.getElementById('noMoreContent').style.display = 'block';
                            }
                        } else {
                            
                            const errorContainer = document.getElementById('pluginContainer');
                            errorContainer.insertAdjacentHTML('beforeend', '<div>请求商城云端失败，可能商城被攻击啦~</div>');
                        }
                        isLoading = false;
                        document.getElementById('loader').style.display = 'none';
                    })
                    .catch(() => {
                        isLoading = false;
                        document.getElementById('loader').style.display = 'none';
                    });
            }

            let debounceTimeout;
            function debounce(func, delay) {
                clearTimeout(debounceTimeout);
                debounceTimeout = setTimeout(func, delay);
            }

            window.addEventListener('scroll', function() {
                debounce(function() {
                    if (window.scrollY + window.innerHeight >= document.documentElement.scrollHeight - 100) {
                        loadPlugins();
                    }
                }, 200);
            });

            loadPlugins();
        });
    </script>
</body>



</html>
