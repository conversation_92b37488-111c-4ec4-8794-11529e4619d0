<?php
session_start();
?>


<?php
include_once 'Nav.php';

//获取配置有问题
$curl = curl_init();
$url = $API_GET_API_CONFIG.'?webkey='.$api_key;
curl_setopt_array($curl, array(
   CURLOPT_URL => $url,
   CURLOPT_RETURNTRANSFER => true,
   CURLOPT_ENCODING => '',
   CURLOPT_MAXREDIRS => 10,
   CURLOPT_SSL_VERIFYPEER => false,
   CURLOPT_SSL_VERIFYHOST => false,
   CURLOPT_TIMEOUT => 0,
   CURLOPT_FOLLOWLOCATION => true,
   CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
   CURLOPT_CUSTOMREQUEST => 'GET',
));
$response = curl_exec($curl);
$responseData = json_decode($response, true);  
if ($responseData && isset($responseData['code']) && $responseData['code'] == 1) {  
    $wxAppId = $responseData['data']['wxAppId'];    
    $wxAppSecret = $responseData['data']['wxAppSecret'];  
    $appletsAppid = $responseData['data']['appletsAppid'];  
    $appletsSecret = $responseData['data']['appletsSecret'];  

} 
?>
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">微信配置</h4>
                <form class="needs-validation" action="loginWxPost.php" method="post"
                      novalidate>
                    <div class="form-group mb-3">
                          <p>在这里配置微信登录和小程序相关信息。</p>
                    </div>
                    <div class="form-group mb-3">
                          <label for="wxAppId">微信应用ID录
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              可不填，负责微信APP登录
                          </span></label>
                          <input name="wxAppId" class="form-control" type="text" id="wxAppId" placeholder="请输入微信应用ID录" value="<?php echo $wxAppId; ?>">
                    </div>
                    <div class="form-group mb-3">
                          <label for="wxAppSecret">微信应用Secret
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              可不填，负责微信APP登录
                          </span></label>
                          <input name="wxAppSecret" class="form-control" type="text" id="wxAppSecret" placeholder="请输入微信应用Secret" value="<?php echo $wxAppSecret;  ?>">
                    </div>
                    <div class="form-group mb-3">
                          <label for="appletsAppid">微信小程序APPID
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              可不填，负责微信小程序登录
                          </span></label>
                          <input name="appletsAppid" class="form-control" type="text" id="appletsAppid" placeholder="请输入微信小程序APPID" value="<?php echo $appletsAppid;  ?>">
                    </div>
                    <div class="form-group mb-3">
                          <label for="appletsSecret">微信小程序Secret
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              可不填，负责微信小程序登录
                          </span></label>
                          <input name="appletsSecret" class="form-control" type="text" id="appletsSecret" placeholder="请输入微信小程序Secret" value="<?php echo $appletsSecret;  ?>">
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success" type="submit" id="loginWxPost">保存修改</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->


<?php
include_once 'Footer.php';
?>

</body>
</html>