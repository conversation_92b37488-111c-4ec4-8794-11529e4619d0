<?php
session_start();
?>



<?php
include_once 'Nav.php';
$id = $_GET['id'];

$article = "SELECT * FROM typecho_space WHERE id='$id' limit 1";
$resarticle = mysqli_query($connect, $article);
$mod = mysqli_fetch_array($resarticle);
if ($mod['type'] == '0'||$mod['type'] == '4') {
    if ($mod['pic'] != NULL) {
       if (strpos($mod['pic'], "||") !== false) {
            $links = explode("||", $mod['pic']);
            $linkCount = count($links);
            for ($i = 0; $i < $linkCount; $i++) {
                ${"link" . ($i + 1)} = $links[$i];
            }
            $links = true;
        } else {
            $links = false;
            $link = $mod['pic'];
        }
    }
}
if ($mod['pic'] == NULL&&$mod['type'] =='0') {
    $noimg = true;
}else{
    $noimg = false;
}

?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3 size_18">审核动态</h4>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">动态id</label>
                        <input type="text" class="form-control" id="validationCustom01"
                               name="cid" value="<?php echo $id ?>" readonly>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">发布者UID</label>
                        <input type="text" class="form-control" id="validationCustom01" placeholder="请输入发布者UID"
                               name="uid" value="<?php echo $mod['uid'] ?>" readonly>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">动态类型</label>
                            <select class="form-control" id="example-select" name="type" readonly disabled>
                       <?php
                                if ($mod['type'] == '0') {
                                    echo '<option value="0" selected>图文动态</option>';
                                } else {
                                    echo '<option value="0">图文动态</option>';
                                }
                                if ($mod['type'] == '4') {
                                    echo '<option value="4" selected>视频动态</option>';
                                } else {
                                    echo '<option value="4">视频动态</option>';
                                }
                                if ($mod['type'] == '3') {
                                    echo '<option value="3" selected>动态评论</option>';
                                } else {
                                    echo '<option value="3">动态评论</option>';
                                }
                                if ($mod['type'] == '1') {
                                    echo '<option value="1" selected>转发文章</option>';
                                } else {
                                    echo '<option value="1">转发文章</option>';
                                }
                                if ($mod['type'] == '2') {
                                    echo '<option value="2" selected>转发动态</option>';
                                } else {
                                    echo '<option value="2">转发动态</option>';
                                }
                                if ($mod['type'] == '5') {
                                    echo '<option value="5" selected>转发商品</option>';
                                } else {
                                    echo '<option value="5">转发商品</option>';
                                }
                                ?>
                        </select>
                    </div>
                    <div class="form-group mb-3">
                     <label for="validationCustom01">仅自己可见</label>
                    <?php
                    if ($mod['onlyMe']==1) {
                        echo '<input type="checkbox" name="Share" id="switch1" value="1" data-switch="success"
                           onclick="myOnClickHandler2(this)" checked disabled>';
                    }else{
                        echo '<input type="checkbox" name="Share" id="switch1" value="0" data-switch="success"
                           onclick="myOnClickHandler2(this)" disabled>';
                    }
                    ?>
                    <label id="switchurl" style="display:block;" for="switch1" data-on-label="打开"
                           data-off-label="关闭" disabled></label>
                    </div>
                    
                   <label for="validationCustom01">动态内容</label>
                        <textarea id="notice" class="form-control" rows="6" name="text" readonly><?php echo $mod['text'] ?></textarea>
                        
                    <?php if($mod['type'] == '0'){
                        if (!$noimg) {
                            
                          if ($links) {
                              echo '<div class="form-group mb-3">
                                        <label for="yl" style="margin-top:.5rem">动态图片：</label><br>';
                                for ($i = 1; $i <= $linkCount; $i++) {
                                    echo '<span class="dtr-data" id="yl"><img style="width: 200px;margin-top:20px;margin-right:20px;object-fit: cover;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" src="'.${"link" . $i}.'" class="spotlight"></span>';
                                }
                                echo '</div>';
                                
                            } else {
                                echo '<div class="form-group mb-3">
                                    <label for="yl" style="margin-top:.5rem">动态图片：</label><br><span class="dtr-data" id="yl"><img style="width: 200px;object-fit: cover;margin-top:20px;margin-right:20px;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" src="'.$link.'" class="spotlight"></span></div>';
                            }
                        }
                    } else if($mod['type'] == '4') {
                     
                       echo '<div class="form-group col-sm-4">
                        <label for="yl" style="margin-top:.5rem">动态视频：</label><br><span class="dtr-data" id="yl"><video style="width: 100%;object-fit: cover;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" src="'.$link.'" class="spotlight"  controls/></span></div>';
                    } else if($mod['type'] == '3') {
                     
                       echo '<div class="form-group mb-3"><br>
                            <span class="badge badge-success-lighten" style="font-size: 1.2rem;width:100%;height:200px;display: flex;justify-content: center;align-items: center;">该条为动态评论</span></div>';
                    }  else {
                        echo '<div class="form-group mb-3"><br>
                            <span class="badge badge-danger-lighten" style="font-size: 1.2rem;width:100%;height:200px;display: flex;justify-content: center;align-items: center;">暂不支持查看转发内容</span></div>';
                    }
                    ;?>
                    <div class="form-group mb-3 text_right">
                        <a class="fabu" onclick="Pass('<?php echo $id ;?>')">
                            <button class="btn btn-primary" id="payoutPost" style="margin-right:10px">通过</button>
                        </a>
                        <a class="fabu" onclick="Refuse('<?php echo $id ;?>')">
                            <button class="btn btn-danger" id="payoutPost">拒绝</button>
                        </a>
                    </div>

            </div> <!-- end card-body-->
        </div> <!-- end card-->
    </div> <!-- end col-->
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->


<script>
    function Pass(id) {
        if (confirm('您确认要通过该动态吗？')) {
            location.href = 'spaceAuditPost.php?id=' + id +'&status=Pass';
        }
    }
    function Refuse(id) {
        if (confirm('您确认要拒绝该动态吗？')) {
            location.href = 'spaceAuditPost.php?id=' + id +'&status=Refuse';
        }
    }
    
</script>

<?php
include_once 'Footer.php';
?>

</body>
</html>