<?php
session_start();
?>
<?php
include_once 'connect.php';
$id = $_POST['id'];
$type = $_POST['type'];
if($type == 'sort'){
    $parent = '0';
}else if($type == 'section'){
    $parent = $_POST['parent'];
}
$name = $_POST['name'];
$intro = $name;
$pic = $_POST['pic'];
$orderKey = $_POST['orderKey'];
if (empty($pic)) {
    $pic = NULL;
}
$file = $_SERVER['PHP_SELF'];


if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    $charu = "UPDATE typecho_shoptype SET `parent`='$parent' ,  `name`='$name' , `intro`='$intro' , `orderKey`='$orderKey' , `pic`='$pic' WHERE id = '$id'";
    $result = mysqli_query($connect, $charu);
    if ($result) {
            echo "<script>alert('修改成功');location.href = 'shopTypeAdmin.php';</script>";
        } else {
            echo "<script>alert('修改失败');history.back();</script>";   
        }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
