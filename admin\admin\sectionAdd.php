<?php
session_start();
?>



<?php
include_once 'Nav.php';
$withdrawals = "SELECT * FROM typecho_forum_section WHERE type = 'sort' ORDER BY id DESC";
$withdrawalsResult = mysqli_query($connect, $withdrawals);

?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3 size_18">新增圈子</h4>

                <form class="needs-validation" action="sectionAddPost.php" method="post" onsubmit="return check()"
                      novalidate>
                    
                    <div id="div1">
                    </div>
                    
                     
                    <div class="form-group col-sm-4">
                        <label>类型</label>
                            <select class="form-control" id="dynamic-type" name="type">
                                    <option value="sort" selected>大类</option>
                                    <option value="section">圈子</option>
                            </select>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom01">名称</label>
                        <input type="text" class="form-control" id="validationCustom01" placeholder="请输入名称"
                               name="name" required>
                    </div>
                    <div id="div2">
                     <div class="form-group mb-3">
                        <label for="validationCustom01">缩略名</label>
                        <input type="text" class="form-control" id="validationCustom01" placeholder="请输入缩略名"
                               name="slug" >
                    </div>
                        <div class="form-group col-sm-4">
                            <label for="validationCustom01">所属大类</label>
                                <select class="form-control" id="example-select" name="parent">
                                    <?php
                                    while ($withdrawal = mysqli_fetch_array($withdrawalsResult)) {
                                        ?>
                                        <option value="<?php echo $withdrawal['id'] ?>"><?php echo $withdrawal['name'] ?></option>
                                    <?php
                                    }
                                    ?>
                                </select>
                        </div>
                    
                    
                    <label for="validationCustom01">简介</label>
                        <textarea id="notice" class="form-control" rows="6" name="text" placeholder="请输入简介" ></textarea>
                    <br />
                     
                     <div class="form-group col-sm-4">
                        <label for="validationCustom01">发帖权限</label>
                            <select class="form-control" id="example-select" name="restrict" >
                                 <option value="5">管理员</option>
                                <option value="4">圈主</option>
                                <option value="3">副圈主</option>
                                <option value="2">执行员</option>
                                <option value="1">审核员</option>
                                <option value="0" selected>所有用户</option>
                            </select>
                    </div>
                    
                   <div class="form-group mb-3" id="validationCustom011">  
                        <label>图标  
                            <button type="button" id="uploadButton" class="btn btn-success2 btn-sm btn-rounded right_10"><i class="dripicons-upload"></i> 上传</button>  
                        </label>  
                        <input type="text" class="form-control" id="picLinkInput" style="display: none;" placeholder="图标链接" name="pic" readonly>  
                        <input type="file" id="uploadImage" accept="image/*" style="display: none;"> 
                        <br><span class="dtr-data" id="logo1"><img style="width: 200px;object-fit: cover;margin-top:20px;margin-right:20px;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" id="picLinkInput1" class="spotlight"></span>
                    </div>  
                    <div class="form-group mb-3" id="validationCustom012">  
                        <label>背景图  
                            <button type="button" id="uploadButtonBg" class="btn btn-success2 btn-sm btn-rounded right_10"><i class="dripicons-upload"></i> 上传</button> 
                        </label>  
                        <input type="text" class="form-control" id="picLinkInputBg" style="display: none;" placeholder="背景图链接" name="bg" readonly>  
                        <input type="file" id="uploadImageBg" accept="image/*" style="display: none;">  
                        <br><span class="dtr-data" id="logo2"><img style="width: 315px;height:160px;object-fit: cover;margin-top:20px;margin-right:20px;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" id="picLinkInput2" class="spotlight"></span>
                    </div>  
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom01">排序（越小越靠前）</label>
                        <input type="number" class="form-control" id="validationCustom01" placeholder="请输入排序" value="0"
                               name="order" required>
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-primary" type="submit" id="sectionAddPost">创建</button>
                    </div>
                </form>

            </div> <!-- end card-body-->
        </div> <!-- end card-->
    </div> <!-- end col-->
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->

<script>
    function check() {
        let name = document.getElementsByName('name')[0].value.trim();
        let slug = document.getElementsByName('slug')[0].value.trim();
        let description = document.getElementsByName('text')[0].value.trim();
        let order = document.getElementsByName('order')[0].value.trim();
        let type = document.getElementsByName('type')[0].value.trim();
        let parent = document.getElementsByName('parent')[0].value.trim();
        if (name.length == 0&&type == 'section') {
            alert("名称不能为空");
            return false;
        } else if (slug.length == 0&&type == 'section') {
            alert("缩略名不能为空");
            return false;
        } else if (description.length == 0&&type == 'section') {
            alert("描述不能为空");
            return false;
        } else if (order.length == 0&&type == 'section') {
            alert("排序不能为空");
            return false;
        }
        

    }
    document.addEventListener('DOMContentLoaded', function() {  
    var selectElement = document.getElementById('dynamic-type');  
    var div1 = document.getElementById('div1');  
    var div2 = document.getElementById('div2');  
    var logo1 = document.getElementById('logo1');  
    var logo2 = document.getElementById('logo2');  
  
    div2.style.display = 'none';  
    logo1.style.display = 'none';  
    logo2.style.display = 'none';  
  
    selectElement.addEventListener('change', function() {  
        if (this.value === 'sort') {  
            div1.style.display = 'block'; 
            div2.style.display = 'none'; 
        } else if (this.value === 'section') {  
            div1.style.display = 'none';    
            div2.style.display = 'block'; 
        }  
    });  
    });
    // 获取上传图片按钮和文件上传输入框
var uploadButton = document.getElementById("uploadButton");
var uploadImage = document.getElementById("uploadImage");
var picLinkInput = document.getElementById("picLinkInput");
var picLinkInput1 = document.getElementById("picLinkInput1");
var picLinkInput2 = document.getElementById("picLinkInput2");
var picLinkInputImg1 = document.getElementById("picLinkInput1");
var picLinkInputImg2 = document.getElementById("picLinkInput2");

uploadFiles(uploadButton, uploadImage, picLinkInput, picLinkInputImg1, logo1);

var uploadButtonBg = document.getElementById("uploadButtonBg");
var uploadImageBg = document.getElementById("uploadImageBg");
var picLinkInputBg = document.getElementById("picLinkInputBg");

uploadFiles(uploadButtonBg, uploadImageBg, picLinkInputBg, picLinkInputImg2, logo2);
<?php
include_once 'uploadJs.php';
?>
</script>

<?php
include_once 'Footer.php';
?>

</body>
</html>