<?php
session_start();
?>

<?php
include_once 'connect.php';
$sql = "select * from Sy_icon order by id desc";
$contents = mysqli_query($connect, $sql);
?>

<?php
include_once 'Nav.php';
?>

<link href="/admin/assets/css/vendor/dataTables.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/responsive.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/buttons.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/select.bootstrap4.css" rel="stylesheet" type="text/css"/>
<!-- third party css end -->


<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">图标模块管理<a class="fabu" href="iconAdd.php">
                        <button type="button" class="btn btn-success2 btn-sm btn-rounded right_10">
                            <i class="dripicons-plus"></i> 添加图标
                        </button>
                    </a></h4>
                
                <table id="basic-textlong" class="table dt-responsive nowrap" width="100%">
                    <thead>
                    <tr>
                        <th>id</th>
                        <th>转跳</th>
                        <th>图标</th>
                        <th>限制</th>
                        <th>名称</th>
                        <th style="width: 125px;">操作</th>
                    </tr>
                    </thead>

                    <tbody>
                    <?php
                    while ($articledata = mysqli_fetch_array($contents)) {
                        ?>
                        <tr>
                            <td><?php echo $articledata['id'] ?></td>
                            
                             <td>
                                <?php echo $articledata['link'] ?>
                            </td>
                            <td>
                                
                                    <span class="dtr-data" id="yl"><img style="width: 100px;object-fit: cover;margin-top:20px;margin-right:20px;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" src="<?php echo $articledata['url'] ?>" class="spotlight"></span>
                            
                            </td>
                            <td>
                                 <h5>
                                    <?php if ($articledata['lgof']== 'false') { ?>
                                    <span class="badge badge-success-lighten">无限制</span>
                                    <?php } else { ?>
                                    <span class="badge badge-info-lighten">需登录</span>
                                    <?php }?>
                                </h5>
                                
                            </td>
                            <td>
                                <?php echo $articledata['name'] ?>
                            </td>
                            <td>
                                <a href="iconEdit.php?id=<?php echo $articledata['id']; ?>">
                                <button style="white-space: nowrap;" type="button"
                                        class="btn btn-info btn-rounded">
                                    <i class="dripicons-document-edit"></i> 编辑
                                </button>
                                </a>
                                <a href="javascript:del(<?php echo $articledata['id']; ?>);">
                                    <button style="white-space: nowrap;" type="button"
                                            class="btn btn-danger btn-rounded">
                                        <i class="mdi mdi-delete-empty mr-1"></i>删除
                                    </button>
                                </a>
                            </td>
                        </tr>
                        <?php
                    }
                    ?>
                    </tbody>
                </table>

            </div>  
        </div>  
    </div> 
</div>


<script>
    function del(id) {
        if (confirm('您确认要删除id为' + id + '的图标吗？')) {
            location.href = 'iconDel.php?id=' + id +'&status=one';
        }
    }
</script>


<?php
include_once 'Footer.php';
?>

<!-- third party js -->
<script src="/admin/assets/js/vendor/jquery.dataTables.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.bootstrap4.js"></script>
<script src="/admin/assets/js/vendor/dataTables.responsive.min.js"></script>
<script src="/admin/assets/js/vendor/responsive.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.buttons.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.html5.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.flash.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.print.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.keyTable.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.select.min.js"></script>
<!-- third party js ends -->
<!-- demo app -->
<script src="/admin/assets/js/pages/demo.datatable-init.js"></script>
<!-- end demo js-->


</body>
</html>