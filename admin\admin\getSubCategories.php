<?php
session_start();
?>



<?php
include_once 'connect.php';
// 检查连接是否成功
if ($connect->connect_error) {
    die("连接失败: " . $connect->connect_error);
}

$parentId = $_GET["parentId"];

// 查询下一级分类
$sql = "SELECT * FROM typecho_metas WHERE type = 'category' AND parent = " . $parentId . " ORDER BY mid DESC";
$result = $connect->query($sql);

if ($result->num_rows > 0) {
    echo '<div class="form-group col-sm-4">';
    echo '<label for="validationCustom01">子分类</label>';
    echo '<select class="form-control" id="example-select" name="subcategory" onchange="loadSubSubCategories(this.value)">';
    echo '<option value="">请选择</option>';
    
    while ($row = $result->fetch_assoc()) {
        echo '<option value="' . $row["mid"] . '">' . $row["name"] . '</option>';
    }
    
    echo '</select>';
    echo '</div>';
}

?>