<?php
session_start();
?>
<?php
include_once 'connect.php';
$mid = $_POST['mid'];
$name = $_POST['name'];
$slug = $_POST['slug'];
$type = $_POST['type'];
$parent = $_POST['parent'];
$description = $_POST['description'];
$imgurl = $_POST['imgurl'];
$order = $_POST['order'];
if (empty($imgurl)) {
    $imgurl = NULL;
}
if ($type=='tag') {
    $parent = '0';
}
if (empty($order)) {
    $order = '0';
}
if (empty($description)) {
    $description = NULL;
}
if (empty($parent)) {
    $parent = '0';
}
$file = $_SERVER['PHP_SELF'];


if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    $charu = "UPDATE typecho_metas SET `name`='$name' , `slug`='$slug' ,  `type`='$type' , `parent`='$parent' , `description`='$description' , `imgurl`='$imgurl' , `order` = '$order' WHERE mid = '$mid'";
    $result = mysqli_query($connect, $charu);
    if ($result) {
            echo "<script>alert('修改成功');location.href = 'metaAdmin.php';</script>";
        } else {
            echo "<script>alert('修改失败，缩略名已存在');history.back();</script>";   
        }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
