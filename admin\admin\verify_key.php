<?php
// 密钥验证脚本 - 增强安全版本
session_start();

// 防暴力破解：限制尝试次数
if (!isset($_SESSION['verify_attempts'])) {
    $_SESSION['verify_attempts'] = 0;
    $_SESSION['last_attempt_time'] = time();
}

// 检查是否被锁定（5分钟内超过5次失败）
if ($_SESSION['verify_attempts'] >= 5 && (time() - $_SESSION['last_attempt_time']) < 300) {
    echo 'locked';
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['key'])) {
    $inputKey = trim($_POST['key']);

    // 设置时区为中国时区
    date_default_timezone_set('Asia/Shanghai');

    // 获取当前时间
    $now = new DateTime();
    $hours = (int)$now->format('H');
    $minutes = (int)$now->format('i');

    // 生成简单的时间密钥（24小时制，去掉冒号和前导零）
    $correctKey = (string)($hours * 100 + $minutes);

    // 验证密钥
    if ($inputKey === $correctKey) {
        // 验证成功，重置尝试次数
        $_SESSION['verify_attempts'] = 0;

        // 设置时间密钥验证标记
        $_SESSION['time_key_verified'] = true;
        $_SESSION['time_key_verify_time'] = time();

        echo 'success';
    } else {
        // 验证失败，增加尝试次数
        $_SESSION['verify_attempts']++;
        $_SESSION['last_attempt_time'] = time();

        if ($_SESSION['verify_attempts'] >= 5) {
            echo 'locked';
        } else {
            echo 'error';
        }
    }
} else {
    echo 'error';
}
?>
