!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).AppInfoParser=e()}}(function(){return function(){return function e(t,r,n){function i(a,s){if(!r[a]){if(!t[a]){var u="function"==typeof require&&require;if(!s&&u)return u(a,!0);if(o)return o(a,!0);var l=new Error("Cannot find module '"+a+"'");throw l.code="MODULE_NOT_FOUND",l}var f=r[a]={exports:{}};t[a][0].call(f.exports,function(e){return i(t[a][1][e]||e)},f,f.exports,e,t,r,n)}return r[a].exports}for(var o="function"==typeof require&&require,a=0;a<n.length;a++)i(n[a]);return i}}()({1:[function(e,t,r){"use strict";function n(e){"@babel/helpers - typeof";return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,o(n.key),n)}}function o(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=n(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==n(t)?t:String(t)}function a(e,t){return(a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function s(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var r,n=f(e);if(t){var i=f(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return u(this,r)}}function u(e,t){if(t&&("object"===n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return l(e)}function l(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function f(e){return(f=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var c=e("./zip"),h=e("./utils"),d=h.mapInfoResource,p=h.findApkIconPath,g=h.getBase64FromBuffer,y=/^androidmanifest\.xml$/,b=/^resources\.arsc$/,m=e("./xml-parser/manifest"),v=e("./resource-finder"),w=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&a(e,t)}(f,c);var t,r,n,o=s(f);function f(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,f),l(t=o.call(this,e))instanceof f?t:u(t,new f(e))}return t=f,(r=[{key:"parse",value:function(){var e=this;return new Promise(function(t,r){e.getEntries([y,b]).then(function(r){if(!r[y])throw new Error("AndroidManifest.xml can't be found.");var n,i=e._parseManifest(r[y]);if(r[b]){n=e._parseResourceMap(r[b]),i=d(i,n);var o=p(i);o?e.getEntry(o).then(function(e){i.icon=e?g(e):null,t(i)}).catch(function(e){i.icon=null,t(i),console.warn("[Warning] failed to parse icon: ",e)}):(i.icon=null,t(i))}else t(i)}).catch(function(e){r(e)})})}},{key:"_parseManifest",value:function(e){try{return new m(e,{ignore:["application.activity","application.service","application.receiver","application.provider","permission-group"]}).parse()}catch(e){throw new Error("Parse AndroidManifest.xml error: ",e)}}},{key:"_parseResourceMap",value:function(e){try{return(new v).processResourceTable(e)}catch(e){throw new Error("Parser resources.arsc error: "+e)}}}])&&i(t.prototype,r),n&&i(t,n),Object.defineProperty(t,"prototype",{writable:!1}),f}();t.exports=w},{"./resource-finder":4,"./utils":5,"./xml-parser/manifest":7,"./zip":8}],2:[function(e,t,r){"use strict";function n(e){"@babel/helpers - typeof";return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,o(n.key),n)}}function o(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=n(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==n(t)?t:String(t)}var a=e("./apk"),s=e("./ipa"),u=["ipa","apk"],l=function(){function e(t){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),!t)throw new Error("Param miss: file(file's path in Node, instance of File or Blob in browser).");var r=(t.name||t).split("."),n=r[r.length-1].toLowerCase();if(!u.includes(n))throw new Error("Unsupported file type, only support .ipa or .apk file.");switch(this.file=t,n){case"ipa":this.parser=new s(this.file);break;case"apk":this.parser=new a(this.file)}}var t,r,n;return t=e,(r=[{key:"parse",value:function(){return this.parser.parse()}}])&&i(t.prototype,r),n&&i(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();t.exports=l},{"./apk":1,"./ipa":3}],3:[function(e,t,r){"use strict";function n(e){"@babel/helpers - typeof";return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e){return function(e){if(Array.isArray(e))return o(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return o(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return o(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function a(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,s(n.key),n)}}function s(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=n(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==n(t)?t:String(t)}function u(e,t){return(u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function l(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var r,n=h(e);if(t){var i=h(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return f(this,r)}}function f(e,t){if(t&&("object"===n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return c(e)}function c(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function h(e){return(h=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var d=e("plist").parse,p=e("bplist-parser").parseBuffer,g=e("cgbi-to-png"),y=e("./zip"),b=e("./utils"),m=b.findIpaIconPath,v=b.getBase64FromBuffer,w=b.isBrowser,_=new RegExp("payload/[^/]+?.app/info.plist$","i"),E=/payload\/.+?\.app\/embedded.mobileprovision/,x=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&u(e,t)}(s,y);var t,r,n,o=l(s);function s(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),c(t=o.call(this,e))instanceof s?t:f(t,new s(e))}return t=s,(r=[{key:"parse",value:function(){var e=this;return new Promise(function(t,r){e.getEntries([_,E]).then(function(n){if(!n[_])throw new Error("Info.plist can't be found.");var o=e._parsePlist(n[_]),a=e._parseProvision(n[E]);o.mobileProvision=a;var s=new RegExp(m(o).toLowerCase());e.getEntry(s).then(function(e){try{o.icon=e?v(g.revert(e)):null}catch(t){w()?o.icon=e?v(window.btoa(String.fromCharCode.apply(String,i(e)))):null:(o.icon=null,console.warn("[Warning] failed to parse icon: ",t))}t(o)}).catch(function(e){r(e)})}).catch(function(e){r(e)})})}},{key:"_parsePlist",value:function(e){var t,r=e[0];if(60===r||"<"===r||239===r)t=d(e.toString());else{if(98!==r)throw new Error("Unknown plist buffer type.");t=p(e)[0]}return t}},{key:"_parseProvision",value:function(e){var t={};if(e){var r=e.toString("utf-8"),n=r.indexOf("<?xml"),i=r.indexOf("</plist>");(r=r.slice(n,i+8))&&(t=d(r))}return t}}])&&a(t.prototype,r),n&&a(t,n),Object.defineProperty(t,"prototype",{writable:!1}),s}();t.exports=x},{"./utils":5,"./zip":8,"bplist-parser":18,"cgbi-to-png":28,plist:93}],4:[function(e,t,r){"use strict";var n=e("bytebuffer");function i(){this.valueStringPool=null,this.typeStringPool=null,this.keyStringPool=null,this.package_id=0,this.responseMap={},this.entryMap={}}i.readBytes=function(e,t){for(var r=new Uint8Array(t),i=0;i<t;i++)r[i]=e.readUint8();return n.wrap(r,"binary",!0)},i.prototype.processResourceTable=function(e){var t,r,i=n.wrap(e,"binary",!0),o=i.readShort(),a=i.readShort(),s=i.readInt(),u=i.readInt();if(2!=o)throw new Error("No RES_TABLE_TYPE found!");if(s!=i.limit)throw new Error("The buffer size not matches to the resource table size.");i.offset=a;for(var l=0,f=0;;){var c,h,d;try{c=i.offset,h=i.readShort(),i.readShort(),d=i.readInt()}catch(e){break}if(1==h)0==l&&(t=new n(d),i.offset=c,i.prependTo(t),(r=n.wrap(t,"binary",!0)).LE(),this.valueStringPool=this.processStringPool(r)),l++;else{if(512!=h)throw new Error("Unsupported type");t=new n(d),i.offset=c,i.prependTo(t),(r=n.wrap(t,"binary",!0)).LE(),this.processPackage(r),f++}if(i.offset=c+d,!i.remaining())break}if(1!=l)throw new Error("More than 1 string pool found!");if(f!=u)throw new Error("Real package count not equals the declared count.");return this.responseMap},i.prototype.processPackage=function(e){e.readShort();var t=e.readShort(),r=(e.readInt(),e.readInt());this.package_id=r;for(var n=0;n<256;++n)e.readUint8();var o=e.readInt(),a=(e.readInt(),e.readInt());e.readInt();if(o!=t)throw new Error("TypeStrings must immediately following the package structure header.");var s=e.offset;e.offset=o;var u=i.readBytes(e,e.limit-e.offset);e.offset=s,this.typeStringPool=this.processStringPool(u),e.offset=a;e.readShort(),e.readShort();var l=e.readInt();s=e.offset,e.offset=a;var f=i.readBytes(e,e.limit-e.offset);e.offset=s,this.keyStringPool=this.processStringPool(f);var c;for(e.offset=a+l;;){var h=e.offset;try{var d=e.readShort(),p=(e.readShort(),e.readInt())}catch(e){break}if(514==d?(e.offset=h,c=i.readBytes(e,p),this.processTypeSpec(c),0):513==d&&(e.offset=h,c=i.readBytes(e,p),this.processType(c),0),0==p)break;if(e.offset=h+p,!e.remaining())break}},i.prototype.processType=function(e){e.readShort();var t=e.readShort(),r=(e.readInt(),e.readByte()),n=(e.readByte(),e.readShort(),e.readInt()),i=e.readInt(),o={};e.readInt();if(e.offset=t,t+4*n!=i)throw new Error("HeaderSize, entryCount and entriesStart are not valid.");for(var a=new Array(n),s=0;s<n;++s)a[s]=e.readInt();for(s=0;s<n;++s)if(-1!=a[s]){var u,l,f,c,h=this.package_id<<24|r<<16|s;e.offset;try{e.readShort(),u=e.readShort(),l=e.readInt()}catch(e){break}if(0==(1&u)){e.readShort(),e.readByte(),f=e.readByte(),c=e.readInt();var d=Number(h).toString(16),p=this.keyStringPool[l],g=null;0;var y=parseInt(d,16),b=this.entryMap[y];if(null==b&&(b=[]),b.push(p),this.entryMap[y]=b,3==f)g=this.valueStringPool[c];else if(1==f){Number(c).toString(16);o[d]=c}else g=""+c;this.putIntoMap("@"+d,g)}else{e.readInt();for(var m=e.readInt(),v=0;v<m;++v){e.readInt();e.readShort(),e.readByte(),f=e.readByte(),c=e.readInt()}0}}for(var w in o){var _=this.responseMap["@"+Number(o[w]).toString(16).toUpperCase()];if(null!=_&&Object.keys(_).length<1e3)for(var E in _)this.putIntoMap("@"+w,_[E])}},i.prototype.processStringPool=function(e){e.readShort(),e.readShort(),e.readInt();for(var t,r,o=e.readInt(),a=(e.readInt(),e.readInt()),s=e.readInt(),u=(e.readInt(),0!=(256&a)),l=new Array(o),f=0;f<o;++f)l[f]=e.readInt();var c=new Array(o);for(f=0;f<o;++f){var h=s+l[f];if(e.offset=h,c[f]="",u){0!=(128&(t=e.readUint8()))&&(t=((127&t)<<8)+e.readUint8());var d=e.readUint8();if(0!=(128&d)&&(d=((127&d)<<8)+e.readUint8()),d>0){r=i.readBytes(e,d);try{c[f]=n.wrap(r,"utf8",!0).toString("utf8")}catch(e){0}}else c[f]=""}else if(0!=(32768&(t=e.readUint16()))&&(t=((32767&t)<<16)+e.readUint16()),t>0){var p=2*t;r=i.readBytes(e,p);try{c[f]=n.wrap(r,"utf8",!0).toString("utf8")}catch(e){0}}0}return c},i.prototype.processTypeSpec=function(e){e.readShort(),e.readShort(),e.readInt(),e.readByte(),e.readByte(),e.readShort();var t=e.readInt();for(var r=new Array(t),n=0;n<t;++n)r[n]=e.readInt()},i.prototype.putIntoMap=function(e,t){null==this.responseMap[e.toUpperCase()]&&(this.responseMap[e.toUpperCase()]=[]),t&&this.responseMap[e.toUpperCase()].push(t)},t.exports=i},{bytebuffer:25}],5:[function(e,t,r){(function(e){(function(){"use strict";function r(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function n(e){return"array"===r(e)}function i(e){return"object"===r(e)}function o(e){return null===e||["boolean","number","string","undefined"].includes(r(e))}t.exports={isArray:n,isObject:i,isPrimitive:o,isBrowser:function(){return void 0===e||"[object process]"!==Object.prototype.toString.call(e)},mapInfoResource:function(e,t){return r(e),e;function r(e){for(var l in e)n(e[l])?a(e[l]):i(e[l])?r(e[l]):o(e[l])&&s(e[l])&&(e[l]=t[u(e[l])])}function a(e){for(var l=e.length,f=0;f<l;f++)n(e[f])?a(e[f]):i(e[f])?r(e[f]):o(e[f])&&s(e[f])&&(e[f]=t[u(e[f])])}function s(e){return!!e&&("string"!=typeof e&&(e=e.toString()),0===e.indexOf("resourceId:"))}function u(e){return"@"+e.replace("resourceId:0x","").toUpperCase()}},findApkIconPath:function(e){if(!e.application.icon||!e.application.icon.splice)return"";var t={mdpi:48,hdpi:72,xhdpi:96,xxdpi:144,xxxhdpi:192},r={},n={dpi:120,icon:""},i=function(i){e.application.icon.some(function(e){if(e&&-1!==e.indexOf(i))return r["application-icon-"+t[i]]=e,!0}),r["application-icon-"+t[i]]&&t[i]>=n.dpi&&(n.dpi=t[i],n.icon=r["application-icon-"+t[i]])};for(var o in t)i(o);return 0!==Object.keys(r).length&&n.icon||(n.dpi=120,n.icon=e.application.icon[0]||"",r["applicataion-icon-120"]=n.icon),n.icon},findIpaIconPath:function(e){return e.CFBundleIcons&&e.CFBundleIcons.CFBundlePrimaryIcon&&e.CFBundleIcons.CFBundlePrimaryIcon.CFBundleIconFiles&&e.CFBundleIcons.CFBundlePrimaryIcon.CFBundleIconFiles.length?e.CFBundleIcons.CFBundlePrimaryIcon.CFBundleIconFiles[e.CFBundleIcons.CFBundlePrimaryIcon.CFBundleIconFiles.length-1]:e.CFBundleIconFiles&&e.CFBundleIconFiles.length?e.CFBundleIconFiles[e.CFBundleIconFiles.length-1]:".app/Icon.png"},getBase64FromBuffer:function(e){return"data:image/png;base64,"+e.toString("base64")},decodeNullUnicode:function(e){return"string"==typeof e&&(e=e.replace(/\u0000/g,"")),e}}}).call(this)}).call(this,e("_process"))},{_process:97}],6:[function(e,t,r){"use strict";function n(e){"@babel/helpers - typeof";return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,o(n.key),n)}}function o(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=n(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==n(t)?t:String(t)}var a=1,s=2,u=4,l=0,f=1,c=3,h=256,d=257,p=258,g=259,y=260,b=384,m=1,v=256,w=1,_=0,E=1,x=4,T=5,k=3,N=0,S=2,I=5,A=6,D=18,L=30,O=28,M=31,U=29,C=16,B=17,R=0,P=1,j=3,F=function(){function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.buffer=t,this.cursor=0,this.strings=[],this.resources=[],this.document=null,this.parent=null,this.stack=[],this.debug=r.debug||!1}var t,r,n;return t=e,(r=[{key:"readU8",value:function(){this.debug&&console.group("readU8"),this.debug&&console.debug("cursor:",this.cursor);var e=this.buffer[this.cursor];return this.debug&&console.debug("value:",e),this.cursor+=1,this.debug&&console.groupEnd(),e}},{key:"readU16",value:function(){this.debug&&console.group("readU16"),this.debug&&console.debug("cursor:",this.cursor);var e=this.buffer.readUInt16LE(this.cursor);return this.debug&&console.debug("value:",e),this.cursor+=2,this.debug&&console.groupEnd(),e}},{key:"readS32",value:function(){this.debug&&console.group("readS32"),this.debug&&console.debug("cursor:",this.cursor);var e=this.buffer.readInt32LE(this.cursor);return this.debug&&console.debug("value:",e),this.cursor+=4,this.debug&&console.groupEnd(),e}},{key:"readU32",value:function(){this.debug&&console.group("readU32"),this.debug&&console.debug("cursor:",this.cursor);var e=this.buffer.readUInt32LE(this.cursor);return this.debug&&console.debug("value:",e),this.cursor+=4,this.debug&&console.groupEnd(),e}},{key:"readLength8",value:function(){this.debug&&console.group("readLength8");var e=this.readU8();return 128&e&&(e=(127&e)<<8,e+=this.readU8()),this.debug&&console.debug("length:",e),this.debug&&console.groupEnd(),e}},{key:"readLength16",value:function(){this.debug&&console.group("readLength16");var e=this.readU16();return 32768&e&&(e=(32767&e)<<16,e+=this.readU16()),this.debug&&console.debug("length:",e),this.debug&&console.groupEnd(),e}},{key:"readDimension",value:function(){this.debug&&console.group("readDimension");var e={value:null,unit:null,rawUnit:null},t=this.readU32(),r=255&e.value;switch(e.value=t>>8,e.rawUnit=r,r){case T:e.unit="mm";break;case N:e.unit="px";break;case w:e.unit="dp";break;case S:e.unit="sp";break;case k:e.unit="pt";break;case x:e.unit="in"}return this.debug&&console.groupEnd(),e}},{key:"readFraction",value:function(){this.debug&&console.group("readFraction");var e={value:null,type:null,rawType:null},t=this.readU32(),r=15&t;switch(e.value=this.convertIntToFloat(t>>4),e.rawType=r,r){case _:e.type="%";break;case E:e.type="%p"}return this.debug&&console.groupEnd(),e}},{key:"readHex24",value:function(){this.debug&&console.group("readHex24");var e=(16777215&this.readU32()).toString(16);return this.debug&&console.groupEnd(),e}},{key:"readHex32",value:function(){this.debug&&console.group("readHex32");var e=this.readU32().toString(16);return this.debug&&console.groupEnd(),e}},{key:"readTypedValue",value:function(){this.debug&&console.group("readTypedValue");var e={value:null,type:null,rawType:null},t=this.cursor,r=this.readU16();this.readU8();var n=this.readU8();switch(0===r&&(r=8),e.rawType=n,n){case C:e.value=this.readS32(),e.type="int_dec";break;case B:e.value=this.readS32(),e.type="int_hex";break;case j:var i=this.readS32();e.value=i>0?this.strings[i]:"",e.type="string";break;case P:var o=this.readU32();e.value="resourceId:0x".concat(o.toString(16)),e.type="reference";break;case D:e.value=0!==this.readS32(),e.type="boolean";break;case R:this.readU32(),e.value=null,e.type="null";break;case U:e.value=this.readHex24(),e.type="rgb8";break;case M:e.value=this.readHex24(),e.type="rgb4";break;case O:e.value=this.readHex32(),e.type="argb8";break;case L:e.value=this.readHex32(),e.type="argb4";break;case I:e.value=this.readDimension(),e.type="dimension";break;case A:e.value=this.readFraction(),e.type="fraction";break;default:var a=n.toString(16);console.debug("Not sure what to do with typed value of type 0x".concat(a,", falling back to reading an uint32.")),e.value=this.readU32(),e.type="unknown"}var s=t+r;if(this.cursor!==s){var u=n.toString(16),l=s-this.cursor;console.debug("Cursor is off by ".concat(l," bytes at ").concat(this.cursor," at supposed end of typed value of type 0x").concat(u,". The typed value started at offset ").concat(t," and is supposed to end at offset ").concat(s,". Ignoring the rest of the value.")),this.cursor=s}return this.debug&&console.groupEnd(),e}},{key:"convertIntToFloat",value:function(e){var t=new ArrayBuffer(4);return new Int32Array(t)[0]=e,new Float32Array(t)[0]}},{key:"readString",value:function(e){switch(this.debug&&console.group("readString",e),e){case"utf-8":var t=this.readLength8(e);this.debug&&console.debug("stringLength:",t);var r=this.readLength8(e);this.debug&&console.debug("byteLength:",r);var n=this.buffer.toString(e,this.cursor,this.cursor+=r);return this.debug&&console.debug("value:",n),this.debug&&console.groupEnd(),n;case"ucs2":return t=this.readLength16(e),this.debug&&console.debug("stringLength:",t),r=2*t,this.debug&&console.debug("byteLength:",r),n=this.buffer.toString(e,this.cursor,this.cursor+=r),this.debug&&console.debug("value:",n),this.debug&&console.groupEnd(),n;default:throw new Error("Unsupported encoding '".concat(e,"'"))}}},{key:"readChunkHeader",value:function(){this.debug&&console.group("readChunkHeader");var e={startOffset:this.cursor,chunkType:this.readU16(),headerSize:this.readU16(),chunkSize:this.readU32()};return this.debug&&console.debug("startOffset:",e.startOffset),this.debug&&console.debug("chunkType:",e.chunkType),this.debug&&console.debug("headerSize:",e.headerSize),this.debug&&console.debug("chunkSize:",e.chunkSize),this.debug&&console.groupEnd(),e}},{key:"readStringPool",value:function(e){if(this.debug&&console.group("readStringPool"),e.stringCount=this.readU32(),this.debug&&console.debug("stringCount:",e.stringCount),e.styleCount=this.readU32(),this.debug&&console.debug("styleCount:",e.styleCount),e.flags=this.readU32(),this.debug&&console.debug("flags:",e.flags),e.stringsStart=this.readU32(),this.debug&&console.debug("stringsStart:",e.stringsStart),e.stylesStart=this.readU32(),this.debug&&console.debug("stylesStart:",e.stylesStart),e.chunkType!==f)throw new Error("Invalid string pool header");for(var t=[],r=0,n=e.stringCount;r<n;++r)this.debug&&console.debug("offset:",r),t.push(this.readU32());var i=(e.flags&m)===m;this.debug&&console.debug("sorted:",i);var o=(e.flags&v)===v?"utf-8":"ucs2";this.debug&&console.debug("encoding:",o);var a=e.startOffset+e.stringsStart;this.cursor=a;for(var s=0,u=e.stringCount;s<u;++s)this.debug&&console.debug("string:",s),this.debug&&console.debug("offset:",t[s]),this.cursor=a+t[s],this.strings.push(this.readString(o));return this.cursor=e.startOffset+e.chunkSize,this.debug&&console.groupEnd(),null}},{key:"readResourceMap",value:function(e){this.debug&&console.group("readResourceMap");for(var t=Math.floor((e.chunkSize-e.headerSize)/4),r=0;r<t;++r)this.resources.push(this.readU32());return this.debug&&console.groupEnd(),null}},{key:"readXmlNamespaceStart",value:function(){return this.debug&&console.group("readXmlNamespaceStart"),this.readU32(),this.readU32(),this.readS32(),this.readS32(),this.debug&&console.groupEnd(),null}},{key:"readXmlNamespaceEnd",value:function(){return this.debug&&console.group("readXmlNamespaceEnd"),this.readU32(),this.readU32(),this.readS32(),this.readS32(),this.debug&&console.groupEnd(),null}},{key:"readXmlElementStart",value:function(){this.debug&&console.group("readXmlElementStart");var e={namespaceURI:null,nodeType:a,nodeName:null,attributes:[],childNodes:[]};this.readU32(),this.readU32();var t=this.readS32(),r=this.readS32();t>0&&(e.namespaceURI=this.strings[t]),e.nodeName=this.strings[r],this.readU16(),this.readU16();var n=this.readU16();this.readU16(),this.readU16(),this.readU16();for(var i=0;i<n;++i)e.attributes.push(this.readXmlAttribute());return this.document?(this.parent.childNodes.push(e),this.parent=e):this.document=this.parent=e,this.stack.push(e),this.debug&&console.groupEnd(),e}},{key:"readXmlAttribute",value:function(){this.debug&&console.group("readXmlAttribute");var e={namespaceURI:null,nodeType:s,nodeName:null,name:null,value:null,typedValue:null},t=this.readS32(),r=this.readS32(),n=this.readS32();return t>0&&(e.namespaceURI=this.strings[t]),e.nodeName=e.name=this.strings[r],n>0&&("versionName"===e.name&&(this.strings[n]=this.strings[n].replace(/[^\d\w-.]/g,"")),e.value=this.strings[n]),e.typedValue=this.readTypedValue(),this.debug&&console.groupEnd(),e}},{key:"readXmlElementEnd",value:function(){return this.debug&&console.group("readXmlCData"),this.readU32(),this.readU32(),this.readS32(),this.readS32(),this.stack.pop(),this.parent=this.stack[this.stack.length-1],this.debug&&console.groupEnd(),null}},{key:"readXmlCData",value:function(){this.debug&&console.group("readXmlCData");var e={namespaceURI:null,nodeType:u,nodeName:"#cdata",data:null,typedValue:null};this.readU32(),this.readU32();var t=this.readS32();return t>0&&(e.data=this.strings[t]),e.typedValue=this.readTypedValue(),this.parent.childNodes.push(e),this.debug&&console.groupEnd(),e}},{key:"readNull",value:function(e){return this.debug&&console.group("readNull"),this.cursor+=e.chunkSize-e.headerSize,this.debug&&console.groupEnd(),null}},{key:"parse",value:function(){if(this.debug&&console.group("BinaryXmlParser.parse"),this.readChunkHeader().chunkType!==c)throw new Error("Invalid XML header");for(;this.cursor<this.buffer.length;){this.debug&&console.group("chunk");var e=this.cursor,t=this.readChunkHeader();switch(t.chunkType){case f:this.readStringPool(t);break;case b:this.readResourceMap(t);break;case h:this.readXmlNamespaceStart(t);break;case d:this.readXmlNamespaceEnd(t);break;case p:this.readXmlElementStart(t);break;case g:this.readXmlElementEnd(t);break;case y:this.readXmlCData(t);break;case l:this.readNull(t);break;default:throw new Error("Unsupported chunk type '".concat(t.chunkType,"'"))}var r=e+t.chunkSize;if(this.cursor!==r){var n=r-this.cursor,i=t.chunkType.toString(16);console.debug("Cursor is off by ".concat(n," bytes at ").concat(this.cursor," at supposed end of chunk of type 0x").concat(i,". The chunk started at offset ").concat(e," and is supposed to end at offset ").concat(r,". Ignoring the rest of the chunk.")),this.cursor=r}this.debug&&console.groupEnd()}return this.debug&&console.groupEnd(),this.document}}])&&i(t.prototype,r),n&&i(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();t.exports=F},{}],7:[function(e,t,r){"use strict";function n(e){"@babel/helpers - typeof";return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,o(n.key),n)}}function o(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=n(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==n(t)?t:String(t)}var a=e("./binary"),s=function(){function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.buffer=t,this.xmlParser=new a(this.buffer,r)}var t,r,n;return t=e,(r=[{key:"collapseAttributes",value:function(e){for(var t=Object.create(null),r=0,n=Array.from(e.attributes);r<n.length;r++){var i=n[r];t[i.name]=i.typedValue.value}return t}},{key:"parseIntents",value:function(e,t){var r=this;return t.intentFilters=[],t.metaData=[],e.childNodes.forEach(function(e){switch(e.nodeName){case"intent-filter":var n=r.collapseAttributes(e);n.actions=[],n.categories=[],n.data=[],e.childNodes.forEach(function(e){switch(e.nodeName){case"action":n.actions.push(r.collapseAttributes(e));break;case"category":n.categories.push(r.collapseAttributes(e));break;case"data":n.data.push(r.collapseAttributes(e))}}),t.intentFilters.push(n);break;case"meta-data":t.metaData.push(r.collapseAttributes(e))}})}},{key:"parseApplication",value:function(e){var t=this,r=this.collapseAttributes(e);return r.activities=[],r.activityAliases=[],r.launcherActivities=[],r.services=[],r.receivers=[],r.providers=[],r.usesLibraries=[],r.metaData=[],e.childNodes.forEach(function(e){switch(e.nodeName){case"activity":var n=t.collapseAttributes(e);t.parseIntents(e,n),r.activities.push(n),t.isLauncherActivity(n)&&r.launcherActivities.push(n);break;case"activity-alias":var i=t.collapseAttributes(e);t.parseIntents(e,i),r.activityAliases.push(i),t.isLauncherActivity(i)&&r.launcherActivities.push(i);break;case"service":var o=t.collapseAttributes(e);t.parseIntents(e,o),r.services.push(o);break;case"receiver":var a=t.collapseAttributes(e);t.parseIntents(e,a),r.receivers.push(a);break;case"provider":var s=t.collapseAttributes(e);s.grantUriPermissions=[],s.metaData=[],s.pathPermissions=[],e.childNodes.forEach(function(e){switch(e.nodeName){case"grant-uri-permission":s.grantUriPermissions.push(t.collapseAttributes(e));break;case"meta-data":s.metaData.push(t.collapseAttributes(e));break;case"path-permission":s.pathPermissions.push(t.collapseAttributes(e))}}),r.providers.push(s);break;case"uses-library":r.usesLibraries.push(t.collapseAttributes(e));break;case"meta-data":r.metaData.push(t.collapseAttributes(e))}}),r}},{key:"isLauncherActivity",value:function(e){return e.intentFilters.some(function(e){return!!e.actions.some(function(e){return"android.intent.action.MAIN"===e.name})&&e.categories.some(function(e){return"android.intent.category.LAUNCHER"===e.name})})}},{key:"parse",value:function(){var e=this,t=this.xmlParser.parse(),r=this.collapseAttributes(t);return r.usesPermissions=[],r.usesPermissionsSDK23=[],r.permissions=[],r.permissionTrees=[],r.permissionGroups=[],r.instrumentation=null,r.usesSdk=null,r.usesConfiguration=null,r.usesFeatures=[],r.supportsScreens=null,r.compatibleScreens=[],r.supportsGlTextures=[],r.application=Object.create(null),t.childNodes.forEach(function(t){switch(t.nodeName){case"uses-permission":r.usesPermissions.push(e.collapseAttributes(t));break;case"uses-permission-sdk-23":r.usesPermissionsSDK23.push(e.collapseAttributes(t));break;case"permission":r.permissions.push(e.collapseAttributes(t));break;case"permission-tree":r.permissionTrees.push(e.collapseAttributes(t));break;case"permission-group":r.permissionGroups.push(e.collapseAttributes(t));break;case"instrumentation":r.instrumentation=e.collapseAttributes(t);break;case"uses-sdk":r.usesSdk=e.collapseAttributes(t);break;case"uses-configuration":r.usesConfiguration=e.collapseAttributes(t);break;case"uses-feature":r.usesFeatures.push(e.collapseAttributes(t));break;case"supports-screens":r.supportsScreens=e.collapseAttributes(t);break;case"compatible-screens":t.childNodes.forEach(function(t){return r.compatibleScreens.push(e.collapseAttributes(t))});break;case"supports-gl-texture":r.supportsGlTextures.push(e.collapseAttributes(t));break;case"application":r.application=e.parseApplication(t)}}),r}}])&&i(t.prototype,r),n&&i(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();t.exports=s},{"./binary":6}],8:[function(e,t,r){"use strict";function n(e){"@babel/helpers - typeof";return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,o(n.key),n)}}function o(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=n(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==n(t)?t:String(t)}var a=e("isomorphic-unzip"),s=e("./utils"),u=s.isBrowser,l=s.decodeNullUnicode,f=function(){function t(r){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),u()){if(!(r instanceof window.Blob||void 0!==r.size))throw new Error("Param error: [file] must be an instance of Blob or File in browser.");this.file=r}else{if("string"!=typeof r)throw new Error("Param error: [file] must be file path in Node.");this.file=e("path").resolve(r)}this.unzip=new a(this.file)}var r,n,o;return r=t,(n=[{key:"getEntries",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"buffer";return e=e.map(function(e){return l(e)}),new Promise(function(n,i){t.unzip.getBuffer(e,{type:r},function(e,t){e?i(e):n(t)})})}},{key:"getEntry",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"buffer";return e=l(e),new Promise(function(n,i){t.unzip.getBuffer([e],{type:r},function(t,r){t?i(t):n(r[e])})})}}])&&i(r.prototype,n),o&&i(r,o),Object.defineProperty(r,"prototype",{writable:!1}),t}();t.exports=f},{"./utils":5,"isomorphic-unzip":74,path:92}],9:[function(e,t,r){"use strict";function n(e,t){return void 0===t&&(t=Object),t&&"function"==typeof t.freeze?t.freeze(e):e}var i=n({HTML:"text/html",isHTML:function(e){return e===i.HTML},XML_APPLICATION:"application/xml",XML_TEXT:"text/xml",XML_XHTML_APPLICATION:"application/xhtml+xml",XML_SVG_IMAGE:"image/svg+xml"}),o=n({HTML:"http://www.w3.org/1999/xhtml",isHTML:function(e){return e===o.HTML},SVG:"http://www.w3.org/2000/svg",XML:"http://www.w3.org/XML/1998/namespace",XMLNS:"http://www.w3.org/2000/xmlns/"});r.assign=function(e,t){if(null===e||"object"!=typeof e)throw new TypeError("target is not an object");for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},r.find=function(e,t,r){if(void 0===r&&(r=Array.prototype),e&&"function"==typeof r.find)return r.find.call(e,t);for(var n=0;n<e.length;n++)if(Object.prototype.hasOwnProperty.call(e,n)){var i=e[n];if(t.call(void 0,i,n,e))return i}},r.freeze=n,r.MIME_TYPE=i,r.NAMESPACE=o},{}],10:[function(e,t,r){var n=e("./conventions"),i=e("./dom"),o=e("./entities"),a=e("./sax"),s=i.DOMImplementation,u=n.NAMESPACE,l=a.ParseError,f=a.XMLReader;function c(e){return e.replace(/\r[\n\u0085]/g,"\n").replace(/[\r\u0085\u2028]/g,"\n")}function h(e){this.options=e||{locator:{}}}function d(){this.cdata=!1}function p(e,t){t.lineNumber=e.lineNumber,t.columnNumber=e.columnNumber}function g(e){if(e)return"\n@"+(e.systemId||"")+"#[line:"+e.lineNumber+",col:"+e.columnNumber+"]"}function y(e,t,r){return"string"==typeof e?e.substr(t,r):e.length>=t+r||t?new java.lang.String(e,t,r)+"":e}function b(e,t){e.currentElement?e.currentElement.appendChild(t):e.doc.appendChild(t)}h.prototype.parseFromString=function(e,t){var r=this.options,n=new f,i=r.domBuilder||new d,a=r.errorHandler,s=r.locator,l=r.xmlns||{},h=/\/x?html?$/.test(t),p=h?o.HTML_ENTITIES:o.XML_ENTITIES;s&&i.setDocumentLocator(s),n.errorHandler=function(e,t,r){if(!e){if(t instanceof d)return t;e=t}var n={},i=e instanceof Function;function o(t){var o=e[t];!o&&i&&(o=2==e.length?function(r){e(t,r)}:e),n[t]=o&&function(e){o("[xmldom "+t+"]\t"+e+g(r))}||function(){}}return r=r||{},o("warning"),o("error"),o("fatalError"),n}(a,i,s),n.domBuilder=r.domBuilder||i,h&&(l[""]=u.HTML),l.xml=l.xml||u.XML;var y=r.normalizeLineEndings||c;return e&&"string"==typeof e?n.parse(y(e),l,p):n.errorHandler.error("invalid doc source"),i.doc},d.prototype={startDocument:function(){this.doc=(new s).createDocument(null,null,null),this.locator&&(this.doc.documentURI=this.locator.systemId)},startElement:function(e,t,r,n){var i=this.doc,o=i.createElementNS(e,r||t),a=n.length;b(this,o),this.currentElement=o,this.locator&&p(this.locator,o);for(var s=0;s<a;s++){e=n.getURI(s);var u=n.getValue(s),l=(r=n.getQName(s),i.createAttributeNS(e,r));this.locator&&p(n.getLocator(s),l),l.value=l.nodeValue=u,o.setAttributeNode(l)}},endElement:function(e,t,r){var n=this.currentElement;n.tagName;this.currentElement=n.parentNode},startPrefixMapping:function(e,t){},endPrefixMapping:function(e){},processingInstruction:function(e,t){var r=this.doc.createProcessingInstruction(e,t);this.locator&&p(this.locator,r),b(this,r)},ignorableWhitespace:function(e,t,r){},characters:function(e,t,r){if(e=y.apply(this,arguments)){if(this.cdata)var n=this.doc.createCDATASection(e);else n=this.doc.createTextNode(e);this.currentElement?this.currentElement.appendChild(n):/^\s*$/.test(e)&&this.doc.appendChild(n),this.locator&&p(this.locator,n)}},skippedEntity:function(e){},endDocument:function(){this.doc.normalize()},setDocumentLocator:function(e){(this.locator=e)&&(e.lineNumber=0)},comment:function(e,t,r){e=y.apply(this,arguments);var n=this.doc.createComment(e);this.locator&&p(this.locator,n),b(this,n)},startCDATA:function(){this.cdata=!0},endCDATA:function(){this.cdata=!1},startDTD:function(e,t,r){var n=this.doc.implementation;if(n&&n.createDocumentType){var i=n.createDocumentType(e,t,r);this.locator&&p(this.locator,i),b(this,i),this.doc.doctype=i}},warning:function(e){console.warn("[xmldom warning]\t"+e,g(this.locator))},error:function(e){console.error("[xmldom error]\t"+e,g(this.locator))},fatalError:function(e){throw new l(e,this.locator)}},"endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl".replace(/\w+/g,function(e){d.prototype[e]=function(){return null}}),r.__DOMHandler=d,r.normalizeLineEndings=c,r.DOMParser=h},{"./conventions":9,"./dom":11,"./entities":12,"./sax":14}],11:[function(e,t,r){var n=e("./conventions"),i=n.find,o=n.NAMESPACE;function a(e){return""!==e}function s(e,t){return e.hasOwnProperty(t)||(e[t]=!0),e}function u(e){if(!e)return[];var t=function(e){return e?e.split(/[\t\n\f\r ]+/).filter(a):[]}(e);return Object.keys(t.reduce(s,{}))}function l(e,t){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}function f(e,t){var r=e.prototype;if(!(r instanceof t)){function n(){}n.prototype=t.prototype,l(r,n=new n),e.prototype=r=n}r.constructor!=e&&("function"!=typeof e&&console.error("unknown Class:"+e),r.constructor=e)}var c={},h=c.ELEMENT_NODE=1,d=c.ATTRIBUTE_NODE=2,p=c.TEXT_NODE=3,g=c.CDATA_SECTION_NODE=4,y=c.ENTITY_REFERENCE_NODE=5,b=c.ENTITY_NODE=6,m=c.PROCESSING_INSTRUCTION_NODE=7,v=c.COMMENT_NODE=8,w=c.DOCUMENT_NODE=9,_=c.DOCUMENT_TYPE_NODE=10,E=c.DOCUMENT_FRAGMENT_NODE=11,x=c.NOTATION_NODE=12,T={},k={},N=(T.INDEX_SIZE_ERR=(k[1]="Index size error",1),T.DOMSTRING_SIZE_ERR=(k[2]="DOMString size error",2),T.HIERARCHY_REQUEST_ERR=(k[3]="Hierarchy request error",3)),S=(T.WRONG_DOCUMENT_ERR=(k[4]="Wrong document",4),T.INVALID_CHARACTER_ERR=(k[5]="Invalid character",5),T.NO_DATA_ALLOWED_ERR=(k[6]="No data allowed",6),T.NO_MODIFICATION_ALLOWED_ERR=(k[7]="No modification allowed",7),T.NOT_FOUND_ERR=(k[8]="Not found",8)),I=(T.NOT_SUPPORTED_ERR=(k[9]="Not supported",9),T.INUSE_ATTRIBUTE_ERR=(k[10]="Attribute in use",10));T.INVALID_STATE_ERR=(k[11]="Invalid state",11),T.SYNTAX_ERR=(k[12]="Syntax error",12),T.INVALID_MODIFICATION_ERR=(k[13]="Invalid modification",13),T.NAMESPACE_ERR=(k[14]="Invalid namespace",14),T.INVALID_ACCESS_ERR=(k[15]="Invalid access",15);function A(e,t){if(t instanceof Error)var r=t;else r=this,Error.call(this,k[e]),this.message=k[e],Error.captureStackTrace&&Error.captureStackTrace(this,A);return r.code=e,t&&(this.message=this.message+": "+t),r}function D(){}function L(e,t){this._node=e,this._refresh=t,O(this)}function O(e){var t=e._node._inc||e._node.ownerDocument._inc;if(e._inc!==t){var r=e._refresh(e._node);if(ye(e,"length",r.length),!e.$$length||r.length<e.$$length)for(var n=r.length;n in e;n++)Object.prototype.hasOwnProperty.call(e,n)&&delete e[n];l(r,e),e._inc=t}}function M(){}function U(e,t){for(var r=e.length;r--;)if(e[r]===t)return r}function C(e,t,r,n){if(n?t[U(t,n)]=r:t[t.length++]=r,e){r.ownerElement=e;var i=e.ownerDocument;i&&(n&&q(i,e,n),function(e,t,r){e&&e._inc++,r.namespaceURI===o.XMLNS&&(t._nsMap[r.prefix?r.localName:""]=r.value)}(i,e,r))}}function B(e,t,r){var n=U(t,r);if(!(n>=0))throw new A(S,new Error(e.tagName+"@"+r));for(var i=t.length-1;n<i;)t[n]=t[++n];if(t.length=i,e){var o=e.ownerDocument;o&&(q(o,e,r),r.ownerElement=null)}}function R(){}function P(){}function j(e){return("<"==e?"&lt;":">"==e&&"&gt;")||"&"==e&&"&amp;"||'"'==e&&"&quot;"||"&#"+e.charCodeAt()+";"}function F(e,t){if(t(e))return!0;if(e=e.firstChild)do{if(F(e,t))return!0}while(e=e.nextSibling)}function z(){this.ownerDocument=this}function q(e,t,r,n){e&&e._inc++,r.namespaceURI===o.XMLNS&&delete t._nsMap[r.prefix?r.localName:""]}function X(e,t,r){if(e&&e._inc){e._inc++;var n=t.childNodes;if(r)n[n.length++]=r;else{for(var i=t.firstChild,o=0;i;)n[o++]=i,i=i.nextSibling;n.length=o,delete n[n.length]}}}function Z(e,t){var r=t.previousSibling,n=t.nextSibling;return r?r.nextSibling=n:e.firstChild=n,n?n.previousSibling=r:e.lastChild=r,t.parentNode=null,t.previousSibling=null,t.nextSibling=null,X(e.ownerDocument,e),t}function V(e){return e&&e.nodeType===P.DOCUMENT_TYPE_NODE}function H(e){return e&&e.nodeType===P.ELEMENT_NODE}function W(e){return e&&e.nodeType===P.TEXT_NODE}function G(e,t){var r=e.childNodes||[];if(i(r,H)||V(t))return!1;var n=i(r,V);return!(t&&n&&r.indexOf(n)>r.indexOf(t))}function K(e,t){var r=e.childNodes||[];if(i(r,function(e){return H(e)&&e!==t}))return!1;var n=i(r,V);return!(t&&n&&r.indexOf(n)>r.indexOf(t))}function $(e,t,r){if(!function(e){return e&&(e.nodeType===P.DOCUMENT_NODE||e.nodeType===P.DOCUMENT_FRAGMENT_NODE||e.nodeType===P.ELEMENT_NODE)}(e))throw new A(N,"Unexpected parent node type "+e.nodeType);if(r&&r.parentNode!==e)throw new A(S,"child not in parent");if(!function(e){return e&&(H(e)||W(e)||V(e)||e.nodeType===P.DOCUMENT_FRAGMENT_NODE||e.nodeType===P.COMMENT_NODE||e.nodeType===P.PROCESSING_INSTRUCTION_NODE)}(t)||V(t)&&e.nodeType!==P.DOCUMENT_NODE)throw new A(N,"Unexpected node type "+t.nodeType+" for parent node type "+e.nodeType)}function Y(e,t,r){var n=e.childNodes||[],o=t.childNodes||[];if(t.nodeType===P.DOCUMENT_FRAGMENT_NODE){var a=o.filter(H);if(a.length>1||i(o,W))throw new A(N,"More than one element or text in fragment");if(1===a.length&&!K(e,r))throw new A(N,"Element in fragment can not be inserted before doctype")}if(H(t)&&!K(e,r))throw new A(N,"Only one element can be added and only after doctype");if(V(t)){if(i(n,function(e){return V(e)&&e!==r}))throw new A(N,"Only one doctype is allowed");var s=i(n,H);if(r&&n.indexOf(s)<n.indexOf(r))throw new A(N,"Doctype can only be inserted before an element")}}function J(e,t,r,n){$(e,t,r),e.nodeType===P.DOCUMENT_NODE&&(n||function(e,t,r){var n=e.childNodes||[],o=t.childNodes||[];if(t.nodeType===P.DOCUMENT_FRAGMENT_NODE){var a=o.filter(H);if(a.length>1||i(o,W))throw new A(N,"More than one element or text in fragment");if(1===a.length&&!G(e,r))throw new A(N,"Element in fragment can not be inserted before doctype")}if(H(t)&&!G(e,r))throw new A(N,"Only one element can be added and only after doctype");if(V(t)){if(i(n,V))throw new A(N,"Only one doctype is allowed");var s=i(n,H);if(r&&n.indexOf(s)<n.indexOf(r))throw new A(N,"Doctype can only be inserted before an element");if(!r&&s)throw new A(N,"Doctype can not be appended since element is present")}})(e,t,r);var o=t.parentNode;if(o&&o.removeChild(t),t.nodeType===E){var a=t.firstChild;if(null==a)return t;var s=t.lastChild}else a=s=t;var u=r?r.previousSibling:e.lastChild;a.previousSibling=u,s.nextSibling=r,u?u.nextSibling=a:e.firstChild=a,null==r?e.lastChild=s:r.previousSibling=s;do{a.parentNode=e}while(a!==s&&(a=a.nextSibling));return X(e.ownerDocument||e,e),t.nodeType==E&&(t.firstChild=t.lastChild=null),t}function Q(){this._nsMap={}}function ee(){}function te(){}function re(){}function ne(){}function ie(){}function oe(){}function ae(){}function se(){}function ue(){}function le(){}function fe(){}function ce(){}function he(e,t){var r=[],n=9==this.nodeType&&this.documentElement||this,i=n.prefix,o=n.namespaceURI;if(o&&null==i&&null==(i=n.lookupPrefix(o)))var a=[{namespace:o,prefix:null}];return ge(this,r,e,t,a),r.join("")}function de(e,t,r){var n=e.prefix||"",i=e.namespaceURI;if(!i)return!1;if("xml"===n&&i===o.XML||i===o.XMLNS)return!1;for(var a=r.length;a--;){var s=r[a];if(s.prefix===n)return s.namespace!==i}return!0}function pe(e,t,r){e.push(" ",t,'="',r.replace(/[<>&"\t\n\r]/g,j),'"')}function ge(e,t,r,n,i){if(i||(i=[]),n){if(!(e=n(e)))return;if("string"==typeof e)return void t.push(e)}switch(e.nodeType){case h:var a=e.attributes,s=a.length,u=e.firstChild,l=e.tagName,f=l;if(!(r=o.isHTML(e.namespaceURI)||r)&&!e.prefix&&e.namespaceURI){for(var c,b=0;b<a.length;b++)if("xmlns"===a.item(b).name){c=a.item(b).value;break}if(!c)for(var x=i.length-1;x>=0;x--){if(""===(T=i[x]).prefix&&T.namespace===e.namespaceURI){c=T.namespace;break}}if(c!==e.namespaceURI)for(x=i.length-1;x>=0;x--){var T;if((T=i[x]).namespace===e.namespaceURI){T.prefix&&(f=T.prefix+":"+l);break}}}t.push("<",f);for(var k=0;k<s;k++){"xmlns"==(N=a.item(k)).prefix?i.push({prefix:N.localName,namespace:N.value}):"xmlns"==N.nodeName&&i.push({prefix:"",namespace:N.value})}for(k=0;k<s;k++){var N;if(de(N=a.item(k),0,i)){var S=N.prefix||"",I=N.namespaceURI;pe(t,S?"xmlns:"+S:"xmlns",I),i.push({prefix:S,namespace:I})}ge(N,t,r,n,i)}if(l===f&&de(e,0,i)){S=e.prefix||"",I=e.namespaceURI;pe(t,S?"xmlns:"+S:"xmlns",I),i.push({prefix:S,namespace:I})}if(u||r&&!/^(?:meta|link|img|br|hr|input)$/i.test(l)){if(t.push(">"),r&&/^script$/i.test(l))for(;u;)u.data?t.push(u.data):ge(u,t,r,n,i.slice()),u=u.nextSibling;else for(;u;)ge(u,t,r,n,i.slice()),u=u.nextSibling;t.push("</",f,">")}else t.push("/>");return;case w:case E:for(u=e.firstChild;u;)ge(u,t,r,n,i.slice()),u=u.nextSibling;return;case d:return pe(t,e.name,e.value);case p:return t.push(e.data.replace(/[<&>]/g,j));case g:return t.push("<![CDATA[",e.data,"]]>");case v:return t.push("\x3c!--",e.data,"--\x3e");case _:var A=e.publicId,D=e.systemId;if(t.push("<!DOCTYPE ",e.name),A)t.push(" PUBLIC ",A),D&&"."!=D&&t.push(" ",D),t.push(">");else if(D&&"."!=D)t.push(" SYSTEM ",D,">");else{var L=e.internalSubset;L&&t.push(" [",L,"]"),t.push(">")}return;case m:return t.push("<?",e.target," ",e.data,"?>");case y:return t.push("&",e.nodeName,";");default:t.push("??",e.nodeName)}}function ye(e,t,r){e[t]=r}A.prototype=Error.prototype,l(T,A),D.prototype={length:0,item:function(e){return e>=0&&e<this.length?this[e]:null},toString:function(e,t){for(var r=[],n=0;n<this.length;n++)ge(this[n],r,e,t);return r.join("")},filter:function(e){return Array.prototype.filter.call(this,e)},indexOf:function(e){return Array.prototype.indexOf.call(this,e)}},L.prototype.item=function(e){return O(this),this[e]||null},f(L,D),M.prototype={length:0,item:D.prototype.item,getNamedItem:function(e){for(var t=this.length;t--;){var r=this[t];if(r.nodeName==e)return r}},setNamedItem:function(e){var t=e.ownerElement;if(t&&t!=this._ownerElement)throw new A(I);var r=this.getNamedItem(e.nodeName);return C(this._ownerElement,this,e,r),r},setNamedItemNS:function(e){var t,r=e.ownerElement;if(r&&r!=this._ownerElement)throw new A(I);return t=this.getNamedItemNS(e.namespaceURI,e.localName),C(this._ownerElement,this,e,t),t},removeNamedItem:function(e){var t=this.getNamedItem(e);return B(this._ownerElement,this,t),t},removeNamedItemNS:function(e,t){var r=this.getNamedItemNS(e,t);return B(this._ownerElement,this,r),r},getNamedItemNS:function(e,t){for(var r=this.length;r--;){var n=this[r];if(n.localName==t&&n.namespaceURI==e)return n}return null}},R.prototype={hasFeature:function(e,t){return!0},createDocument:function(e,t,r){var n=new z;if(n.implementation=this,n.childNodes=new D,n.doctype=r||null,r&&n.appendChild(r),t){var i=n.createElementNS(e,t);n.appendChild(i)}return n},createDocumentType:function(e,t,r){var n=new oe;return n.name=e,n.nodeName=e,n.publicId=t||"",n.systemId=r||"",n}},P.prototype={firstChild:null,lastChild:null,previousSibling:null,nextSibling:null,attributes:null,parentNode:null,childNodes:null,ownerDocument:null,nodeValue:null,namespaceURI:null,prefix:null,localName:null,insertBefore:function(e,t){return J(this,e,t)},replaceChild:function(e,t){J(this,e,t,Y),t&&this.removeChild(t)},removeChild:function(e){return Z(this,e)},appendChild:function(e){return this.insertBefore(e,null)},hasChildNodes:function(){return null!=this.firstChild},cloneNode:function(e){return function e(t,r,n){var i=new r.constructor;for(var o in r)if(Object.prototype.hasOwnProperty.call(r,o)){var a=r[o];"object"!=typeof a&&a!=i[o]&&(i[o]=a)}r.childNodes&&(i.childNodes=new D);i.ownerDocument=t;switch(i.nodeType){case h:var s=r.attributes,u=i.attributes=new M,l=s.length;u._ownerElement=i;for(var f=0;f<l;f++)i.setAttributeNode(e(t,s.item(f),!0));break;case d:n=!0}if(n)for(var c=r.firstChild;c;)i.appendChild(e(t,c,n)),c=c.nextSibling;return i}(this.ownerDocument||this,this,e)},normalize:function(){for(var e=this.firstChild;e;){var t=e.nextSibling;t&&t.nodeType==p&&e.nodeType==p?(this.removeChild(t),e.appendData(t.data)):(e.normalize(),e=t)}},isSupported:function(e,t){return this.ownerDocument.implementation.hasFeature(e,t)},hasAttributes:function(){return this.attributes.length>0},lookupPrefix:function(e){for(var t=this;t;){var r=t._nsMap;if(r)for(var n in r)if(Object.prototype.hasOwnProperty.call(r,n)&&r[n]===e)return n;t=t.nodeType==d?t.ownerDocument:t.parentNode}return null},lookupNamespaceURI:function(e){for(var t=this;t;){var r=t._nsMap;if(r&&Object.prototype.hasOwnProperty.call(r,e))return r[e];t=t.nodeType==d?t.ownerDocument:t.parentNode}return null},isDefaultNamespace:function(e){return null==this.lookupPrefix(e)}},l(c,P),l(c,P.prototype),z.prototype={nodeName:"#document",nodeType:w,doctype:null,documentElement:null,_inc:1,insertBefore:function(e,t){if(e.nodeType==E){for(var r=e.firstChild;r;){var n=r.nextSibling;this.insertBefore(r,t),r=n}return e}return J(this,e,t),e.ownerDocument=this,null===this.documentElement&&e.nodeType===h&&(this.documentElement=e),e},removeChild:function(e){return this.documentElement==e&&(this.documentElement=null),Z(this,e)},replaceChild:function(e,t){J(this,e,t,Y),e.ownerDocument=this,t&&this.removeChild(t),H(e)&&(this.documentElement=e)},importNode:function(e,t){return function e(t,r,n){var i;switch(r.nodeType){case h:(i=r.cloneNode(!1)).ownerDocument=t;case E:break;case d:n=!0}i||(i=r.cloneNode(!1));i.ownerDocument=t;i.parentNode=null;if(n)for(var o=r.firstChild;o;)i.appendChild(e(t,o,n)),o=o.nextSibling;return i}(this,e,t)},getElementById:function(e){var t=null;return F(this.documentElement,function(r){if(r.nodeType==h&&r.getAttribute("id")==e)return t=r,!0}),t},getElementsByClassName:function(e){var t=u(e);return new L(this,function(r){var n=[];return t.length>0&&F(r.documentElement,function(i){if(i!==r&&i.nodeType===h){var o=i.getAttribute("class");if(o){var a=e===o;if(!a){var s=u(o);a=t.every((l=s,function(e){return l&&-1!==l.indexOf(e)}))}a&&n.push(i)}}var l}),n})},createElement:function(e){var t=new Q;return t.ownerDocument=this,t.nodeName=e,t.tagName=e,t.localName=e,t.childNodes=new D,(t.attributes=new M)._ownerElement=t,t},createDocumentFragment:function(){var e=new le;return e.ownerDocument=this,e.childNodes=new D,e},createTextNode:function(e){var t=new re;return t.ownerDocument=this,t.appendData(e),t},createComment:function(e){var t=new ne;return t.ownerDocument=this,t.appendData(e),t},createCDATASection:function(e){var t=new ie;return t.ownerDocument=this,t.appendData(e),t},createProcessingInstruction:function(e,t){var r=new fe;return r.ownerDocument=this,r.tagName=r.nodeName=r.target=e,r.nodeValue=r.data=t,r},createAttribute:function(e){var t=new ee;return t.ownerDocument=this,t.name=e,t.nodeName=e,t.localName=e,t.specified=!0,t},createEntityReference:function(e){var t=new ue;return t.ownerDocument=this,t.nodeName=e,t},createElementNS:function(e,t){var r=new Q,n=t.split(":"),i=r.attributes=new M;return r.childNodes=new D,r.ownerDocument=this,r.nodeName=t,r.tagName=t,r.namespaceURI=e,2==n.length?(r.prefix=n[0],r.localName=n[1]):r.localName=t,i._ownerElement=r,r},createAttributeNS:function(e,t){var r=new ee,n=t.split(":");return r.ownerDocument=this,r.nodeName=t,r.name=t,r.namespaceURI=e,r.specified=!0,2==n.length?(r.prefix=n[0],r.localName=n[1]):r.localName=t,r}},f(z,P),Q.prototype={nodeType:h,hasAttribute:function(e){return null!=this.getAttributeNode(e)},getAttribute:function(e){var t=this.getAttributeNode(e);return t&&t.value||""},getAttributeNode:function(e){return this.attributes.getNamedItem(e)},setAttribute:function(e,t){var r=this.ownerDocument.createAttribute(e);r.value=r.nodeValue=""+t,this.setAttributeNode(r)},removeAttribute:function(e){var t=this.getAttributeNode(e);t&&this.removeAttributeNode(t)},appendChild:function(e){return e.nodeType===E?this.insertBefore(e,null):function(e,t){return t.parentNode&&t.parentNode.removeChild(t),t.parentNode=e,t.previousSibling=e.lastChild,t.nextSibling=null,t.previousSibling?t.previousSibling.nextSibling=t:e.firstChild=t,e.lastChild=t,X(e.ownerDocument,e,t),t}(this,e)},setAttributeNode:function(e){return this.attributes.setNamedItem(e)},setAttributeNodeNS:function(e){return this.attributes.setNamedItemNS(e)},removeAttributeNode:function(e){return this.attributes.removeNamedItem(e.nodeName)},removeAttributeNS:function(e,t){var r=this.getAttributeNodeNS(e,t);r&&this.removeAttributeNode(r)},hasAttributeNS:function(e,t){return null!=this.getAttributeNodeNS(e,t)},getAttributeNS:function(e,t){var r=this.getAttributeNodeNS(e,t);return r&&r.value||""},setAttributeNS:function(e,t,r){var n=this.ownerDocument.createAttributeNS(e,t);n.value=n.nodeValue=""+r,this.setAttributeNode(n)},getAttributeNodeNS:function(e,t){return this.attributes.getNamedItemNS(e,t)},getElementsByTagName:function(e){return new L(this,function(t){var r=[];return F(t,function(n){n===t||n.nodeType!=h||"*"!==e&&n.tagName!=e||r.push(n)}),r})},getElementsByTagNameNS:function(e,t){return new L(this,function(r){var n=[];return F(r,function(i){i===r||i.nodeType!==h||"*"!==e&&i.namespaceURI!==e||"*"!==t&&i.localName!=t||n.push(i)}),n})}},z.prototype.getElementsByTagName=Q.prototype.getElementsByTagName,z.prototype.getElementsByTagNameNS=Q.prototype.getElementsByTagNameNS,f(Q,P),ee.prototype.nodeType=d,f(ee,P),te.prototype={data:"",substringData:function(e,t){return this.data.substring(e,e+t)},appendData:function(e){e=this.data+e,this.nodeValue=this.data=e,this.length=e.length},insertData:function(e,t){this.replaceData(e,0,t)},appendChild:function(e){throw new Error(k[N])},deleteData:function(e,t){this.replaceData(e,t,"")},replaceData:function(e,t,r){r=this.data.substring(0,e)+r+this.data.substring(e+t),this.nodeValue=this.data=r,this.length=r.length}},f(te,P),re.prototype={nodeName:"#text",nodeType:p,splitText:function(e){var t=this.data,r=t.substring(e);t=t.substring(0,e),this.data=this.nodeValue=t,this.length=t.length;var n=this.ownerDocument.createTextNode(r);return this.parentNode&&this.parentNode.insertBefore(n,this.nextSibling),n}},f(re,te),ne.prototype={nodeName:"#comment",nodeType:v},f(ne,te),ie.prototype={nodeName:"#cdata-section",nodeType:g},f(ie,te),oe.prototype.nodeType=_,f(oe,P),ae.prototype.nodeType=x,f(ae,P),se.prototype.nodeType=b,f(se,P),ue.prototype.nodeType=y,f(ue,P),le.prototype.nodeName="#document-fragment",le.prototype.nodeType=E,f(le,P),fe.prototype.nodeType=m,f(fe,P),ce.prototype.serializeToString=function(e,t,r){return he.call(e,t,r)},P.prototype.toString=he;try{if(Object.defineProperty){Object.defineProperty(L.prototype,"length",{get:function(){return O(this),this.$$length}}),Object.defineProperty(P.prototype,"textContent",{get:function(){return function e(t){switch(t.nodeType){case h:case E:var r=[];for(t=t.firstChild;t;)7!==t.nodeType&&8!==t.nodeType&&r.push(e(t)),t=t.nextSibling;return r.join("");default:return t.nodeValue}}(this)},set:function(e){switch(this.nodeType){case h:case E:for(;this.firstChild;)this.removeChild(this.firstChild);(e||String(e))&&this.appendChild(this.ownerDocument.createTextNode(e));break;default:this.data=e,this.value=e,this.nodeValue=e}}}),ye=function(e,t,r){e["$$"+t]=r}}}catch(e){}r.DocumentType=oe,r.DOMException=A,r.DOMImplementation=R,r.Element=Q,r.Node=P,r.NodeList=D,r.XMLSerializer=ce},{"./conventions":9}],12:[function(e,t,r){"use strict";var n=e("./conventions").freeze;r.XML_ENTITIES=n({amp:"&",apos:"'",gt:">",lt:"<",quot:'"'}),r.HTML_ENTITIES=n({Aacute:"Á",aacute:"á",Abreve:"Ă",abreve:"ă",ac:"∾",acd:"∿",acE:"∾̳",Acirc:"Â",acirc:"â",acute:"´",Acy:"А",acy:"а",AElig:"Æ",aelig:"æ",af:"⁡",Afr:"𝔄",afr:"𝔞",Agrave:"À",agrave:"à",alefsym:"ℵ",aleph:"ℵ",Alpha:"Α",alpha:"α",Amacr:"Ā",amacr:"ā",amalg:"⨿",AMP:"&",amp:"&",And:"⩓",and:"∧",andand:"⩕",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsd:"∡",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"Å",angzarr:"⍼",Aogon:"Ą",aogon:"ą",Aopf:"𝔸",aopf:"𝕒",ap:"≈",apacir:"⩯",apE:"⩰",ape:"≊",apid:"≋",apos:"'",ApplyFunction:"⁡",approx:"≈",approxeq:"≊",Aring:"Å",aring:"å",Ascr:"𝒜",ascr:"𝒶",Assign:"≔",ast:"*",asymp:"≈",asympeq:"≍",Atilde:"Ã",atilde:"ã",Auml:"Ä",auml:"ä",awconint:"∳",awint:"⨑",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",Backslash:"∖",Barv:"⫧",barvee:"⊽",Barwed:"⌆",barwed:"⌅",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",Bcy:"Б",bcy:"б",bdquo:"„",becaus:"∵",Because:"∵",because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",Bernoullis:"ℬ",Beta:"Β",beta:"β",beth:"ℶ",between:"≬",Bfr:"𝔅",bfr:"𝔟",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bNot:"⫭",bnot:"⌐",Bopf:"𝔹",bopf:"𝕓",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxbox:"⧉",boxDL:"╗",boxDl:"╖",boxdL:"╕",boxdl:"┐",boxDR:"╔",boxDr:"╓",boxdR:"╒",boxdr:"┌",boxH:"═",boxh:"─",boxHD:"╦",boxHd:"╤",boxhD:"╥",boxhd:"┬",boxHU:"╩",boxHu:"╧",boxhU:"╨",boxhu:"┴",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxUL:"╝",boxUl:"╜",boxuL:"╛",boxul:"┘",boxUR:"╚",boxUr:"╙",boxuR:"╘",boxur:"└",boxV:"║",boxv:"│",boxVH:"╬",boxVh:"╫",boxvH:"╪",boxvh:"┼",boxVL:"╣",boxVl:"╢",boxvL:"╡",boxvl:"┤",boxVR:"╠",boxVr:"╟",boxvR:"╞",boxvr:"├",bprime:"‵",Breve:"˘",breve:"˘",brvbar:"¦",Bscr:"ℬ",bscr:"𝒷",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsol:"\\",bsolb:"⧅",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpE:"⪮",bumpe:"≏",Bumpeq:"≎",bumpeq:"≏",Cacute:"Ć",cacute:"ć",Cap:"⋒",cap:"∩",capand:"⩄",capbrcup:"⩉",capcap:"⩋",capcup:"⩇",capdot:"⩀",CapitalDifferentialD:"ⅅ",caps:"∩︀",caret:"⁁",caron:"ˇ",Cayleys:"ℭ",ccaps:"⩍",Ccaron:"Č",ccaron:"č",Ccedil:"Ç",ccedil:"ç",Ccirc:"Ĉ",ccirc:"ĉ",Cconint:"∰",ccups:"⩌",ccupssm:"⩐",Cdot:"Ċ",cdot:"ċ",cedil:"¸",Cedilla:"¸",cemptyv:"⦲",cent:"¢",CenterDot:"·",centerdot:"·",Cfr:"ℭ",cfr:"𝔠",CHcy:"Ч",chcy:"ч",check:"✓",checkmark:"✓",Chi:"Χ",chi:"χ",cir:"○",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",CircleDot:"⊙",circledR:"®",circledS:"Ⓢ",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",cirE:"⧃",cire:"≗",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",clubs:"♣",clubsuit:"♣",Colon:"∷",colon:":",Colone:"⩴",colone:"≔",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",Congruent:"≡",Conint:"∯",conint:"∮",ContourIntegral:"∮",Copf:"ℂ",copf:"𝕔",coprod:"∐",Coproduct:"∐",COPY:"©",copy:"©",copysr:"℗",CounterClockwiseContourIntegral:"∳",crarr:"↵",Cross:"⨯",cross:"✗",Cscr:"𝒞",cscr:"𝒸",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",Cup:"⋓",cup:"∪",cupbrcap:"⩈",CupCap:"≍",cupcap:"⩆",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curren:"¤",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",Dagger:"‡",dagger:"†",daleth:"ℸ",Darr:"↡",dArr:"⇓",darr:"↓",dash:"‐",Dashv:"⫤",dashv:"⊣",dbkarow:"⤏",dblac:"˝",Dcaron:"Ď",dcaron:"ď",Dcy:"Д",dcy:"д",DD:"ⅅ",dd:"ⅆ",ddagger:"‡",ddarr:"⇊",DDotrahd:"⤑",ddotseq:"⩷",deg:"°",Del:"∇",Delta:"Δ",delta:"δ",demptyv:"⦱",dfisht:"⥿",Dfr:"𝔇",dfr:"𝔡",dHar:"⥥",dharl:"⇃",dharr:"⇂",DiacriticalAcute:"´",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",diam:"⋄",Diamond:"⋄",diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"¨",DifferentialD:"ⅆ",digamma:"ϝ",disin:"⋲",div:"÷",divide:"÷",divideontimes:"⋇",divonx:"⋇",DJcy:"Ђ",djcy:"ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",Dopf:"𝔻",dopf:"𝕕",Dot:"¨",dot:"˙",DotDot:"⃜",doteq:"≐",doteqdot:"≑",DotEqual:"≐",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",DoubleContourIntegral:"∯",DoubleDot:"¨",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",DownArrow:"↓",Downarrow:"⇓",downarrow:"↓",DownArrowBar:"⤓",DownArrowUpArrow:"⇵",DownBreve:"̑",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVector:"↽",DownLeftVectorBar:"⥖",DownRightTeeVector:"⥟",DownRightVector:"⇁",DownRightVectorBar:"⥗",DownTee:"⊤",DownTeeArrow:"↧",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",Dscr:"𝒟",dscr:"𝒹",DScy:"Ѕ",dscy:"ѕ",dsol:"⧶",Dstrok:"Đ",dstrok:"đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",DZcy:"Џ",dzcy:"џ",dzigrarr:"⟿",Eacute:"É",eacute:"é",easter:"⩮",Ecaron:"Ě",ecaron:"ě",ecir:"≖",Ecirc:"Ê",ecirc:"ê",ecolon:"≕",Ecy:"Э",ecy:"э",eDDot:"⩷",Edot:"Ė",eDot:"≑",edot:"ė",ee:"ⅇ",efDot:"≒",Efr:"𝔈",efr:"𝔢",eg:"⪚",Egrave:"È",egrave:"è",egs:"⪖",egsdot:"⪘",el:"⪙",Element:"∈",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",Emacr:"Ē",emacr:"ē",empty:"∅",emptyset:"∅",EmptySmallSquare:"◻",emptyv:"∅",EmptyVerySmallSquare:"▫",emsp:" ",emsp13:" ",emsp14:" ",ENG:"Ŋ",eng:"ŋ",ensp:" ",Eogon:"Ę",eogon:"ę",Eopf:"𝔼",eopf:"𝕖",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",Epsilon:"Ε",epsilon:"ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",Equal:"⩵",equals:"=",EqualTilde:"≂",equest:"≟",Equilibrium:"⇌",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erarr:"⥱",erDot:"≓",Escr:"ℰ",escr:"ℯ",esdot:"≐",Esim:"⩳",esim:"≂",Eta:"Η",eta:"η",ETH:"Ð",eth:"ð",Euml:"Ë",euml:"ë",euro:"€",excl:"!",exist:"∃",Exists:"∃",expectation:"ℰ",ExponentialE:"ⅇ",exponentiale:"ⅇ",fallingdotseq:"≒",Fcy:"Ф",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",Ffr:"𝔉",ffr:"𝔣",filig:"ﬁ",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",Fopf:"𝔽",fopf:"𝕗",ForAll:"∀",forall:"∀",fork:"⋔",forkv:"⫙",Fouriertrf:"ℱ",fpartint:"⨍",frac12:"½",frac13:"⅓",frac14:"¼",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac34:"¾",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",Fscr:"ℱ",fscr:"𝒻",gacute:"ǵ",Gamma:"Γ",gamma:"γ",Gammad:"Ϝ",gammad:"ϝ",gap:"⪆",Gbreve:"Ğ",gbreve:"ğ",Gcedil:"Ģ",Gcirc:"Ĝ",gcirc:"ĝ",Gcy:"Г",gcy:"г",Gdot:"Ġ",gdot:"ġ",gE:"≧",ge:"≥",gEl:"⪌",gel:"⋛",geq:"≥",geqq:"≧",geqslant:"⩾",ges:"⩾",gescc:"⪩",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",Gfr:"𝔊",gfr:"𝔤",Gg:"⋙",gg:"≫",ggg:"⋙",gimel:"ℷ",GJcy:"Ѓ",gjcy:"ѓ",gl:"≷",gla:"⪥",glE:"⪒",glj:"⪤",gnap:"⪊",gnapprox:"⪊",gnE:"≩",gne:"⪈",gneq:"⪈",gneqq:"≩",gnsim:"⋧",Gopf:"𝔾",gopf:"𝕘",grave:"`",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",Gscr:"𝒢",gscr:"ℊ",gsim:"≳",gsime:"⪎",gsiml:"⪐",Gt:"≫",GT:">",gt:">",gtcc:"⪧",gtcir:"⩺",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",Hacek:"ˇ",hairsp:" ",half:"½",hamilt:"ℋ",HARDcy:"Ъ",hardcy:"ъ",hArr:"⇔",harr:"↔",harrcir:"⥈",harrw:"↭",Hat:"^",hbar:"ℏ",Hcirc:"Ĥ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",Hfr:"ℌ",hfr:"𝔥",HilbertSpace:"ℋ",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",Hopf:"ℍ",hopf:"𝕙",horbar:"―",HorizontalLine:"─",Hscr:"ℋ",hscr:"𝒽",hslash:"ℏ",Hstrok:"Ħ",hstrok:"ħ",HumpDownHump:"≎",HumpEqual:"≏",hybull:"⁃",hyphen:"‐",Iacute:"Í",iacute:"í",ic:"⁣",Icirc:"Î",icirc:"î",Icy:"И",icy:"и",Idot:"İ",IEcy:"Е",iecy:"е",iexcl:"¡",iff:"⇔",Ifr:"ℑ",ifr:"𝔦",Igrave:"Ì",igrave:"ì",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",IJlig:"Ĳ",ijlig:"ĳ",Im:"ℑ",Imacr:"Ī",imacr:"ī",image:"ℑ",ImaginaryI:"ⅈ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",imof:"⊷",imped:"Ƶ",Implies:"⇒",in:"∈",incare:"℅",infin:"∞",infintie:"⧝",inodot:"ı",Int:"∬",int:"∫",intcal:"⊺",integers:"ℤ",Integral:"∫",intercal:"⊺",Intersection:"⋂",intlarhk:"⨗",intprod:"⨼",InvisibleComma:"⁣",InvisibleTimes:"⁢",IOcy:"Ё",iocy:"ё",Iogon:"Į",iogon:"į",Iopf:"𝕀",iopf:"𝕚",Iota:"Ι",iota:"ι",iprod:"⨼",iquest:"¿",Iscr:"ℐ",iscr:"𝒾",isin:"∈",isindot:"⋵",isinE:"⋹",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",Itilde:"Ĩ",itilde:"ĩ",Iukcy:"І",iukcy:"і",Iuml:"Ï",iuml:"ï",Jcirc:"Ĵ",jcirc:"ĵ",Jcy:"Й",jcy:"й",Jfr:"𝔍",jfr:"𝔧",jmath:"ȷ",Jopf:"𝕁",jopf:"𝕛",Jscr:"𝒥",jscr:"𝒿",Jsercy:"Ј",jsercy:"ј",Jukcy:"Є",jukcy:"є",Kappa:"Κ",kappa:"κ",kappav:"ϰ",Kcedil:"Ķ",kcedil:"ķ",Kcy:"К",kcy:"к",Kfr:"𝔎",kfr:"𝔨",kgreen:"ĸ",KHcy:"Х",khcy:"х",KJcy:"Ќ",kjcy:"ќ",Kopf:"𝕂",kopf:"𝕜",Kscr:"𝒦",kscr:"𝓀",lAarr:"⇚",Lacute:"Ĺ",lacute:"ĺ",laemptyv:"⦴",lagran:"ℒ",Lambda:"Λ",lambda:"λ",Lang:"⟪",lang:"⟨",langd:"⦑",langle:"⟨",lap:"⪅",Laplacetrf:"ℒ",laquo:"«",Larr:"↞",lArr:"⇐",larr:"←",larrb:"⇤",larrbfs:"⤟",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",lat:"⪫",lAtail:"⤛",latail:"⤙",late:"⪭",lates:"⪭︀",lBarr:"⤎",lbarr:"⤌",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",Lcaron:"Ľ",lcaron:"ľ",Lcedil:"Ļ",lcedil:"ļ",lceil:"⌈",lcub:"{",Lcy:"Л",lcy:"л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",lE:"≦",le:"≤",LeftAngleBracket:"⟨",LeftArrow:"←",Leftarrow:"⇐",leftarrow:"←",LeftArrowBar:"⇤",LeftArrowRightArrow:"⇆",leftarrowtail:"↢",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVector:"⇃",LeftDownVectorBar:"⥙",LeftFloor:"⌊",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",LeftRightArrow:"↔",Leftrightarrow:"⇔",leftrightarrow:"↔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",LeftRightVector:"⥎",LeftTee:"⊣",LeftTeeArrow:"↤",LeftTeeVector:"⥚",leftthreetimes:"⋋",LeftTriangle:"⊲",LeftTriangleBar:"⧏",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVector:"↿",LeftUpVectorBar:"⥘",LeftVector:"↼",LeftVectorBar:"⥒",lEg:"⪋",leg:"⋚",leq:"≤",leqq:"≦",leqslant:"⩽",les:"⩽",lescc:"⪨",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",lessgtr:"≶",LessLess:"⪡",lesssim:"≲",LessSlantEqual:"⩽",LessTilde:"≲",lfisht:"⥼",lfloor:"⌊",Lfr:"𝔏",lfr:"𝔩",lg:"≶",lgE:"⪑",lHar:"⥢",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",LJcy:"Љ",ljcy:"љ",Ll:"⋘",ll:"≪",llarr:"⇇",llcorner:"⌞",Lleftarrow:"⇚",llhard:"⥫",lltri:"◺",Lmidot:"Ŀ",lmidot:"ŀ",lmoust:"⎰",lmoustache:"⎰",lnap:"⪉",lnapprox:"⪉",lnE:"≨",lne:"⪇",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",LongLeftArrow:"⟵",Longleftarrow:"⟸",longleftarrow:"⟵",LongLeftRightArrow:"⟷",Longleftrightarrow:"⟺",longleftrightarrow:"⟷",longmapsto:"⟼",LongRightArrow:"⟶",Longrightarrow:"⟹",longrightarrow:"⟶",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",Lopf:"𝕃",lopf:"𝕝",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",LowerLeftArrow:"↙",LowerRightArrow:"↘",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",Lscr:"ℒ",lscr:"𝓁",Lsh:"↰",lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",Lstrok:"Ł",lstrok:"ł",Lt:"≪",LT:"<",lt:"<",ltcc:"⪦",ltcir:"⩹",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltri:"◃",ltrie:"⊴",ltrif:"◂",ltrPar:"⦖",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",macr:"¯",male:"♂",malt:"✠",maltese:"✠",Map:"⤅",map:"↦",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",Mcy:"М",mcy:"м",mdash:"—",mDDot:"∺",measuredangle:"∡",MediumSpace:" ",Mellintrf:"ℳ",Mfr:"𝔐",mfr:"𝔪",mho:"℧",micro:"µ",mid:"∣",midast:"*",midcir:"⫰",middot:"·",minus:"−",minusb:"⊟",minusd:"∸",minusdu:"⨪",MinusPlus:"∓",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",Mopf:"𝕄",mopf:"𝕞",mp:"∓",Mscr:"ℳ",mscr:"𝓂",mstpos:"∾",Mu:"Μ",mu:"μ",multimap:"⊸",mumap:"⊸",nabla:"∇",Nacute:"Ń",nacute:"ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natur:"♮",natural:"♮",naturals:"ℕ",nbsp:" ",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",Ncaron:"Ň",ncaron:"ň",Ncedil:"Ņ",ncedil:"ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",Ncy:"Н",ncy:"н",ndash:"–",ne:"≠",nearhk:"⤤",neArr:"⇗",nearr:"↗",nearrow:"↗",nedot:"≐̸",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",nequiv:"≢",nesear:"⤨",nesim:"≂̸",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:"\n",nexist:"∄",nexists:"∄",Nfr:"𝔑",nfr:"𝔫",ngE:"≧̸",nge:"≱",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",nGg:"⋙̸",ngsim:"≵",nGt:"≫⃒",ngt:"≯",ngtr:"≯",nGtv:"≫̸",nhArr:"⇎",nharr:"↮",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",NJcy:"Њ",njcy:"њ",nlArr:"⇍",nlarr:"↚",nldr:"‥",nlE:"≦̸",nle:"≰",nLeftarrow:"⇍",nleftarrow:"↚",nLeftrightarrow:"⇎",nleftrightarrow:"↮",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nLl:"⋘̸",nlsim:"≴",nLt:"≪⃒",nlt:"≮",nltri:"⋪",nltrie:"⋬",nLtv:"≪̸",nmid:"∤",NoBreak:"⁠",NonBreakingSpace:" ",Nopf:"ℕ",nopf:"𝕟",Not:"⫬",not:"¬",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",notin:"∉",notindot:"⋵̸",notinE:"⋹̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",NotLeftTriangle:"⋪",NotLeftTriangleBar:"⧏̸",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangle:"⋫",NotRightTriangleBar:"⧐̸",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",npar:"∦",nparallel:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",npre:"⪯̸",nprec:"⊀",npreceq:"⪯̸",nrArr:"⇏",nrarr:"↛",nrarrc:"⤳̸",nrarrw:"↝̸",nRightarrow:"⇏",nrightarrow:"↛",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",Nscr:"𝒩",nscr:"𝓃",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsubE:"⫅̸",nsube:"⊈",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupE:"⫆̸",nsupe:"⊉",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",Ntilde:"Ñ",ntilde:"ñ",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",Nu:"Ν",nu:"ν",num:"#",numero:"№",numsp:" ",nvap:"≍⃒",nVDash:"⊯",nVdash:"⊮",nvDash:"⊭",nvdash:"⊬",nvge:"≥⃒",nvgt:">⃒",nvHarr:"⤄",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwarhk:"⤣",nwArr:"⇖",nwarr:"↖",nwarrow:"↖",nwnear:"⤧",Oacute:"Ó",oacute:"ó",oast:"⊛",ocir:"⊚",Ocirc:"Ô",ocirc:"ô",Ocy:"О",ocy:"о",odash:"⊝",Odblac:"Ő",odblac:"ő",odiv:"⨸",odot:"⊙",odsold:"⦼",OElig:"Œ",oelig:"œ",ofcir:"⦿",Ofr:"𝔒",ofr:"𝔬",ogon:"˛",Ograve:"Ò",ograve:"ò",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",Omacr:"Ō",omacr:"ō",Omega:"Ω",omega:"ω",Omicron:"Ο",omicron:"ο",omid:"⦶",ominus:"⊖",Oopf:"𝕆",oopf:"𝕠",opar:"⦷",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",operp:"⦹",oplus:"⊕",Or:"⩔",or:"∨",orarr:"↻",ord:"⩝",order:"ℴ",orderof:"ℴ",ordf:"ª",ordm:"º",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oS:"Ⓢ",Oscr:"𝒪",oscr:"ℴ",Oslash:"Ø",oslash:"ø",osol:"⊘",Otilde:"Õ",otilde:"õ",Otimes:"⨷",otimes:"⊗",otimesas:"⨶",Ouml:"Ö",ouml:"ö",ovbar:"⌽",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",par:"∥",para:"¶",parallel:"∥",parsim:"⫳",parsl:"⫽",part:"∂",PartialD:"∂",Pcy:"П",pcy:"п",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",Pfr:"𝔓",pfr:"𝔭",Phi:"Φ",phi:"φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",Pi:"Π",pi:"π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plus:"+",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plusdo:"∔",plusdu:"⨥",pluse:"⩲",PlusMinus:"±",plusmn:"±",plussim:"⨦",plustwo:"⨧",pm:"±",Poincareplane:"ℌ",pointint:"⨕",Popf:"ℙ",popf:"𝕡",pound:"£",Pr:"⪻",pr:"≺",prap:"⪷",prcue:"≼",prE:"⪳",pre:"⪯",prec:"≺",precapprox:"⪷",preccurlyeq:"≼",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",precsim:"≾",Prime:"″",prime:"′",primes:"ℙ",prnap:"⪹",prnE:"⪵",prnsim:"⋨",prod:"∏",Product:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",Proportion:"∷",Proportional:"∝",propto:"∝",prsim:"≾",prurel:"⊰",Pscr:"𝒫",pscr:"𝓅",Psi:"Ψ",psi:"ψ",puncsp:" ",Qfr:"𝔔",qfr:"𝔮",qint:"⨌",Qopf:"ℚ",qopf:"𝕢",qprime:"⁗",Qscr:"𝒬",qscr:"𝓆",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",QUOT:'"',quot:'"',rAarr:"⇛",race:"∽̱",Racute:"Ŕ",racute:"ŕ",radic:"√",raemptyv:"⦳",Rang:"⟫",rang:"⟩",rangd:"⦒",range:"⦥",rangle:"⟩",raquo:"»",Rarr:"↠",rArr:"⇒",rarr:"→",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",Rarrtl:"⤖",rarrtl:"↣",rarrw:"↝",rAtail:"⤜",ratail:"⤚",ratio:"∶",rationals:"ℚ",RBarr:"⤐",rBarr:"⤏",rbarr:"⤍",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",Rcaron:"Ř",rcaron:"ř",Rcedil:"Ŗ",rcedil:"ŗ",rceil:"⌉",rcub:"}",Rcy:"Р",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",Re:"ℜ",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",rect:"▭",REG:"®",reg:"®",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",rfisht:"⥽",rfloor:"⌋",Rfr:"ℜ",rfr:"𝔯",rHar:"⥤",rhard:"⇁",rharu:"⇀",rharul:"⥬",Rho:"Ρ",rho:"ρ",rhov:"ϱ",RightAngleBracket:"⟩",RightArrow:"→",Rightarrow:"⇒",rightarrow:"→",RightArrowBar:"⇥",RightArrowLeftArrow:"⇄",rightarrowtail:"↣",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVector:"⇂",RightDownVectorBar:"⥕",RightFloor:"⌋",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",RightTee:"⊢",RightTeeArrow:"↦",RightTeeVector:"⥛",rightthreetimes:"⋌",RightTriangle:"⊳",RightTriangleBar:"⧐",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVector:"↾",RightUpVectorBar:"⥔",RightVector:"⇀",RightVectorBar:"⥓",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoust:"⎱",rmoustache:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",Ropf:"ℝ",ropf:"𝕣",roplus:"⨮",rotimes:"⨵",RoundImplies:"⥰",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",Rrightarrow:"⇛",rsaquo:"›",Rscr:"ℛ",rscr:"𝓇",Rsh:"↱",rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",RuleDelayed:"⧴",ruluhar:"⥨",rx:"℞",Sacute:"Ś",sacute:"ś",sbquo:"‚",Sc:"⪼",sc:"≻",scap:"⪸",Scaron:"Š",scaron:"š",sccue:"≽",scE:"⪴",sce:"⪰",Scedil:"Ş",scedil:"ş",Scirc:"Ŝ",scirc:"ŝ",scnap:"⪺",scnE:"⪶",scnsim:"⋩",scpolint:"⨓",scsim:"≿",Scy:"С",scy:"с",sdot:"⋅",sdotb:"⊡",sdote:"⩦",searhk:"⤥",seArr:"⇘",searr:"↘",searrow:"↘",sect:"§",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",Sfr:"𝔖",sfr:"𝔰",sfrown:"⌢",sharp:"♯",SHCHcy:"Щ",shchcy:"щ",SHcy:"Ш",shcy:"ш",ShortDownArrow:"↓",ShortLeftArrow:"←",shortmid:"∣",shortparallel:"∥",ShortRightArrow:"→",ShortUpArrow:"↑",shy:"­",Sigma:"Σ",sigma:"σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",SmallCircle:"∘",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",SOFTcy:"Ь",softcy:"ь",sol:"/",solb:"⧄",solbar:"⌿",Sopf:"𝕊",sopf:"𝕤",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",Sqrt:"√",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",squ:"□",Square:"□",square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",squarf:"▪",squf:"▪",srarr:"→",Sscr:"𝒮",sscr:"𝓈",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",Star:"⋆",star:"☆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"¯",Sub:"⋐",sub:"⊂",subdot:"⪽",subE:"⫅",sube:"⊆",subedot:"⫃",submult:"⫁",subnE:"⫋",subne:"⊊",subplus:"⪿",subrarr:"⥹",Subset:"⋐",subset:"⊂",subseteq:"⊆",subseteqq:"⫅",SubsetEqual:"⊆",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succ:"≻",succapprox:"⪸",succcurlyeq:"≽",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",SuchThat:"∋",Sum:"∑",sum:"∑",sung:"♪",Sup:"⋑",sup:"⊃",sup1:"¹",sup2:"²",sup3:"³",supdot:"⪾",supdsub:"⫘",supE:"⫆",supe:"⊇",supedot:"⫄",Superset:"⊃",SupersetEqual:"⊇",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supne:"⊋",supplus:"⫀",Supset:"⋑",supset:"⊃",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swarhk:"⤦",swArr:"⇙",swarr:"↙",swarrow:"↙",swnwar:"⤪",szlig:"ß",Tab:"\t",target:"⌖",Tau:"Τ",tau:"τ",tbrk:"⎴",Tcaron:"Ť",tcaron:"ť",Tcedil:"Ţ",tcedil:"ţ",Tcy:"Т",tcy:"т",tdot:"⃛",telrec:"⌕",Tfr:"𝔗",tfr:"𝔱",there4:"∴",Therefore:"∴",therefore:"∴",Theta:"Θ",theta:"θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",ThickSpace:"  ",thinsp:" ",ThinSpace:" ",thkap:"≈",thksim:"∼",THORN:"Þ",thorn:"þ",Tilde:"∼",tilde:"˜",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",times:"×",timesb:"⊠",timesbar:"⨱",timesd:"⨰",tint:"∭",toea:"⤨",top:"⊤",topbot:"⌶",topcir:"⫱",Topf:"𝕋",topf:"𝕥",topfork:"⫚",tosa:"⤩",tprime:"‴",TRADE:"™",trade:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",TripleDot:"⃛",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",Tscr:"𝒯",tscr:"𝓉",TScy:"Ц",tscy:"ц",TSHcy:"Ћ",tshcy:"ћ",Tstrok:"Ŧ",tstrok:"ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",Uacute:"Ú",uacute:"ú",Uarr:"↟",uArr:"⇑",uarr:"↑",Uarrocir:"⥉",Ubrcy:"Ў",ubrcy:"ў",Ubreve:"Ŭ",ubreve:"ŭ",Ucirc:"Û",ucirc:"û",Ucy:"У",ucy:"у",udarr:"⇅",Udblac:"Ű",udblac:"ű",udhar:"⥮",ufisht:"⥾",Ufr:"𝔘",ufr:"𝔲",Ugrave:"Ù",ugrave:"ù",uHar:"⥣",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",Umacr:"Ū",umacr:"ū",uml:"¨",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",Uogon:"Ų",uogon:"ų",Uopf:"𝕌",uopf:"𝕦",UpArrow:"↑",Uparrow:"⇑",uparrow:"↑",UpArrowBar:"⤒",UpArrowDownArrow:"⇅",UpDownArrow:"↕",Updownarrow:"⇕",updownarrow:"↕",UpEquilibrium:"⥮",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",UpperLeftArrow:"↖",UpperRightArrow:"↗",Upsi:"ϒ",upsi:"υ",upsih:"ϒ",Upsilon:"Υ",upsilon:"υ",UpTee:"⊥",UpTeeArrow:"↥",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",Uring:"Ů",uring:"ů",urtri:"◹",Uscr:"𝒰",uscr:"𝓊",utdot:"⋰",Utilde:"Ũ",utilde:"ũ",utri:"▵",utrif:"▴",uuarr:"⇈",Uuml:"Ü",uuml:"ü",uwangle:"⦧",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",vArr:"⇕",varr:"↕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",Vbar:"⫫",vBar:"⫨",vBarv:"⫩",Vcy:"В",vcy:"в",VDash:"⊫",Vdash:"⊩",vDash:"⊨",vdash:"⊢",Vdashl:"⫦",Vee:"⋁",vee:"∨",veebar:"⊻",veeeq:"≚",vellip:"⋮",Verbar:"‖",verbar:"|",Vert:"‖",vert:"|",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",Vfr:"𝔙",vfr:"𝔳",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",Vopf:"𝕍",vopf:"𝕧",vprop:"∝",vrtri:"⊳",Vscr:"𝒱",vscr:"𝓋",vsubnE:"⫋︀",vsubne:"⊊︀",vsupnE:"⫌︀",vsupne:"⊋︀",Vvdash:"⊪",vzigzag:"⦚",Wcirc:"Ŵ",wcirc:"ŵ",wedbar:"⩟",Wedge:"⋀",wedge:"∧",wedgeq:"≙",weierp:"℘",Wfr:"𝔚",wfr:"𝔴",Wopf:"𝕎",wopf:"𝕨",wp:"℘",wr:"≀",wreath:"≀",Wscr:"𝒲",wscr:"𝓌",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",Xfr:"𝔛",xfr:"𝔵",xhArr:"⟺",xharr:"⟷",Xi:"Ξ",xi:"ξ",xlArr:"⟸",xlarr:"⟵",xmap:"⟼",xnis:"⋻",xodot:"⨀",Xopf:"𝕏",xopf:"𝕩",xoplus:"⨁",xotime:"⨂",xrArr:"⟹",xrarr:"⟶",Xscr:"𝒳",xscr:"𝓍",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",Yacute:"Ý",yacute:"ý",YAcy:"Я",yacy:"я",Ycirc:"Ŷ",ycirc:"ŷ",Ycy:"Ы",ycy:"ы",yen:"¥",Yfr:"𝔜",yfr:"𝔶",YIcy:"Ї",yicy:"ї",Yopf:"𝕐",yopf:"𝕪",Yscr:"𝒴",yscr:"𝓎",YUcy:"Ю",yucy:"ю",Yuml:"Ÿ",yuml:"ÿ",Zacute:"Ź",zacute:"ź",Zcaron:"Ž",zcaron:"ž",Zcy:"З",zcy:"з",Zdot:"Ż",zdot:"ż",zeetrf:"ℨ",ZeroWidthSpace:"​",Zeta:"Ζ",zeta:"ζ",Zfr:"ℨ",zfr:"𝔷",ZHcy:"Ж",zhcy:"ж",zigrarr:"⇝",Zopf:"ℤ",zopf:"𝕫",Zscr:"𝒵",zscr:"𝓏",zwj:"‍",zwnj:"‌"}),r.entityMap=r.HTML_ENTITIES},{"./conventions":9}],13:[function(e,t,r){var n=e("./dom");r.DOMImplementation=n.DOMImplementation,r.XMLSerializer=n.XMLSerializer,r.DOMParser=e("./dom-parser").DOMParser},{"./dom":11,"./dom-parser":10}],14:[function(e,t,r){var n=e("./conventions").NAMESPACE,i=/[A-Z_a-z\xC0-\xD6\xD8-\xF6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,o=new RegExp("[\\-\\.0-9"+i.source.slice(1,-1)+"\\u00B7\\u0300-\\u036F\\u203F-\\u2040]"),a=new RegExp("^"+i.source+o.source+"*(?::"+i.source+o.source+"*)?$"),s=0,u=1,l=2,f=3,c=4,h=5,d=6,p=7;function g(e,t){this.message=e,this.locator=t,Error.captureStackTrace&&Error.captureStackTrace(this,g)}function y(){}function b(e,t){return t.lineNumber=e.lineNumber,t.columnNumber=e.columnNumber,t}function m(e,t,r,i,o,a){function g(e,t,n){r.attributeNames.hasOwnProperty(e)&&a.fatalError("Attribute "+e+" redefined"),r.addValue(e,t.replace(/[\t\n\r]/g," ").replace(/&#?\w+;/g,o),n)}for(var y,b=++t,m=s;;){var v=e.charAt(b);switch(v){case"=":if(m===u)y=e.slice(t,b),m=f;else{if(m!==l)throw new Error("attribute equal must after attrName");m=f}break;case"'":case'"':if(m===f||m===u){if(m===u&&(a.warning('attribute value must after "="'),y=e.slice(t,b)),t=b+1,!((b=e.indexOf(v,t))>0))throw new Error("attribute value no end '"+v+"' match");g(y,w=e.slice(t,b),t-1),m=h}else{if(m!=c)throw new Error('attribute value must after "="');g(y,w=e.slice(t,b),t),a.warning('attribute "'+y+'" missed start quot('+v+")!!"),t=b+1,m=h}break;case"/":switch(m){case s:r.setTagName(e.slice(t,b));case h:case d:case p:m=p,r.closed=!0;case c:case u:break;case l:r.closed=!0;break;default:throw new Error("attribute invalid close char('/')")}break;case"":return a.error("unexpected end of input"),m==s&&r.setTagName(e.slice(t,b)),b;case">":switch(m){case s:r.setTagName(e.slice(t,b));case h:case d:case p:break;case c:case u:"/"===(w=e.slice(t,b)).slice(-1)&&(r.closed=!0,w=w.slice(0,-1));case l:m===l&&(w=y),m==c?(a.warning('attribute "'+w+'" missed quot(")!'),g(y,w,t)):(n.isHTML(i[""])&&w.match(/^(?:disabled|checked|selected)$/i)||a.warning('attribute "'+w+'" missed value!! "'+w+'" instead!!'),g(w,w,t));break;case f:throw new Error("attribute value missed!!")}return b;case"":v=" ";default:if(v<=" ")switch(m){case s:r.setTagName(e.slice(t,b)),m=d;break;case u:y=e.slice(t,b),m=l;break;case c:var w=e.slice(t,b);a.warning('attribute "'+w+'" missed quot(")!!'),g(y,w,t);case h:m=d}else switch(m){case l:r.tagName;n.isHTML(i[""])&&y.match(/^(?:disabled|checked|selected)$/i)||a.warning('attribute "'+y+'" missed value!! "'+y+'" instead2!!'),g(y,y,t),t=b,m=u;break;case h:a.warning('attribute space is required"'+y+'"!!');case d:m=u,t=b;break;case f:m=c,t=b;break;case p:throw new Error("elements closed character '/' and '>' must be connected to")}}b++}}function v(e,t,r){for(var i=e.tagName,o=null,a=e.length;a--;){var s=e[a],u=s.qName,l=s.value;if((d=u.indexOf(":"))>0)var f=s.prefix=u.slice(0,d),c=u.slice(d+1),h="xmlns"===f&&c;else c=u,f=null,h="xmlns"===u&&"";s.localName=c,!1!==h&&(null==o&&(o={},E(r,r={})),r[h]=o[h]=l,s.uri=n.XMLNS,t.startPrefixMapping(h,l))}for(a=e.length;a--;){(f=(s=e[a]).prefix)&&("xml"===f&&(s.uri=n.XML),"xmlns"!==f&&(s.uri=r[f||""]))}var d;(d=i.indexOf(":"))>0?(f=e.prefix=i.slice(0,d),c=e.localName=i.slice(d+1)):(f=null,c=e.localName=i);var p=e.uri=r[f||""];if(t.startElement(p,c,i,e),!e.closed)return e.currentNSMap=r,e.localNSMap=o,!0;if(t.endElement(p,c,i),o)for(f in o)Object.prototype.hasOwnProperty.call(o,f)&&t.endPrefixMapping(f)}function w(e,t,r,n,i){if(/^(?:script|textarea)$/i.test(r)){var o=e.indexOf("</"+r+">",t),a=e.substring(t+1,o);if(/[&<]/.test(a))return/^script$/i.test(r)?(i.characters(a,0,a.length),o):(a=a.replace(/&#?\w+;/g,n),i.characters(a,0,a.length),o)}return t+1}function _(e,t,r,n){var i=n[r];return null==i&&((i=e.lastIndexOf("</"+r+">"))<t&&(i=e.lastIndexOf("</"+r)),n[r]=i),i<t}function E(e,t){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}function x(e,t,r,n){switch(e.charAt(t+2)){case"-":return"-"===e.charAt(t+3)?(i=e.indexOf("--\x3e",t+4))>t?(r.comment(e,t+4,i-t-4),i+3):(n.error("Unclosed comment"),-1):-1;default:if("CDATA["==e.substr(t+3,6)){var i=e.indexOf("]]>",t+9);return r.startCDATA(),r.characters(e,t+9,i-t-9),r.endCDATA(),i+3}var o=function(e,t){var r,n=[],i=/'[^']+'|"[^"]+"|[^\s<>\/=]+=?|(\/?\s*>|<)/g;i.lastIndex=t,i.exec(e);for(;r=i.exec(e);)if(n.push(r),r[1])return n}(e,t),a=o.length;if(a>1&&/!doctype/i.test(o[0][0])){var s=o[1][0],u=!1,l=!1;a>3&&(/^public$/i.test(o[2][0])?(u=o[3][0],l=a>4&&o[4][0]):/^system$/i.test(o[2][0])&&(l=o[3][0]));var f=o[a-1];return r.startDTD(s,u,l),r.endDTD(),f.index+f[0].length}}return-1}function T(e,t,r){var n=e.indexOf("?>",t);if(n){var i=e.substring(t,n).match(/^<\?(\S*)\s*([\s\S]*?)\s*$/);if(i){i[0].length;return r.processingInstruction(i[1],i[2]),n+2}return-1}return-1}function k(){this.attributeNames={}}g.prototype=new Error,g.prototype.name=g.name,y.prototype={parse:function(e,t,r){var i=this.domBuilder;i.startDocument(),E(t,t={}),function(e,t,r,i,o){function a(e){var t=e.slice(1,-1);return Object.hasOwnProperty.call(r,t)?r[t]:"#"===t.charAt(0)?function(e){if(e>65535){var t=55296+((e-=65536)>>10),r=56320+(1023&e);return String.fromCharCode(t,r)}return String.fromCharCode(e)}(parseInt(t.substr(1).replace("x","0x"))):(o.error("entity not found:"+e),e)}function s(t){if(t>y){var r=e.substring(y,t).replace(/&#?\w+;/g,a);h&&u(y),i.characters(r,0,t-y),y=t}}function u(t,r){for(;t>=f&&(r=c.exec(e));)l=r.index,f=l+r[0].length,h.lineNumber++;h.columnNumber=t-l+1}var l=0,f=0,c=/.*(?:\r\n?|\n)|.*$/g,h=i.locator,d=[{currentNSMap:t}],p={},y=0;for(;;){try{var E=e.indexOf("<",y);if(E<0){if(!e.substr(y).match(/^\s*$/)){var N=i.doc,S=N.createTextNode(e.substr(y));N.appendChild(S),i.currentElement=S}return}switch(E>y&&s(E),e.charAt(E+1)){case"/":var I=e.indexOf(">",E+3),A=e.substring(E+2,I).replace(/[ \t\n\r]+$/g,""),D=d.pop();I<0?(A=e.substring(E+2).replace(/[\s<].*/,""),o.error("end tag name: "+A+" is not complete:"+D.tagName),I=E+1+A.length):A.match(/\s</)&&(A=A.replace(/[\s<].*/,""),o.error("end tag name: "+A+" maybe not complete"),I=E+1+A.length);var L=D.localNSMap,O=D.tagName==A,M=O||D.tagName&&D.tagName.toLowerCase()==A.toLowerCase();if(M){if(i.endElement(D.uri,D.localName,A),L)for(var U in L)Object.prototype.hasOwnProperty.call(L,U)&&i.endPrefixMapping(U);O||o.fatalError("end tag name: "+A+" is not match the current start tagName:"+D.tagName)}else d.push(D);I++;break;case"?":h&&u(E),I=T(e,E,i);break;case"!":h&&u(E),I=x(e,E,i,o);break;default:h&&u(E);var C=new k,B=d[d.length-1].currentNSMap,I=m(e,E,C,B,a,o),R=C.length;if(!C.closed&&_(e,I,C.tagName,p)&&(C.closed=!0,r.nbsp||o.warning("unclosed xml attribute")),h&&R){for(var P=b(h,{}),j=0;j<R;j++){var F=C[j];u(F.offset),F.locator=b(h,{})}i.locator=P,v(C,i,B)&&d.push(C),i.locator=h}else v(C,i,B)&&d.push(C);n.isHTML(C.uri)&&!C.closed?I=w(e,I,C.tagName,a,i):I++}}catch(e){if(e instanceof g)throw e;o.error("element parse error: "+e),I=-1}I>y?y=I:s(Math.max(E,y)+1)}}(e,t,r,i,this.errorHandler),i.endDocument()}},k.prototype={setTagName:function(e){if(!a.test(e))throw new Error("invalid tagName:"+e);this.tagName=e},addValue:function(e,t,r){if(!a.test(e))throw new Error("invalid attribute:"+e);this.attributeNames[e]=this.length,this[this.length++]={qName:e,value:t,offset:r}},length:0,getLocalName:function(e){return this[e].localName},getLocator:function(e){return this[e].locator},getQName:function(e){return this[e].qName},getURI:function(e){return this[e].uri},getValue:function(e){return this[e].value}},r.XMLReader=y,r.ParseError=g},{"./conventions":9}],15:[function(e,t,r){(function(r){(function(){"use strict";var n=e("object.assign/polyfill")();function i(e,t){if(e===t)return 0;for(var r=e.length,n=t.length,i=0,o=Math.min(r,n);i<o;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:n<r?1:0}function o(e){return r.Buffer&&"function"==typeof r.Buffer.isBuffer?r.Buffer.isBuffer(e):!(null==e||!e._isBuffer)}var a=e("util/"),s=Object.prototype.hasOwnProperty,u=Array.prototype.slice,l="foo"===function(){}.name;function f(e){return Object.prototype.toString.call(e)}function c(e){return!o(e)&&("function"==typeof r.ArrayBuffer&&("function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):!!e&&(e instanceof DataView||!!(e.buffer&&e.buffer instanceof ArrayBuffer))))}var h=t.exports=m,d=/\s*function\s+([^\(\s]*)\s*/;function p(e){if(a.isFunction(e)){if(l)return e.name;var t=e.toString().match(d);return t&&t[1]}}function g(e,t){return"string"==typeof e?e.length<t?e:e.slice(0,t):e}function y(e){if(l||!a.isFunction(e))return a.inspect(e);var t=p(e);return"[Function"+(t?": "+t:"")+"]"}function b(e,t,r,n,i){throw new h.AssertionError({message:r,actual:e,expected:t,operator:n,stackStartFunction:i})}function m(e,t){e||b(e,!0,t,"==",h.ok)}function v(e,t,r,n){if(e===t)return!0;if(o(e)&&o(t))return 0===i(e,t);if(a.isDate(e)&&a.isDate(t))return e.getTime()===t.getTime();if(a.isRegExp(e)&&a.isRegExp(t))return e.source===t.source&&e.global===t.global&&e.multiline===t.multiline&&e.lastIndex===t.lastIndex&&e.ignoreCase===t.ignoreCase;if(null!==e&&"object"==typeof e||null!==t&&"object"==typeof t){if(c(e)&&c(t)&&f(e)===f(t)&&!(e instanceof Float32Array||e instanceof Float64Array))return 0===i(new Uint8Array(e.buffer),new Uint8Array(t.buffer));if(o(e)!==o(t))return!1;var s=(n=n||{actual:[],expected:[]}).actual.indexOf(e);return-1!==s&&s===n.expected.indexOf(t)||(n.actual.push(e),n.expected.push(t),function(e,t,r,n){if(null===e||void 0===e||null===t||void 0===t)return!1;if(a.isPrimitive(e)||a.isPrimitive(t))return e===t;if(r&&Object.getPrototypeOf(e)!==Object.getPrototypeOf(t))return!1;var i=w(e),o=w(t);if(i&&!o||!i&&o)return!1;if(i)return e=u.call(e),t=u.call(t),v(e,t,r);var s,l,f=x(e),c=x(t);if(f.length!==c.length)return!1;for(f.sort(),c.sort(),l=f.length-1;l>=0;l--)if(f[l]!==c[l])return!1;for(l=f.length-1;l>=0;l--)if(s=f[l],!v(e[s],t[s],r,n))return!1;return!0}(e,t,r,n))}return r?e===t:e==t}function w(e){return"[object Arguments]"==Object.prototype.toString.call(e)}function _(e,t){if(!e||!t)return!1;if("[object RegExp]"==Object.prototype.toString.call(t))return t.test(e);try{if(e instanceof t)return!0}catch(e){}return!Error.isPrototypeOf(t)&&!0===t.call({},e)}function E(e,t,r,n){var i;if("function"!=typeof t)throw new TypeError('"block" argument must be a function');"string"==typeof r&&(n=r,r=null),i=function(e){var t;try{e()}catch(e){t=e}return t}(t),n=(r&&r.name?" ("+r.name+").":".")+(n?" "+n:"."),e&&!i&&b(i,r,"Missing expected exception"+n);var o="string"==typeof n,s=!e&&a.isError(i),u=!e&&i&&!r;if((s&&o&&_(i,r)||u)&&b(i,r,"Got unwanted exception"+n),e&&i&&r&&!_(i,r)||!e&&i)throw i}h.AssertionError=function(e){var t;this.name="AssertionError",this.actual=e.actual,this.expected=e.expected,this.operator=e.operator,e.message?(this.message=e.message,this.generatedMessage=!1):(this.message=g(y((t=this).actual),128)+" "+t.operator+" "+g(y(t.expected),128),this.generatedMessage=!0);var r=e.stackStartFunction||b;if(Error.captureStackTrace)Error.captureStackTrace(this,r);else{var n=new Error;if(n.stack){var i=n.stack,o=p(r),a=i.indexOf("\n"+o);if(a>=0){var s=i.indexOf("\n",a+1);i=i.substring(s+1)}this.stack=i}}},a.inherits(h.AssertionError,Error),h.fail=b,h.ok=m,h.equal=function(e,t,r){e!=t&&b(e,t,r,"==",h.equal)},h.notEqual=function(e,t,r){e==t&&b(e,t,r,"!=",h.notEqual)},h.deepEqual=function(e,t,r){v(e,t,!1)||b(e,t,r,"deepEqual",h.deepEqual)},h.deepStrictEqual=function(e,t,r){v(e,t,!0)||b(e,t,r,"deepStrictEqual",h.deepStrictEqual)},h.notDeepEqual=function(e,t,r){v(e,t,!1)&&b(e,t,r,"notDeepEqual",h.notDeepEqual)},h.notDeepStrictEqual=function e(t,r,n){v(t,r,!0)&&b(t,r,n,"notDeepStrictEqual",e)},h.strictEqual=function(e,t,r){e!==t&&b(e,t,r,"===",h.strictEqual)},h.notStrictEqual=function(e,t,r){e===t&&b(e,t,r,"!==",h.notStrictEqual)},h.throws=function(e,t,r){E(!0,e,t,r)},h.doesNotThrow=function(e,t,r){E(!1,e,t,r)},h.ifError=function(e){if(e)throw e},h.strict=n(function e(t,r){t||b(t,!0,r,"==",e)},h,{equal:h.strictEqual,deepEqual:h.deepStrictEqual,notEqual:h.notStrictEqual,notDeepEqual:h.notDeepStrictEqual}),h.strict.strict=h.strict;var x=Object.keys||function(e){var t=[];for(var r in e)s.call(e,r)&&t.push(r);return t}}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"object.assign/polyfill":80,"util/":122}],16:[function(e,t,r){"use strict";r.byteLength=function(e){var t=l(e),r=t[0],n=t[1];return 3*(r+n)/4-n},r.toByteArray=function(e){var t,r,n=l(e),a=n[0],s=n[1],u=new o(function(e,t,r){return 3*(t+r)/4-r}(0,a,s)),f=0,c=s>0?a-4:a;for(r=0;r<c;r+=4)t=i[e.charCodeAt(r)]<<18|i[e.charCodeAt(r+1)]<<12|i[e.charCodeAt(r+2)]<<6|i[e.charCodeAt(r+3)],u[f++]=t>>16&255,u[f++]=t>>8&255,u[f++]=255&t;2===s&&(t=i[e.charCodeAt(r)]<<2|i[e.charCodeAt(r+1)]>>4,u[f++]=255&t);1===s&&(t=i[e.charCodeAt(r)]<<10|i[e.charCodeAt(r+1)]<<4|i[e.charCodeAt(r+2)]>>2,u[f++]=t>>8&255,u[f++]=255&t);return u},r.fromByteArray=function(e){for(var t,r=e.length,i=r%3,o=[],a=0,s=r-i;a<s;a+=16383)o.push(f(e,a,a+16383>s?s:a+16383));1===i?(t=e[r-1],o.push(n[t>>2]+n[t<<4&63]+"==")):2===i&&(t=(e[r-2]<<8)+e[r-1],o.push(n[t>>10]+n[t>>4&63]+n[t<<2&63]+"="));return o.join("")};for(var n=[],i=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,u=a.length;s<u;++s)n[s]=a[s],i[a.charCodeAt(s)]=s;function l(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");return-1===r&&(r=t),[r,r===t?0:4-r%4]}function f(e,t,r){for(var i,o,a=[],s=t;s<r;s+=3)i=(e[s]<<16&16711680)+(e[s+1]<<8&65280)+(255&e[s+2]),a.push(n[(o=i)>>18&63]+n[o>>12&63]+n[o>>6&63]+n[63&o]);return a.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},{}],17:[function(e,t,r){var n=function(e){"use strict";var t=1e7,r=7,i=9007199254740992,o=d(i),a="0123456789abcdefghijklmnopqrstuvwxyz",s="function"==typeof BigInt;function u(e,t,r,n){return void 0===e?u[0]:void 0!==t&&(10!=+t||r)?Z(e,t,r,n):K(e)}function l(e,t){this.value=e,this.sign=t,this.isSmall=!1}function f(e){this.value=e,this.sign=e<0,this.isSmall=!0}function c(e){this.value=e}function h(e){return-i<e&&e<i}function d(e){return e<1e7?[e]:e<1e14?[e%1e7,Math.floor(e/1e7)]:[e%1e7,Math.floor(e/1e7)%1e7,Math.floor(e/1e14)]}function p(e){g(e);var r=e.length;if(r<4&&D(e,o)<0)switch(r){case 0:return 0;case 1:return e[0];case 2:return e[0]+e[1]*t;default:return e[0]+(e[1]+e[2]*t)*t}return e}function g(e){for(var t=e.length;0===e[--t];);e.length=t+1}function y(e){for(var t=new Array(e),r=-1;++r<e;)t[r]=0;return t}function b(e){return e>0?Math.floor(e):Math.ceil(e)}function m(e,r){var n,i,o=e.length,a=r.length,s=new Array(o),u=0,l=t;for(i=0;i<a;i++)u=(n=e[i]+r[i]+u)>=l?1:0,s[i]=n-u*l;for(;i<o;)u=(n=e[i]+u)===l?1:0,s[i++]=n-u*l;return u>0&&s.push(u),s}function v(e,t){return e.length>=t.length?m(e,t):m(t,e)}function w(e,r){var n,i,o=e.length,a=new Array(o),s=t;for(i=0;i<o;i++)n=e[i]-s+r,r=Math.floor(n/s),a[i]=n-r*s,r+=1;for(;r>0;)a[i++]=r%s,r=Math.floor(r/s);return a}function _(e,r){var n,i,o=e.length,a=r.length,s=new Array(o),u=0,l=t;for(n=0;n<a;n++)(i=e[n]-u-r[n])<0?(i+=l,u=1):u=0,s[n]=i;for(n=a;n<o;n++){if(!((i=e[n]-u)<0)){s[n++]=i;break}i+=l,s[n]=i}for(;n<o;n++)s[n]=e[n];return g(s),s}function E(e,r,n){var i,o,a=e.length,s=new Array(a),u=-r,c=t;for(i=0;i<a;i++)o=e[i]+u,u=Math.floor(o/c),o%=c,s[i]=o<0?o+c:o;return"number"==typeof(s=p(s))?(n&&(s=-s),new f(s)):new l(s,n)}function x(e,r){var n,i,o,a,s=e.length,u=r.length,l=y(s+u),f=t;for(o=0;o<s;++o){a=e[o];for(var c=0;c<u;++c)n=a*r[c]+l[o+c],i=Math.floor(n/f),l[o+c]=n-i*f,l[o+c+1]+=i}return g(l),l}function T(e,r){var n,i,o=e.length,a=new Array(o),s=t,u=0;for(i=0;i<o;i++)n=e[i]*r+u,u=Math.floor(n/s),a[i]=n-u*s;for(;u>0;)a[i++]=u%s,u=Math.floor(u/s);return a}function k(e,t){for(var r=[];t-- >0;)r.push(0);return r.concat(e)}function N(e,r,n){return new l(e<t?T(r,e):x(r,d(e)),n)}function S(e){var r,n,i,o,a=e.length,s=y(a+a),u=t;for(i=0;i<a;i++){n=0-(o=e[i])*o;for(var l=i;l<a;l++)r=o*e[l]*2+s[i+l]+n,n=Math.floor(r/u),s[i+l]=r-n*u;s[i+a]=n}return g(s),s}function I(e,r){var n,i,o,a,s=e.length,u=y(s),l=t;for(o=0,n=s-1;n>=0;--n)o=(a=o*l+e[n])-(i=b(a/r))*r,u[n]=0|i;return[u,0|o]}function A(e,r){var n,i=K(r);if(s)return[new c(e.value/i.value),new c(e.value%i.value)];var o,a=e.value,h=i.value;if(0===h)throw new Error("Cannot divide by zero");if(e.isSmall)return i.isSmall?[new f(b(a/h)),new f(a%h)]:[u[0],e];if(i.isSmall){if(1===h)return[e,u[0]];if(-1==h)return[e.negate(),u[0]];var m=Math.abs(h);if(m<t){o=p((n=I(a,m))[0]);var v=n[1];return e.sign&&(v=-v),"number"==typeof o?(e.sign!==i.sign&&(o=-o),[new f(o),new f(v)]):[new l(o,e.sign!==i.sign),new f(v)]}h=d(m)}var w=D(a,h);if(-1===w)return[u[0],e];if(0===w)return[u[e.sign===i.sign?1:-1],u[0]];o=(n=a.length+h.length<=200?function(e,r){var n,i,o,a,s,u,l,f=e.length,c=r.length,h=t,d=y(r.length),g=r[c-1],b=Math.ceil(h/(2*g)),m=T(e,b),v=T(r,b);for(m.length<=f&&m.push(0),v.push(0),g=v[c-1],i=f-c;i>=0;i--){for(n=h-1,m[i+c]!==g&&(n=Math.floor((m[i+c]*h+m[i+c-1])/g)),o=0,a=0,u=v.length,s=0;s<u;s++)o+=n*v[s],l=Math.floor(o/h),a+=m[i+s]-(o-l*h),o=l,a<0?(m[i+s]=a+h,a=-1):(m[i+s]=a,a=0);for(;0!==a;){for(n-=1,o=0,s=0;s<u;s++)(o+=m[i+s]-h+v[s])<0?(m[i+s]=o+h,o=0):(m[i+s]=o,o=1);a+=o}d[i]=n}return m=I(m,b)[0],[p(d),p(m)]}(a,h):function(e,r){for(var n,i,o,a,s,u=e.length,l=r.length,f=[],c=[],h=t;u;)if(c.unshift(e[--u]),g(c),D(c,r)<0)f.push(0);else{o=c[(i=c.length)-1]*h+c[i-2],a=r[l-1]*h+r[l-2],i>l&&(o=(o+1)*h),n=Math.ceil(o/a);do{if(D(s=T(r,n),c)<=0)break;n--}while(n);f.push(n),c=_(c,s)}return f.reverse(),[p(f),p(c)]}(a,h))[0];var E=e.sign!==i.sign,x=n[1],k=e.sign;return"number"==typeof o?(E&&(o=-o),o=new f(o)):o=new l(o,E),"number"==typeof x?(k&&(x=-x),x=new f(x)):x=new l(x,k),[o,x]}function D(e,t){if(e.length!==t.length)return e.length>t.length?1:-1;for(var r=e.length-1;r>=0;r--)if(e[r]!==t[r])return e[r]>t[r]?1:-1;return 0}function L(e){var t=e.abs();return!t.isUnit()&&(!!(t.equals(2)||t.equals(3)||t.equals(5))||!(t.isEven()||t.isDivisibleBy(3)||t.isDivisibleBy(5))&&(!!t.lesser(49)||void 0))}function O(e,t){for(var r,i,o,a=e.prev(),s=a,u=0;s.isEven();)s=s.divide(2),u++;e:for(i=0;i<t.length;i++)if(!e.lesser(t[i])&&!(o=n(t[i]).modPow(s,e)).isUnit()&&!o.equals(a)){for(r=u-1;0!=r;r--){if((o=o.square().mod(e)).isUnit())return!1;if(o.equals(a))continue e}return!1}return!0}l.prototype=Object.create(u.prototype),f.prototype=Object.create(u.prototype),c.prototype=Object.create(u.prototype),l.prototype.add=function(e){var t=K(e);if(this.sign!==t.sign)return this.subtract(t.negate());var r=this.value,n=t.value;return t.isSmall?new l(w(r,Math.abs(n)),this.sign):new l(v(r,n),this.sign)},l.prototype.plus=l.prototype.add,f.prototype.add=function(e){var t=K(e),r=this.value;if(r<0!==t.sign)return this.subtract(t.negate());var n=t.value;if(t.isSmall){if(h(r+n))return new f(r+n);n=d(Math.abs(n))}return new l(w(n,Math.abs(r)),r<0)},f.prototype.plus=f.prototype.add,c.prototype.add=function(e){return new c(this.value+K(e).value)},c.prototype.plus=c.prototype.add,l.prototype.subtract=function(e){var t=K(e);if(this.sign!==t.sign)return this.add(t.negate());var r=this.value,n=t.value;return t.isSmall?E(r,Math.abs(n),this.sign):function(e,t,r){var n;return D(e,t)>=0?n=_(e,t):(n=_(t,e),r=!r),"number"==typeof(n=p(n))?(r&&(n=-n),new f(n)):new l(n,r)}(r,n,this.sign)},l.prototype.minus=l.prototype.subtract,f.prototype.subtract=function(e){var t=K(e),r=this.value;if(r<0!==t.sign)return this.add(t.negate());var n=t.value;return t.isSmall?new f(r-n):E(n,Math.abs(r),r>=0)},f.prototype.minus=f.prototype.subtract,c.prototype.subtract=function(e){return new c(this.value-K(e).value)},c.prototype.minus=c.prototype.subtract,l.prototype.negate=function(){return new l(this.value,!this.sign)},f.prototype.negate=function(){var e=this.sign,t=new f(-this.value);return t.sign=!e,t},c.prototype.negate=function(){return new c(-this.value)},l.prototype.abs=function(){return new l(this.value,!1)},f.prototype.abs=function(){return new f(Math.abs(this.value))},c.prototype.abs=function(){return new c(this.value>=0?this.value:-this.value)},l.prototype.multiply=function(e){var r,n,i,o=K(e),a=this.value,s=o.value,f=this.sign!==o.sign;if(o.isSmall){if(0===s)return u[0];if(1===s)return this;if(-1===s)return this.negate();if((r=Math.abs(s))<t)return new l(T(a,r),f);s=d(r)}return n=a.length,i=s.length,new l(-.012*n-.012*i+15e-6*n*i>0?function e(t,r){var n=Math.max(t.length,r.length);if(n<=30)return x(t,r);n=Math.ceil(n/2);var i=t.slice(n),o=t.slice(0,n),a=r.slice(n),s=r.slice(0,n),u=e(o,s),l=e(i,a),f=v(v(u,k(_(_(e(v(o,i),v(s,a)),u),l),n)),k(l,2*n));return g(f),f}(a,s):x(a,s),f)},l.prototype.times=l.prototype.multiply,f.prototype._multiplyBySmall=function(e){return h(e.value*this.value)?new f(e.value*this.value):N(Math.abs(e.value),d(Math.abs(this.value)),this.sign!==e.sign)},l.prototype._multiplyBySmall=function(e){return 0===e.value?u[0]:1===e.value?this:-1===e.value?this.negate():N(Math.abs(e.value),this.value,this.sign!==e.sign)},f.prototype.multiply=function(e){return K(e)._multiplyBySmall(this)},f.prototype.times=f.prototype.multiply,c.prototype.multiply=function(e){return new c(this.value*K(e).value)},c.prototype.times=c.prototype.multiply,l.prototype.square=function(){return new l(S(this.value),!1)},f.prototype.square=function(){var e=this.value*this.value;return h(e)?new f(e):new l(S(d(Math.abs(this.value))),!1)},c.prototype.square=function(e){return new c(this.value*this.value)},l.prototype.divmod=function(e){var t=A(this,e);return{quotient:t[0],remainder:t[1]}},c.prototype.divmod=f.prototype.divmod=l.prototype.divmod,l.prototype.divide=function(e){return A(this,e)[0]},c.prototype.over=c.prototype.divide=function(e){return new c(this.value/K(e).value)},f.prototype.over=f.prototype.divide=l.prototype.over=l.prototype.divide,l.prototype.mod=function(e){return A(this,e)[1]},c.prototype.mod=c.prototype.remainder=function(e){return new c(this.value%K(e).value)},f.prototype.remainder=f.prototype.mod=l.prototype.remainder=l.prototype.mod,l.prototype.pow=function(e){var t,r,n,i=K(e),o=this.value,a=i.value;if(0===a)return u[1];if(0===o)return u[0];if(1===o)return u[1];if(-1===o)return i.isEven()?u[1]:u[-1];if(i.sign)return u[0];if(!i.isSmall)throw new Error("The exponent "+i.toString()+" is too large.");if(this.isSmall&&h(t=Math.pow(o,a)))return new f(b(t));for(r=this,n=u[1];!0&a&&(n=n.times(r),--a),0!==a;)a/=2,r=r.square();return n},f.prototype.pow=l.prototype.pow,c.prototype.pow=function(e){var t=K(e),r=this.value,n=t.value,i=BigInt(0),o=BigInt(1),a=BigInt(2);if(n===i)return u[1];if(r===i)return u[0];if(r===o)return u[1];if(r===BigInt(-1))return t.isEven()?u[1]:u[-1];if(t.isNegative())return new c(i);for(var s=this,l=u[1];(n&o)===o&&(l=l.times(s),--n),n!==i;)n/=a,s=s.square();return l},l.prototype.modPow=function(e,t){if(e=K(e),(t=K(t)).isZero())throw new Error("Cannot take modPow with modulus 0");var r=u[1],n=this.mod(t);for(e.isNegative()&&(e=e.multiply(u[-1]),n=n.modInv(t));e.isPositive();){if(n.isZero())return u[0];e.isOdd()&&(r=r.multiply(n).mod(t)),e=e.divide(2),n=n.square().mod(t)}return r},c.prototype.modPow=f.prototype.modPow=l.prototype.modPow,l.prototype.compareAbs=function(e){var t=K(e),r=this.value,n=t.value;return t.isSmall?1:D(r,n)},f.prototype.compareAbs=function(e){var t=K(e),r=Math.abs(this.value),n=t.value;return t.isSmall?r===(n=Math.abs(n))?0:r>n?1:-1:-1},c.prototype.compareAbs=function(e){var t=this.value,r=K(e).value;return(t=t>=0?t:-t)===(r=r>=0?r:-r)?0:t>r?1:-1},l.prototype.compare=function(e){if(e===1/0)return-1;if(e===-1/0)return 1;var t=K(e),r=this.value,n=t.value;return this.sign!==t.sign?t.sign?1:-1:t.isSmall?this.sign?-1:1:D(r,n)*(this.sign?-1:1)},l.prototype.compareTo=l.prototype.compare,f.prototype.compare=function(e){if(e===1/0)return-1;if(e===-1/0)return 1;var t=K(e),r=this.value,n=t.value;return t.isSmall?r==n?0:r>n?1:-1:r<0!==t.sign?r<0?-1:1:r<0?1:-1},f.prototype.compareTo=f.prototype.compare,c.prototype.compare=function(e){if(e===1/0)return-1;if(e===-1/0)return 1;var t=this.value,r=K(e).value;return t===r?0:t>r?1:-1},c.prototype.compareTo=c.prototype.compare,l.prototype.equals=function(e){return 0===this.compare(e)},c.prototype.eq=c.prototype.equals=f.prototype.eq=f.prototype.equals=l.prototype.eq=l.prototype.equals,l.prototype.notEquals=function(e){return 0!==this.compare(e)},c.prototype.neq=c.prototype.notEquals=f.prototype.neq=f.prototype.notEquals=l.prototype.neq=l.prototype.notEquals,l.prototype.greater=function(e){return this.compare(e)>0},c.prototype.gt=c.prototype.greater=f.prototype.gt=f.prototype.greater=l.prototype.gt=l.prototype.greater,l.prototype.lesser=function(e){return this.compare(e)<0},c.prototype.lt=c.prototype.lesser=f.prototype.lt=f.prototype.lesser=l.prototype.lt=l.prototype.lesser,l.prototype.greaterOrEquals=function(e){return this.compare(e)>=0},c.prototype.geq=c.prototype.greaterOrEquals=f.prototype.geq=f.prototype.greaterOrEquals=l.prototype.geq=l.prototype.greaterOrEquals,l.prototype.lesserOrEquals=function(e){return this.compare(e)<=0},c.prototype.leq=c.prototype.lesserOrEquals=f.prototype.leq=f.prototype.lesserOrEquals=l.prototype.leq=l.prototype.lesserOrEquals,l.prototype.isEven=function(){return 0==(1&this.value[0])},f.prototype.isEven=function(){return 0==(1&this.value)},c.prototype.isEven=function(){return(this.value&BigInt(1))===BigInt(0)},l.prototype.isOdd=function(){return 1==(1&this.value[0])},f.prototype.isOdd=function(){return 1==(1&this.value)},c.prototype.isOdd=function(){return(this.value&BigInt(1))===BigInt(1)},l.prototype.isPositive=function(){return!this.sign},f.prototype.isPositive=function(){return this.value>0},c.prototype.isPositive=f.prototype.isPositive,l.prototype.isNegative=function(){return this.sign},f.prototype.isNegative=function(){return this.value<0},c.prototype.isNegative=f.prototype.isNegative,l.prototype.isUnit=function(){return!1},f.prototype.isUnit=function(){return 1===Math.abs(this.value)},c.prototype.isUnit=function(){return this.abs().value===BigInt(1)},l.prototype.isZero=function(){return!1},f.prototype.isZero=function(){return 0===this.value},c.prototype.isZero=function(){return this.value===BigInt(0)},l.prototype.isDivisibleBy=function(e){var t=K(e);return!t.isZero()&&(!!t.isUnit()||(0===t.compareAbs(2)?this.isEven():this.mod(t).isZero()))},c.prototype.isDivisibleBy=f.prototype.isDivisibleBy=l.prototype.isDivisibleBy,l.prototype.isPrime=function(e){var t=L(this);if(void 0!==t)return t;var r=this.abs(),i=r.bitLength();if(i<=64)return O(r,[2,3,5,7,11,13,17,19,23,29,31,37]);for(var o=Math.log(2)*i.toJSNumber(),a=Math.ceil(!0===e?2*Math.pow(o,2):o),s=[],u=0;u<a;u++)s.push(n(u+2));return O(r,s)},c.prototype.isPrime=f.prototype.isPrime=l.prototype.isPrime,l.prototype.isProbablePrime=function(e,t){var r=L(this);if(void 0!==r)return r;for(var i=this.abs(),o=void 0===e?5:e,a=[],s=0;s<o;s++)a.push(n.randBetween(2,i.minus(2),t));return O(i,a)},c.prototype.isProbablePrime=f.prototype.isProbablePrime=l.prototype.isProbablePrime,l.prototype.modInv=function(e){for(var t,r,i,o=n.zero,a=n.one,s=K(e),u=this.abs();!u.isZero();)t=s.divide(u),r=o,i=s,o=a,s=u,a=r.subtract(t.multiply(a)),u=i.subtract(t.multiply(u));if(!s.isUnit())throw new Error(this.toString()+" and "+e.toString()+" are not co-prime");return-1===o.compare(0)&&(o=o.add(e)),this.isNegative()?o.negate():o},c.prototype.modInv=f.prototype.modInv=l.prototype.modInv,l.prototype.next=function(){var e=this.value;return this.sign?E(e,1,this.sign):new l(w(e,1),this.sign)},f.prototype.next=function(){var e=this.value;return e+1<i?new f(e+1):new l(o,!1)},c.prototype.next=function(){return new c(this.value+BigInt(1))},l.prototype.prev=function(){var e=this.value;return this.sign?new l(w(e,1),!0):E(e,1,this.sign)},f.prototype.prev=function(){var e=this.value;return e-1>-i?new f(e-1):new l(o,!0)},c.prototype.prev=function(){return new c(this.value-BigInt(1))};for(var M=[1];2*M[M.length-1]<=t;)M.push(2*M[M.length-1]);var U=M.length,C=M[U-1];function B(e){return Math.abs(e)<=t}function R(e,t,r){t=K(t);for(var i=e.isNegative(),o=t.isNegative(),a=i?e.not():e,s=o?t.not():t,u=0,l=0,f=null,c=null,h=[];!a.isZero()||!s.isZero();)u=(f=A(a,C))[1].toJSNumber(),i&&(u=C-1-u),l=(c=A(s,C))[1].toJSNumber(),o&&(l=C-1-l),a=f[0],s=c[0],h.push(r(u,l));for(var d=0!==r(i?1:0,o?1:0)?n(-1):n(0),p=h.length-1;p>=0;p-=1)d=d.multiply(C).add(n(h[p]));return d}l.prototype.shiftLeft=function(e){var t=K(e).toJSNumber();if(!B(t))throw new Error(String(t)+" is too large for shifting.");if(t<0)return this.shiftRight(-t);var r=this;if(r.isZero())return r;for(;t>=U;)r=r.multiply(C),t-=U-1;return r.multiply(M[t])},c.prototype.shiftLeft=f.prototype.shiftLeft=l.prototype.shiftLeft,l.prototype.shiftRight=function(e){var t,r=K(e).toJSNumber();if(!B(r))throw new Error(String(r)+" is too large for shifting.");if(r<0)return this.shiftLeft(-r);for(var n=this;r>=U;){if(n.isZero()||n.isNegative()&&n.isUnit())return n;n=(t=A(n,C))[1].isNegative()?t[0].prev():t[0],r-=U-1}return(t=A(n,M[r]))[1].isNegative()?t[0].prev():t[0]},c.prototype.shiftRight=f.prototype.shiftRight=l.prototype.shiftRight,l.prototype.not=function(){return this.negate().prev()},c.prototype.not=f.prototype.not=l.prototype.not,l.prototype.and=function(e){return R(this,e,function(e,t){return e&t})},c.prototype.and=f.prototype.and=l.prototype.and,l.prototype.or=function(e){return R(this,e,function(e,t){return e|t})},c.prototype.or=f.prototype.or=l.prototype.or,l.prototype.xor=function(e){return R(this,e,function(e,t){return e^t})},c.prototype.xor=f.prototype.xor=l.prototype.xor;var P=1<<30,j=(t&-t)*(t&-t)|P;function F(e){var r=e.value,n="number"==typeof r?r|P:"bigint"==typeof r?r|BigInt(P):r[0]+r[1]*t|j;return n&-n}function z(e,t){return e=K(e),t=K(t),e.greater(t)?e:t}function q(e,t){return e=K(e),t=K(t),e.lesser(t)?e:t}function X(e,t){if(e=K(e).abs(),t=K(t).abs(),e.equals(t))return e;if(e.isZero())return t;if(t.isZero())return e;for(var r,n,i=u[1];e.isEven()&&t.isEven();)r=q(F(e),F(t)),e=e.divide(r),t=t.divide(r),i=i.multiply(r);for(;e.isEven();)e=e.divide(F(e));do{for(;t.isEven();)t=t.divide(F(t));e.greater(t)&&(n=t,t=e,e=n),t=t.subtract(e)}while(!t.isZero());return i.isUnit()?e:e.multiply(i)}l.prototype.bitLength=function(){var e=this;return e.compareTo(n(0))<0&&(e=e.negate().subtract(n(1))),0===e.compareTo(n(0))?n(0):n(function e(t,r){if(r.compareTo(t)<=0){var i=e(t,r.square(r)),o=i.p,a=i.e,s=o.multiply(r);return s.compareTo(t)<=0?{p:s,e:2*a+1}:{p:o,e:2*a}}return{p:n(1),e:0}}(e,n(2)).e).add(n(1))},c.prototype.bitLength=f.prototype.bitLength=l.prototype.bitLength;var Z=function(e,t,r,n){r=r||a,e=String(e),n||(e=e.toLowerCase(),r=r.toLowerCase());var i,o=e.length,s=Math.abs(t),u={};for(i=0;i<r.length;i++)u[r[i]]=i;for(i=0;i<o;i++){if("-"!==(c=e[i])&&(c in u&&u[c]>=s)){if("1"===c&&1===s)continue;throw new Error(c+" is not a valid digit in base "+t+".")}}t=K(t);var l=[],f="-"===e[0];for(i=f?1:0;i<e.length;i++){var c;if((c=e[i])in u)l.push(K(u[c]));else{if("<"!==c)throw new Error(c+" is not a valid character");var h=i;do{i++}while(">"!==e[i]&&i<e.length);l.push(K(e.slice(h+1,i)))}}return V(l,t,f)};function V(e,t,r){var n,i=u[0],o=u[1];for(n=e.length-1;n>=0;n--)i=i.add(e[n].times(o)),o=o.times(t);return r?i.negate():i}function H(e,t){if((t=n(t)).isZero()){if(e.isZero())return{value:[0],isNegative:!1};throw new Error("Cannot convert nonzero numbers to base 0.")}if(t.equals(-1)){if(e.isZero())return{value:[0],isNegative:!1};if(e.isNegative())return{value:[].concat.apply([],Array.apply(null,Array(-e.toJSNumber())).map(Array.prototype.valueOf,[1,0])),isNegative:!1};var r=Array.apply(null,Array(e.toJSNumber()-1)).map(Array.prototype.valueOf,[0,1]);return r.unshift([1]),{value:[].concat.apply([],r),isNegative:!1}}var i=!1;if(e.isNegative()&&t.isPositive()&&(i=!0,e=e.abs()),t.isUnit())return e.isZero()?{value:[0],isNegative:!1}:{value:Array.apply(null,Array(e.toJSNumber())).map(Number.prototype.valueOf,1),isNegative:i};for(var o,a=[],s=e;s.isNegative()||s.compareAbs(t)>=0;){s=(o=s.divmod(t)).quotient;var u=o.remainder;u.isNegative()&&(u=t.minus(u).abs(),s=s.next()),a.push(u.toJSNumber())}return a.push(s.toJSNumber()),{value:a.reverse(),isNegative:i}}function W(e,t,r){var n=H(e,t);return(n.isNegative?"-":"")+n.value.map(function(e){return function(e,t){return e<(t=t||a).length?t[e]:"<"+e+">"}(e,r)}).join("")}function G(e){if(h(+e)){var t=+e;if(t===b(t))return s?new c(BigInt(t)):new f(t);throw new Error("Invalid integer: "+e)}var n="-"===e[0];n&&(e=e.slice(1));var i=e.split(/e/i);if(i.length>2)throw new Error("Invalid integer: "+i.join("e"));if(2===i.length){var o=i[1];if("+"===o[0]&&(o=o.slice(1)),(o=+o)!==b(o)||!h(o))throw new Error("Invalid integer: "+o+" is not a valid exponent.");var a=i[0],u=a.indexOf(".");if(u>=0&&(o-=a.length-u-1,a=a.slice(0,u)+a.slice(u+1)),o<0)throw new Error("Cannot include negative exponent part for integers");e=a+=new Array(o+1).join("0")}if(!/^([0-9][0-9]*)$/.test(e))throw new Error("Invalid integer: "+e);if(s)return new c(BigInt(n?"-"+e:e));for(var d=[],p=e.length,y=r,m=p-y;p>0;)d.push(+e.slice(m,p)),(m-=y)<0&&(m=0),p-=y;return g(d),new l(d,n)}function K(e){return"number"==typeof e?function(e){if(s)return new c(BigInt(e));if(h(e)){if(e!==b(e))throw new Error(e+" is not an integer.");return new f(e)}return G(e.toString())}(e):"string"==typeof e?G(e):"bigint"==typeof e?new c(e):e}l.prototype.toArray=function(e){return H(this,e)},f.prototype.toArray=function(e){return H(this,e)},c.prototype.toArray=function(e){return H(this,e)},l.prototype.toString=function(e,t){if(void 0===e&&(e=10),10!==e||t)return W(this,e,t);for(var r,n=this.value,i=n.length,o=String(n[--i]);--i>=0;)r=String(n[i]),o+="0000000".slice(r.length)+r;return(this.sign?"-":"")+o},f.prototype.toString=function(e,t){return void 0===e&&(e=10),10!=e||t?W(this,e,t):String(this.value)},c.prototype.toString=f.prototype.toString,c.prototype.toJSON=l.prototype.toJSON=f.prototype.toJSON=function(){return this.toString()},l.prototype.valueOf=function(){return parseInt(this.toString(),10)},l.prototype.toJSNumber=l.prototype.valueOf,f.prototype.valueOf=function(){return this.value},f.prototype.toJSNumber=f.prototype.valueOf,c.prototype.valueOf=c.prototype.toJSNumber=function(){return parseInt(this.toString(),10)};for(var $=0;$<1e3;$++)u[$]=K($),$>0&&(u[-$]=K(-$));return u.one=u[1],u.zero=u[0],u.minusOne=u[-1],u.max=z,u.min=q,u.gcd=X,u.lcm=function(e,t){return e=K(e).abs(),t=K(t).abs(),e.divide(X(e,t)).multiply(t)},u.isInstance=function(e){return e instanceof l||e instanceof f||e instanceof c},u.randBetween=function(e,r,n){e=K(e),r=K(r);var i=n||Math.random,o=q(e,r),a=z(e,r).subtract(o).add(1);if(a.isSmall)return o.add(Math.floor(i()*a));for(var s=H(a,t).value,l=[],f=!0,c=0;c<s.length;c++){var h=f?s[c]+(c+1<s.length?s[c+1]/t:0):t,d=b(i()*h);l.push(d),d<s[c]&&(f=!1)}return o.add(u.fromArray(l,t,!1))},u.fromArray=function(e,t,r){return V(e.map(K),K(t||10),r)},u}();void 0!==t&&t.hasOwnProperty("exports")&&(t.exports=n)},{}],18:[function(e,t,r){(function(t){(function(){"use strict";const n=e("fs"),i=e("big-integer"),o=!1;r.maxObjectSize=1e8,r.maxObjectCount=32768;const a=9783072e5,s=r.UID=function(e){this.UID=e},u=(r.parseFile=function(e,r){return new Promise(function(i,o){function a(e){let t,n=null;try{t=u(e),i(t)}catch(e){o(n=e)}finally{r&&r(n,t)}}if(t.isBuffer(e))return a(e);n.readFile(e,function(e,t){if(e)return o(e),r(e);a(t)})})},r.parseBuffer=function(e){if("bplist"!==e.slice(0,"bplist".length).toString("utf8"))throw new Error("Invalid binary plist. Expected 'bplist' at offset 0.");const n=e.slice(e.length-32,e.length),u=n.readUInt8(6);o&&console.log("offsetSize: "+u);const c=n.readUInt8(7);o&&console.log("objectRefSize: "+c);const h=f(n,8);o&&console.log("numObjects: "+h);const d=f(n,16);o&&console.log("topObject: "+d);const p=f(n,24);if(o&&console.log("offsetTableOffset: "+p),h>r.maxObjectCount)throw new Error("maxObjectCount exceeded");const g=[];for(let t=0;t<h;t++){const r=e.slice(p+t*u,p+(t+1)*u);g[t]=l(r,0),o&&console.log("Offset for Object #"+t+" is "+g[t]+" ["+g[t].toString(16)+"]")}return[function n(u){const f=g[u],h=e[f],d=(240&h)>>4,p=15&h;switch(d){case 0:return function(){switch(p){case 0:return null;case 8:return!1;case 9:return!0;case 15:return null;default:throw new Error("Unhandled simple type 0x"+d.toString(16))}}();case 1:return function(){const t=Math.pow(2,p);if(4==p){const r=e.slice(f+1,f+1+t),n=function(e){let t,r="";for(t=0;t<e.length&&0==e[t];t++);for(;t<e.length;t++){const n="00"+e[t].toString(16);r+=n.substr(n.length-2)}return r}(r);return i(n,16)}if(3==p)return e.readInt32BE(f+1);if(t<r.maxObjectSize)return l(e.slice(f+1,f+1+t));throw new Error("To little heap space available! Wanted to read "+t+" bytes, but only "+r.maxObjectSize+" are available.")}();case 8:return function(){const t=p+1;if(t<r.maxObjectSize)return new s(l(e.slice(f+1,f+1+t)));throw new Error("To little heap space available! Wanted to read "+t+" bytes, but only "+r.maxObjectSize+" are available.")}();case 2:return function(){const t=Math.pow(2,p);if(!(t<r.maxObjectSize))throw new Error("To little heap space available! Wanted to read "+t+" bytes, but only "+r.maxObjectSize+" are available.");{const r=e.slice(f+1,f+1+t);if(4===t)return r.readFloatBE(0);if(8===t)return r.readDoubleBE(0)}}();case 3:return function(){3!=p&&console.error("Unknown date type :"+p+". Parsing anyway...");const t=e.slice(f+1,f+9);return new Date(a+1e3*t.readDoubleBE(0))}();case 4:return function(){let t=1,n=p;if(15==p){const r=e[f+1],i=(240&r)/16;1!=i&&console.error("0x4: UNEXPECTED LENGTH-INT TYPE! "+i);const o=15&r,a=Math.pow(2,o);t=2+a,n=l(e.slice(f+2,f+2+a))}if(n<r.maxObjectSize)return e.slice(f+t,f+t+n);throw new Error("To little heap space available! Wanted to read "+n+" bytes, but only "+r.maxObjectSize+" are available.")}();case 5:return y();case 6:return y(!0);case 10:return function(){let t=p,i=1;if(15==p){const r=e[f+1],n=(240&r)/16;1!=n&&console.error("0xa: UNEXPECTED LENGTH-INT TYPE! "+n);const o=15&r,a=Math.pow(2,o);i=2+a,t=l(e.slice(f+2,f+2+a))}if(t*c>r.maxObjectSize)throw new Error("To little heap space available!");const o=[];for(let r=0;r<t;r++){const t=l(e.slice(f+i+r*c,f+i+(r+1)*c));o[r]=n(t)}return o}();case 13:return function(){let t=p,i=1;if(15==p){const r=e[f+1],n=(240&r)/16;1!=n&&console.error("0xD: UNEXPECTED LENGTH-INT TYPE! "+n);const o=15&r,a=Math.pow(2,o);i=2+a,t=l(e.slice(f+2,f+2+a))}if(2*t*c>r.maxObjectSize)throw new Error("To little heap space available!");o&&console.log("Parsing dictionary #"+u);const a={};for(let r=0;r<t;r++){const s=l(e.slice(f+i+r*c,f+i+(r+1)*c)),h=l(e.slice(f+i+t*c+r*c,f+i+t*c+(r+1)*c)),d=n(s),p=n(h);o&&console.log("  DICT #"+u+": Mapped "+d+" to "+p),a[d]=p}return a}();default:throw new Error("Unhandled type 0x"+d.toString(16))}function y(n){n=n||0;let i="utf8",o=p,a=1;if(15==p){const t=e[f+1],r=(240&t)/16;1!=r&&console.err("UNEXPECTED LENGTH-INT TYPE! "+r);const n=15&t,i=Math.pow(2,n);a=2+i,o=l(e.slice(f+2,f+2+i))}if((o*=n+1)<r.maxObjectSize){let r=t.from(e.slice(f+a,f+a+o));return n&&(r=function(e){const t=e.length;for(let r=0;r<t;r+=2){const t=e[r];e[r]=e[r+1],e[r+1]=t}return e}(r),i="ucs2"),r.toString(i)}throw new Error("To little heap space available! Wanted to read "+o+" bytes, but only "+r.maxObjectSize+" are available.")}}(d)]});function l(e,t){let r=0;for(let n=t=t||0;n<e.length;n++)r<<=8,r|=255&e[n];return r}function f(e,t){return e.slice(t,t+8).readUInt32BE(4,8)}}).call(this)}).call(this,e("buffer").Buffer)},{"big-integer":17,buffer:23,fs:22}],19:[function(e,t,r){},{}],20:[function(e,t,r){(function(t,n){(function(){"use strict";var i=e("assert"),o=e("pako/lib/zlib/zstream"),a=e("pako/lib/zlib/deflate.js"),s=e("pako/lib/zlib/inflate.js"),u=e("pako/lib/zlib/constants");for(var l in u)r[l]=u[l];r.NONE=0,r.DEFLATE=1,r.INFLATE=2,r.GZIP=3,r.GUNZIP=4,r.DEFLATERAW=5,r.INFLATERAW=6,r.UNZIP=7;function f(e){if("number"!=typeof e||e<r.DEFLATE||e>r.UNZIP)throw new TypeError("Bad argument");this.dictionary=null,this.err=0,this.flush=0,this.init_done=!1,this.level=0,this.memLevel=0,this.mode=e,this.strategy=0,this.windowBits=0,this.write_in_progress=!1,this.pending_close=!1,this.gzip_id_bytes_read=0}f.prototype.close=function(){this.write_in_progress?this.pending_close=!0:(this.pending_close=!1,i(this.init_done,"close before init"),i(this.mode<=r.UNZIP),this.mode===r.DEFLATE||this.mode===r.GZIP||this.mode===r.DEFLATERAW?a.deflateEnd(this.strm):this.mode!==r.INFLATE&&this.mode!==r.GUNZIP&&this.mode!==r.INFLATERAW&&this.mode!==r.UNZIP||s.inflateEnd(this.strm),this.mode=r.NONE,this.dictionary=null)},f.prototype.write=function(e,t,r,n,i,o,a){return this._write(!0,e,t,r,n,i,o,a)},f.prototype.writeSync=function(e,t,r,n,i,o,a){return this._write(!1,e,t,r,n,i,o,a)},f.prototype._write=function(e,o,a,s,u,l,f,c){if(i.equal(arguments.length,8),i(this.init_done,"write before init"),i(this.mode!==r.NONE,"already finalized"),i.equal(!1,this.write_in_progress,"write already in progress"),i.equal(!1,this.pending_close,"close is pending"),this.write_in_progress=!0,i.equal(!1,void 0===o,"must provide flush value"),this.write_in_progress=!0,o!==r.Z_NO_FLUSH&&o!==r.Z_PARTIAL_FLUSH&&o!==r.Z_SYNC_FLUSH&&o!==r.Z_FULL_FLUSH&&o!==r.Z_FINISH&&o!==r.Z_BLOCK)throw new Error("Invalid flush value");if(null==a&&(a=n.alloc(0),u=0,s=0),this.strm.avail_in=u,this.strm.input=a,this.strm.next_in=s,this.strm.avail_out=c,this.strm.output=l,this.strm.next_out=f,this.flush=o,!e)return this._process(),this._checkError()?this._afterSync():void 0;var h=this;return t.nextTick(function(){h._process(),h._after()}),this},f.prototype._afterSync=function(){var e=this.strm.avail_out,t=this.strm.avail_in;return this.write_in_progress=!1,[t,e]},f.prototype._process=function(){var e=null;switch(this.mode){case r.DEFLATE:case r.GZIP:case r.DEFLATERAW:this.err=a.deflate(this.strm,this.flush);break;case r.UNZIP:switch(this.strm.avail_in>0&&(e=this.strm.next_in),this.gzip_id_bytes_read){case 0:if(null===e)break;if(31!==this.strm.input[e]){this.mode=r.INFLATE;break}if(this.gzip_id_bytes_read=1,e++,1===this.strm.avail_in)break;case 1:if(null===e)break;139===this.strm.input[e]?(this.gzip_id_bytes_read=2,this.mode=r.GUNZIP):this.mode=r.INFLATE;break;default:throw new Error("invalid number of gzip magic number bytes read")}case r.INFLATE:case r.GUNZIP:case r.INFLATERAW:for(this.err=s.inflate(this.strm,this.flush),this.err===r.Z_NEED_DICT&&this.dictionary&&(this.err=s.inflateSetDictionary(this.strm,this.dictionary),this.err===r.Z_OK?this.err=s.inflate(this.strm,this.flush):this.err===r.Z_DATA_ERROR&&(this.err=r.Z_NEED_DICT));this.strm.avail_in>0&&this.mode===r.GUNZIP&&this.err===r.Z_STREAM_END&&0!==this.strm.next_in[0];)this.reset(),this.err=s.inflate(this.strm,this.flush);break;default:throw new Error("Unknown mode "+this.mode)}},f.prototype._checkError=function(){switch(this.err){case r.Z_OK:case r.Z_BUF_ERROR:if(0!==this.strm.avail_out&&this.flush===r.Z_FINISH)return this._error("unexpected end of file"),!1;break;case r.Z_STREAM_END:break;case r.Z_NEED_DICT:return null==this.dictionary?this._error("Missing dictionary"):this._error("Bad dictionary"),!1;default:return this._error("Zlib error"),!1}return!0},f.prototype._after=function(){if(this._checkError()){var e=this.strm.avail_out,t=this.strm.avail_in;this.write_in_progress=!1,this.callback(t,e),this.pending_close&&this.close()}},f.prototype._error=function(e){this.strm.msg&&(e=this.strm.msg),this.onerror(e,this.err),this.write_in_progress=!1,this.pending_close&&this.close()},f.prototype.init=function(e,t,n,o,a){i(4===arguments.length||5===arguments.length,"init(windowBits, level, memLevel, strategy, [dictionary])"),i(e>=8&&e<=15,"invalid windowBits"),i(t>=-1&&t<=9,"invalid compression level"),i(n>=1&&n<=9,"invalid memlevel"),i(o===r.Z_FILTERED||o===r.Z_HUFFMAN_ONLY||o===r.Z_RLE||o===r.Z_FIXED||o===r.Z_DEFAULT_STRATEGY,"invalid strategy"),this._init(t,e,n,o,a),this._setDictionary()},f.prototype.params=function(){throw new Error("deflateParams Not supported")},f.prototype.reset=function(){this._reset(),this._setDictionary()},f.prototype._init=function(e,t,n,i,u){switch(this.level=e,this.windowBits=t,this.memLevel=n,this.strategy=i,this.flush=r.Z_NO_FLUSH,this.err=r.Z_OK,this.mode!==r.GZIP&&this.mode!==r.GUNZIP||(this.windowBits+=16),this.mode===r.UNZIP&&(this.windowBits+=32),this.mode!==r.DEFLATERAW&&this.mode!==r.INFLATERAW||(this.windowBits=-1*this.windowBits),this.strm=new o,this.mode){case r.DEFLATE:case r.GZIP:case r.DEFLATERAW:this.err=a.deflateInit2(this.strm,this.level,r.Z_DEFLATED,this.windowBits,this.memLevel,this.strategy);break;case r.INFLATE:case r.GUNZIP:case r.INFLATERAW:case r.UNZIP:this.err=s.inflateInit2(this.strm,this.windowBits);break;default:throw new Error("Unknown mode "+this.mode)}this.err!==r.Z_OK&&this._error("Init error"),this.dictionary=u,this.write_in_progress=!1,this.init_done=!0},f.prototype._setDictionary=function(){if(null!=this.dictionary){switch(this.err=r.Z_OK,this.mode){case r.DEFLATE:case r.DEFLATERAW:this.err=a.deflateSetDictionary(this.strm,this.dictionary)}this.err!==r.Z_OK&&this._error("Failed to set dictionary")}},f.prototype._reset=function(){switch(this.err=r.Z_OK,this.mode){case r.DEFLATE:case r.DEFLATERAW:case r.GZIP:this.err=a.deflateReset(this.strm);break;case r.INFLATE:case r.INFLATERAW:case r.GUNZIP:this.err=s.inflateReset(this.strm)}this.err!==r.Z_OK&&this._error("Failed to reset stream")},r.Zlib=f}).call(this)}).call(this,e("_process"),e("buffer").Buffer)},{_process:97,assert:15,buffer:23,"pako/lib/zlib/constants":83,"pako/lib/zlib/deflate.js":85,"pako/lib/zlib/inflate.js":87,"pako/lib/zlib/zstream":91}],21:[function(e,t,r){(function(t){(function(){"use strict";var n=e("buffer").Buffer,i=e("stream").Transform,o=e("./binding"),a=e("util"),s=e("assert").ok,u=e("buffer").kMaxLength,l="Cannot create final Buffer. It would be larger than 0x"+u.toString(16)+" bytes";o.Z_MIN_WINDOWBITS=8,o.Z_MAX_WINDOWBITS=15,o.Z_DEFAULT_WINDOWBITS=15,o.Z_MIN_CHUNK=64,o.Z_MAX_CHUNK=1/0,o.Z_DEFAULT_CHUNK=16384,o.Z_MIN_MEMLEVEL=1,o.Z_MAX_MEMLEVEL=9,o.Z_DEFAULT_MEMLEVEL=8,o.Z_MIN_LEVEL=-1,o.Z_MAX_LEVEL=9,o.Z_DEFAULT_LEVEL=o.Z_DEFAULT_COMPRESSION;for(var f=Object.keys(o),c=0;c<f.length;c++){var h=f[c];h.match(/^Z/)&&Object.defineProperty(r,h,{enumerable:!0,value:o[h],writable:!1})}for(var d={Z_OK:o.Z_OK,Z_STREAM_END:o.Z_STREAM_END,Z_NEED_DICT:o.Z_NEED_DICT,Z_ERRNO:o.Z_ERRNO,Z_STREAM_ERROR:o.Z_STREAM_ERROR,Z_DATA_ERROR:o.Z_DATA_ERROR,Z_MEM_ERROR:o.Z_MEM_ERROR,Z_BUF_ERROR:o.Z_BUF_ERROR,Z_VERSION_ERROR:o.Z_VERSION_ERROR},p=Object.keys(d),g=0;g<p.length;g++){var y=p[g];d[d[y]]=y}function b(e,t,r){var i=[],o=0;function a(){for(var t;null!==(t=e.read());)i.push(t),o+=t.length;e.once("readable",a)}function s(){var t,a=null;o>=u?a=new RangeError(l):t=n.concat(i,o),i=[],e.close(),r(a,t)}e.on("error",function(t){e.removeListener("end",s),e.removeListener("readable",a),r(t)}),e.on("end",s),e.end(t),a()}function m(e,t){if("string"==typeof t&&(t=n.from(t)),!n.isBuffer(t))throw new TypeError("Not a string or buffer");var r=e._finishFlushFlag;return e._processChunk(t,r)}function v(e){if(!(this instanceof v))return new v(e);S.call(this,e,o.DEFLATE)}function w(e){if(!(this instanceof w))return new w(e);S.call(this,e,o.INFLATE)}function _(e){if(!(this instanceof _))return new _(e);S.call(this,e,o.GZIP)}function E(e){if(!(this instanceof E))return new E(e);S.call(this,e,o.GUNZIP)}function x(e){if(!(this instanceof x))return new x(e);S.call(this,e,o.DEFLATERAW)}function T(e){if(!(this instanceof T))return new T(e);S.call(this,e,o.INFLATERAW)}function k(e){if(!(this instanceof k))return new k(e);S.call(this,e,o.UNZIP)}function N(e){return e===o.Z_NO_FLUSH||e===o.Z_PARTIAL_FLUSH||e===o.Z_SYNC_FLUSH||e===o.Z_FULL_FLUSH||e===o.Z_FINISH||e===o.Z_BLOCK}function S(e,t){var a=this;if(this._opts=e=e||{},this._chunkSize=e.chunkSize||r.Z_DEFAULT_CHUNK,i.call(this,e),e.flush&&!N(e.flush))throw new Error("Invalid flush flag: "+e.flush);if(e.finishFlush&&!N(e.finishFlush))throw new Error("Invalid flush flag: "+e.finishFlush);if(this._flushFlag=e.flush||o.Z_NO_FLUSH,this._finishFlushFlag=void 0!==e.finishFlush?e.finishFlush:o.Z_FINISH,e.chunkSize&&(e.chunkSize<r.Z_MIN_CHUNK||e.chunkSize>r.Z_MAX_CHUNK))throw new Error("Invalid chunk size: "+e.chunkSize);if(e.windowBits&&(e.windowBits<r.Z_MIN_WINDOWBITS||e.windowBits>r.Z_MAX_WINDOWBITS))throw new Error("Invalid windowBits: "+e.windowBits);if(e.level&&(e.level<r.Z_MIN_LEVEL||e.level>r.Z_MAX_LEVEL))throw new Error("Invalid compression level: "+e.level);if(e.memLevel&&(e.memLevel<r.Z_MIN_MEMLEVEL||e.memLevel>r.Z_MAX_MEMLEVEL))throw new Error("Invalid memLevel: "+e.memLevel);if(e.strategy&&e.strategy!=r.Z_FILTERED&&e.strategy!=r.Z_HUFFMAN_ONLY&&e.strategy!=r.Z_RLE&&e.strategy!=r.Z_FIXED&&e.strategy!=r.Z_DEFAULT_STRATEGY)throw new Error("Invalid strategy: "+e.strategy);if(e.dictionary&&!n.isBuffer(e.dictionary))throw new Error("Invalid dictionary: it should be a Buffer instance");this._handle=new o.Zlib(t);var s=this;this._hadError=!1,this._handle.onerror=function(e,t){I(s),s._hadError=!0;var n=new Error(e);n.errno=t,n.code=r.codes[t],s.emit("error",n)};var u=r.Z_DEFAULT_COMPRESSION;"number"==typeof e.level&&(u=e.level);var l=r.Z_DEFAULT_STRATEGY;"number"==typeof e.strategy&&(l=e.strategy),this._handle.init(e.windowBits||r.Z_DEFAULT_WINDOWBITS,u,e.memLevel||r.Z_DEFAULT_MEMLEVEL,l,e.dictionary),this._buffer=n.allocUnsafe(this._chunkSize),this._offset=0,this._level=u,this._strategy=l,this.once("end",this.close),Object.defineProperty(this,"_closed",{get:function(){return!a._handle},configurable:!0,enumerable:!0})}function I(e,r){r&&t.nextTick(r),e._handle&&(e._handle.close(),e._handle=null)}function A(e){e.emit("close")}Object.defineProperty(r,"codes",{enumerable:!0,value:Object.freeze(d),writable:!1}),r.Deflate=v,r.Inflate=w,r.Gzip=_,r.Gunzip=E,r.DeflateRaw=x,r.InflateRaw=T,r.Unzip=k,r.createDeflate=function(e){return new v(e)},r.createInflate=function(e){return new w(e)},r.createDeflateRaw=function(e){return new x(e)},r.createInflateRaw=function(e){return new T(e)},r.createGzip=function(e){return new _(e)},r.createGunzip=function(e){return new E(e)},r.createUnzip=function(e){return new k(e)},r.deflate=function(e,t,r){return"function"==typeof t&&(r=t,t={}),b(new v(t),e,r)},r.deflateSync=function(e,t){return m(new v(t),e)},r.gzip=function(e,t,r){return"function"==typeof t&&(r=t,t={}),b(new _(t),e,r)},r.gzipSync=function(e,t){return m(new _(t),e)},r.deflateRaw=function(e,t,r){return"function"==typeof t&&(r=t,t={}),b(new x(t),e,r)},r.deflateRawSync=function(e,t){return m(new x(t),e)},r.unzip=function(e,t,r){return"function"==typeof t&&(r=t,t={}),b(new k(t),e,r)},r.unzipSync=function(e,t){return m(new k(t),e)},r.inflate=function(e,t,r){return"function"==typeof t&&(r=t,t={}),b(new w(t),e,r)},r.inflateSync=function(e,t){return m(new w(t),e)},r.gunzip=function(e,t,r){return"function"==typeof t&&(r=t,t={}),b(new E(t),e,r)},r.gunzipSync=function(e,t){return m(new E(t),e)},r.inflateRaw=function(e,t,r){return"function"==typeof t&&(r=t,t={}),b(new T(t),e,r)},r.inflateRawSync=function(e,t){return m(new T(t),e)},a.inherits(S,i),S.prototype.params=function(e,n,i){if(e<r.Z_MIN_LEVEL||e>r.Z_MAX_LEVEL)throw new RangeError("Invalid compression level: "+e);if(n!=r.Z_FILTERED&&n!=r.Z_HUFFMAN_ONLY&&n!=r.Z_RLE&&n!=r.Z_FIXED&&n!=r.Z_DEFAULT_STRATEGY)throw new TypeError("Invalid strategy: "+n);if(this._level!==e||this._strategy!==n){var a=this;this.flush(o.Z_SYNC_FLUSH,function(){s(a._handle,"zlib binding closed"),a._handle.params(e,n),a._hadError||(a._level=e,a._strategy=n,i&&i())})}else t.nextTick(i)},S.prototype.reset=function(){return s(this._handle,"zlib binding closed"),this._handle.reset()},S.prototype._flush=function(e){this._transform(n.alloc(0),"",e)},S.prototype.flush=function(e,r){var i=this,a=this._writableState;("function"==typeof e||void 0===e&&!r)&&(r=e,e=o.Z_FULL_FLUSH),a.ended?r&&t.nextTick(r):a.ending?r&&this.once("end",r):a.needDrain?r&&this.once("drain",function(){return i.flush(e,r)}):(this._flushFlag=e,this.write(n.alloc(0),"",r))},S.prototype.close=function(e){I(this,e),t.nextTick(A,this)},S.prototype._transform=function(e,t,r){var i,a=this._writableState,s=(a.ending||a.ended)&&(!e||a.length===e.length);return null===e||n.isBuffer(e)?this._handle?(s?i=this._finishFlushFlag:(i=this._flushFlag,e.length>=a.length&&(this._flushFlag=this._opts.flush||o.Z_NO_FLUSH)),void this._processChunk(e,i,r)):r(new Error("zlib binding closed")):r(new Error("invalid input"))},S.prototype._processChunk=function(e,t,r){var i=e&&e.length,o=this._chunkSize-this._offset,a=0,f=this,c="function"==typeof r;if(!c){var h,d=[],p=0;this.on("error",function(e){h=e}),s(this._handle,"zlib binding closed");do{var g=this._handle.writeSync(t,e,a,i,this._buffer,this._offset,o)}while(!this._hadError&&m(g[0],g[1]));if(this._hadError)throw h;if(p>=u)throw I(this),new RangeError(l);var y=n.concat(d,p);return I(this),y}s(this._handle,"zlib binding closed");var b=this._handle.write(t,e,a,i,this._buffer,this._offset,o);function m(u,l){if(this&&(this.buffer=null,this.callback=null),!f._hadError){var h=o-l;if(s(h>=0,"have should not go down"),h>0){var g=f._buffer.slice(f._offset,f._offset+h);f._offset+=h,c?f.push(g):(d.push(g),p+=g.length)}if((0===l||f._offset>=f._chunkSize)&&(o=f._chunkSize,f._offset=0,f._buffer=n.allocUnsafe(f._chunkSize)),0===l){if(a+=i-u,i=u,!c)return!0;var y=f._handle.write(t,e,a,i,f._buffer,f._offset,f._chunkSize);return y.callback=m,void(y.buffer=e)}if(!c)return!1;r()}}b.buffer=e,b.callback=m},a.inherits(v,S),a.inherits(w,S),a.inherits(_,S),a.inherits(E,S),a.inherits(x,S),a.inherits(T,S),a.inherits(k,S)}).call(this)}).call(this,e("_process"))},{"./binding":20,_process:97,assert:15,buffer:23,stream:114,util:122}],22:[function(e,t,r){arguments[4][19][0].apply(r,arguments)},{dup:19}],23:[function(e,t,r){(function(t){(function(){"use strict";var t=e("base64-js"),n=e("ieee754");r.Buffer=a,r.SlowBuffer=function(e){+e!=e&&(e=0);return a.alloc(+e)},r.INSPECT_MAX_BYTES=50;var i=2147483647;function o(e){if(e>i)throw new RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return t.__proto__=a.prototype,t}function a(e,t,r){if("number"==typeof e){if("string"==typeof t)throw new TypeError('The "string" argument must be of type string. Received type number');return l(e)}return s(e,t,r)}function s(e,t,r){if("string"==typeof e)return function(e,t){"string"==typeof t&&""!==t||(t="utf8");if(!a.isEncoding(t))throw new TypeError("Unknown encoding: "+t);var r=0|h(e,t),n=o(r),i=n.write(e,t);i!==r&&(n=n.slice(0,i));return n}(e,t);if(ArrayBuffer.isView(e))return f(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(j(e,ArrayBuffer)||e&&j(e.buffer,ArrayBuffer))return function(e,t,r){if(t<0||e.byteLength<t)throw new RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw new RangeError('"length" is outside of buffer bounds');var n;n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r);return n.__proto__=a.prototype,n}(e,t,r);if("number"==typeof e)throw new TypeError('The "value" argument must not be of type number. Received type number');var n=e.valueOf&&e.valueOf();if(null!=n&&n!==e)return a.from(n,t,r);var i=function(e){if(a.isBuffer(e)){var t=0|c(e.length),r=o(t);return 0===r.length?r:(e.copy(r,0,0,t),r)}if(void 0!==e.length)return"number"!=typeof e.length||F(e.length)?o(0):f(e);if("Buffer"===e.type&&Array.isArray(e.data))return f(e.data)}(e);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return a.from(e[Symbol.toPrimitive]("string"),t,r);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function u(e){if("number"!=typeof e)throw new TypeError('"size" argument must be of type number');if(e<0)throw new RangeError('The value "'+e+'" is invalid for option "size"')}function l(e){return u(e),o(e<0?0:0|c(e))}function f(e){for(var t=e.length<0?0:0|c(e.length),r=o(t),n=0;n<t;n+=1)r[n]=255&e[n];return r}function c(e){if(e>=i)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+i.toString(16)+" bytes");return 0|e}function h(e,t){if(a.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||j(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return B(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return R(e).length;default:if(i)return n?-1:B(e).length;t=(""+t).toLowerCase(),i=!0}}function d(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function p(e,t,r,n,i){if(0===e.length)return-1;if("string"==typeof r?(n=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),F(r=+r)&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return-1;r=e.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof t&&(t=a.from(t,n)),a.isBuffer(t))return 0===t.length?-1:g(e,t,r,n,i);if("number"==typeof t)return t&=255,"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):g(e,[t],r,n,i);throw new TypeError("val must be string, number or Buffer")}function g(e,t,r,n,i){var o,a=1,s=e.length,u=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return-1;a=2,s/=2,u/=2,r/=2}function l(e,t){return 1===a?e[t]:e.readUInt16BE(t*a)}if(i){var f=-1;for(o=r;o<s;o++)if(l(e,o)===l(t,-1===f?0:o-f)){if(-1===f&&(f=o),o-f+1===u)return f*a}else-1!==f&&(o-=o-f),f=-1}else for(r+u>s&&(r=s-u),o=r;o>=0;o--){for(var c=!0,h=0;h<u;h++)if(l(e,o+h)!==l(t,h)){c=!1;break}if(c)return o}return-1}function y(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=t.length;n>o/2&&(n=o/2);for(var a=0;a<n;++a){var s=parseInt(t.substr(2*a,2),16);if(F(s))return a;e[r+a]=s}return a}function b(e,t,r,n){return P(B(t,e.length-r),e,r,n)}function m(e,t,r,n){return P(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,n)}function v(e,t,r,n){return m(e,t,r,n)}function w(e,t,r,n){return P(R(t),e,r,n)}function _(e,t,r,n){return P(function(e,t){for(var r,n,i,o=[],a=0;a<e.length&&!((t-=2)<0);++a)r=e.charCodeAt(a),n=r>>8,i=r%256,o.push(i),o.push(n);return o}(t,e.length-r),e,r,n)}function E(e,r,n){return 0===r&&n===e.length?t.fromByteArray(e):t.fromByteArray(e.slice(r,n))}function x(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var o,a,s,u,l=e[i],f=null,c=l>239?4:l>223?3:l>191?2:1;if(i+c<=r)switch(c){case 1:l<128&&(f=l);break;case 2:128==(192&(o=e[i+1]))&&(u=(31&l)<<6|63&o)>127&&(f=u);break;case 3:o=e[i+1],a=e[i+2],128==(192&o)&&128==(192&a)&&(u=(15&l)<<12|(63&o)<<6|63&a)>2047&&(u<55296||u>57343)&&(f=u);break;case 4:o=e[i+1],a=e[i+2],s=e[i+3],128==(192&o)&&128==(192&a)&&128==(192&s)&&(u=(15&l)<<18|(63&o)<<12|(63&a)<<6|63&s)>65535&&u<1114112&&(f=u)}null===f?(f=65533,c=1):f>65535&&(f-=65536,n.push(f>>>10&1023|55296),f=56320|1023&f),n.push(f),i+=c}return function(e){var t=e.length;if(t<=T)return String.fromCharCode.apply(String,e);var r="",n=0;for(;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=T));return r}(n)}r.kMaxLength=i,a.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()}catch(e){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),"undefined"!=typeof Symbol&&null!=Symbol.species&&a[Symbol.species]===a&&Object.defineProperty(a,Symbol.species,{value:null,configurable:!0,enumerable:!1,writable:!1}),a.poolSize=8192,a.from=function(e,t,r){return s(e,t,r)},a.prototype.__proto__=Uint8Array.prototype,a.__proto__=Uint8Array,a.alloc=function(e,t,r){return function(e,t,r){return u(e),e<=0?o(e):void 0!==t?"string"==typeof r?o(e).fill(t,r):o(e).fill(t):o(e)}(e,t,r)},a.allocUnsafe=function(e){return l(e)},a.allocUnsafeSlow=function(e){return l(e)},a.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==a.prototype},a.compare=function(e,t){if(j(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),j(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(e)||!a.isBuffer(t))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var r=e.length,n=t.length,i=0,o=Math.min(r,n);i<o;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:n<r?1:0},a.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(e,t){if(!Array.isArray(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return a.alloc(0);var r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;var n=a.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var o=e[r];if(j(o,Uint8Array)&&(o=a.from(o)),!a.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(n,i),i+=o.length}return n},a.byteLength=h,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)d(this,t,t+1);return this},a.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)d(this,t,t+3),d(this,t+1,t+2);return this},a.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)d(this,t,t+7),d(this,t+1,t+6),d(this,t+2,t+5),d(this,t+3,t+4);return this},a.prototype.toString=function(){var e=this.length;return 0===e?"":0===arguments.length?x(this,0,e):function(e,t,r){var n=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return S(this,t,r);case"utf8":case"utf-8":return x(this,t,r);case"ascii":return k(this,t,r);case"latin1":case"binary":return N(this,t,r);case"base64":return E(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return I(this,t,r);default:if(n)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),n=!0}}.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(e){if(!a.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===a.compare(this,e)},a.prototype.inspect=function(){var e="",t=r.INSPECT_MAX_BYTES;return e=this.toString("hex",0,t).replace(/(.{2})/g,"$1 ").trim(),this.length>t&&(e+=" ... "),"<Buffer "+e+">"},a.prototype.compare=function(e,t,r,n,i){if(j(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(e))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return-1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,i>>>=0,this===e)return 0;for(var o=i-n,s=r-t,u=Math.min(o,s),l=this.slice(n,i),f=e.slice(t,r),c=0;c<u;++c)if(l[c]!==f[c]){o=l[c],s=f[c];break}return o<s?-1:s<o?1:0},a.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},a.prototype.indexOf=function(e,t,r){return p(this,e,t,r,!0)},a.prototype.lastIndexOf=function(e,t,r){return p(this,e,t,r,!1)},a.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-t;if((void 0===r||r>i)&&(r=i),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var o=!1;;)switch(n){case"hex":return y(this,e,t,r);case"utf8":case"utf-8":return b(this,e,t,r);case"ascii":return m(this,e,t,r);case"latin1":case"binary":return v(this,e,t,r);case"base64":return w(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return _(this,e,t,r);default:if(o)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),o=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var T=4096;function k(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}function N(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}function S(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=t;o<r;++o)i+=C(e[o]);return i}function I(e,t,r){for(var n=e.slice(t,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function A(e,t,r){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function D(e,t,r,n,i,o){if(!a.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw new RangeError('"value" argument is out of bounds');if(r+n>e.length)throw new RangeError("Index out of range")}function L(e,t,r,n,i,o){if(r+n>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function O(e,t,r,i,o){return t=+t,r>>>=0,o||L(e,0,r,4),n.write(e,t,r,i,23,4),r+4}function M(e,t,r,i,o){return t=+t,r>>>=0,o||L(e,0,r,8),n.write(e,t,r,i,52,8),r+8}a.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var n=this.subarray(e,t);return n.__proto__=a.prototype,n},a.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||A(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n},a.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||A(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},a.prototype.readUInt8=function(e,t){return e>>>=0,t||A(e,1,this.length),this[e]},a.prototype.readUInt16LE=function(e,t){return e>>>=0,t||A(e,2,this.length),this[e]|this[e+1]<<8},a.prototype.readUInt16BE=function(e,t){return e>>>=0,t||A(e,2,this.length),this[e]<<8|this[e+1]},a.prototype.readUInt32LE=function(e,t){return e>>>=0,t||A(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},a.prototype.readUInt32BE=function(e,t){return e>>>=0,t||A(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},a.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||A(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},a.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||A(e,t,this.length);for(var n=t,i=1,o=this[e+--n];n>0&&(i*=256);)o+=this[e+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*t)),o},a.prototype.readInt8=function(e,t){return e>>>=0,t||A(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},a.prototype.readInt16LE=function(e,t){e>>>=0,t||A(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},a.prototype.readInt16BE=function(e,t){e>>>=0,t||A(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},a.prototype.readInt32LE=function(e,t){return e>>>=0,t||A(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},a.prototype.readInt32BE=function(e,t){return e>>>=0,t||A(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},a.prototype.readFloatLE=function(e,t){return e>>>=0,t||A(e,4,this.length),n.read(this,e,!0,23,4)},a.prototype.readFloatBE=function(e,t){return e>>>=0,t||A(e,4,this.length),n.read(this,e,!1,23,4)},a.prototype.readDoubleLE=function(e,t){return e>>>=0,t||A(e,8,this.length),n.read(this,e,!0,52,8)},a.prototype.readDoubleBE=function(e,t){return e>>>=0,t||A(e,8,this.length),n.read(this,e,!1,52,8)},a.prototype.writeUIntLE=function(e,t,r,n){(e=+e,t>>>=0,r>>>=0,n)||D(this,e,t,r,Math.pow(2,8*r)-1,0);var i=1,o=0;for(this[t]=255&e;++o<r&&(i*=256);)this[t+o]=e/i&255;return t+r},a.prototype.writeUIntBE=function(e,t,r,n){(e=+e,t>>>=0,r>>>=0,n)||D(this,e,t,r,Math.pow(2,8*r)-1,0);var i=r-1,o=1;for(this[t+i]=255&e;--i>=0&&(o*=256);)this[t+i]=e/o&255;return t+r},a.prototype.writeUInt8=function(e,t,r){return e=+e,t>>>=0,r||D(this,e,t,1,255,0),this[t]=255&e,t+1},a.prototype.writeUInt16LE=function(e,t,r){return e=+e,t>>>=0,r||D(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},a.prototype.writeUInt16BE=function(e,t,r){return e=+e,t>>>=0,r||D(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},a.prototype.writeUInt32LE=function(e,t,r){return e=+e,t>>>=0,r||D(this,e,t,4,4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},a.prototype.writeUInt32BE=function(e,t,r){return e=+e,t>>>=0,r||D(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},a.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t>>>=0,!n){var i=Math.pow(2,8*r-1);D(this,e,t,r,i-1,-i)}var o=0,a=1,s=0;for(this[t]=255&e;++o<r&&(a*=256);)e<0&&0===s&&0!==this[t+o-1]&&(s=1),this[t+o]=(e/a>>0)-s&255;return t+r},a.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t>>>=0,!n){var i=Math.pow(2,8*r-1);D(this,e,t,r,i-1,-i)}var o=r-1,a=1,s=0;for(this[t+o]=255&e;--o>=0&&(a*=256);)e<0&&0===s&&0!==this[t+o+1]&&(s=1),this[t+o]=(e/a>>0)-s&255;return t+r},a.prototype.writeInt8=function(e,t,r){return e=+e,t>>>=0,r||D(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},a.prototype.writeInt16LE=function(e,t,r){return e=+e,t>>>=0,r||D(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},a.prototype.writeInt16BE=function(e,t,r){return e=+e,t>>>=0,r||D(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},a.prototype.writeInt32LE=function(e,t,r){return e=+e,t>>>=0,r||D(this,e,t,4,2147483647,-2147483648),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},a.prototype.writeInt32BE=function(e,t,r){return e=+e,t>>>=0,r||D(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},a.prototype.writeFloatLE=function(e,t,r){return O(this,e,t,!0,r)},a.prototype.writeFloatBE=function(e,t,r){return O(this,e,t,!1,r)},a.prototype.writeDoubleLE=function(e,t,r){return M(this,e,t,!0,r)},a.prototype.writeDoubleBE=function(e,t,r){return M(this,e,t,!1,r)},a.prototype.copy=function(e,t,r,n){if(!a.isBuffer(e))throw new TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i=n-r;if(this===e&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(t,r,n);else if(this===e&&r<t&&t<n)for(var o=i-1;o>=0;--o)e[o+t]=this[o+r];else Uint8Array.prototype.set.call(e,this.subarray(r,n),t);return i},a.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!a.isEncoding(n))throw new TypeError("Unknown encoding: "+n);if(1===e.length){var i=e.charCodeAt(0);("utf8"===n&&i<128||"latin1"===n)&&(e=i)}}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;var o;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(o=t;o<r;++o)this[o]=e;else{var s=a.isBuffer(e)?e:a.from(e,n),u=s.length;if(0===u)throw new TypeError('The value "'+e+'" is invalid for argument "value"');for(o=0;o<r-t;++o)this[o+t]=s[o%u]}return this};var U=/[^+/0-9A-Za-z-_]/g;function C(e){return e<16?"0"+e.toString(16):e.toString(16)}function B(e,t){var r;t=t||1/0;for(var n=e.length,i=null,o=[],a=0;a<n;++a){if((r=e.charCodeAt(a))>55295&&r<57344){if(!i){if(r>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(a+1===n){(t-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;o.push(r)}else if(r<2048){if((t-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function R(e){return t.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(U,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function P(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length||i>=e.length);++i)t[i+r]=e[i];return i}function j(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}function F(e){return e!=e}}).call(this)}).call(this,e("buffer").Buffer)},{"base64-js":16,buffer:23,ieee754:66}],24:[function(e,t,r){(function(e){(function(){t.exports=new function(){var t,r=!1,n=this;n._DeArray=function(e,t,r){return[e.slice(t,t+r)]},n._EnArray=function(e,t,r,n){for(var i=0;i<r;e[t+i]=n[i]?n[i]:0,i++);},n._DeChar=function(e,t){return String.fromCharCode(e[t])},n._EnChar=function(e,t,r){e[t]=r.charCodeAt(0)},n._DeInt=function(e,n){var i,o,a,s=r?t.len-1:0,u=r?-1:1,l=s+u*t.len;for(i=0,o=s,a=1;o!=l;i+=e[n+o]*a,o+=u,a*=256);return t.bSigned&&i&Math.pow(2,8*t.len-1)&&(i-=Math.pow(2,8*t.len)),i},n._EnInt=function(e,n,i){var o,a=r?t.len-1:0,s=r?-1:1,u=a+s*t.len;for(i=i<t.min?t.min:i>t.max?t.max:i,o=a;o!=u;e[n+o]=255&i,o+=s,i>>=8);},n._DeString=function(e,t,r){for(var n=new Array(r),i=0;i<r;n[i]=String.fromCharCode(e[t+i]),i++);return n.join("")},n._EnString=function(e,t,r,n){for(var i,o=0;o<r;e[t+o]=(i=n.charCodeAt(o))?i:0,o++);},n._DeNullString=function(e,t,r,i){var o=n._DeString(e,t,r,i);return o.substring(0,o.length-1)},n._De754=function(e,n){var i,o,a,s,u,l,f,c,h,d;for(f=t.mLen,h=(d=(1<<(c=8*t.len-t.mLen-1))-1)>>1,s=r?0:t.len-1,u=r?1:-1,i=e[n+s],s+=u,o=i&(1<<-(l=-7))-1,i>>=-l,l+=c;l>0;o=256*o+e[n+s],s+=u,l-=8);for(a=o&(1<<-l)-1,o>>=-l,l+=f;l>0;a=256*a+e[n+s],s+=u,l-=8);switch(o){case 0:o=1-h;break;case d:return a?NaN:1/0*(i?-1:1);default:a+=Math.pow(2,f),o-=h}return(i?-1:1)*a*Math.pow(2,o-f)},n._En754=function(e,n,i){var o,a,s,u,l,f,c,h,d,p;for(c=t.mLen,d=(p=(1<<(h=8*t.len-t.mLen-1))-1)>>1,o=i<0?1:0,i=Math.abs(i),isNaN(i)||i==1/0?(s=isNaN(i)?1:0,a=p):(a=Math.floor(Math.log(i)/Math.LN2),i*(f=Math.pow(2,-a))<1&&(a--,f*=2),(i+=a+d>=1?t.rt/f:t.rt*Math.pow(2,1-d))*f>=2&&(a++,f/=2),a+d>=p?(s=0,a=p):a+d>=1?(s=(i*f-1)*Math.pow(2,c),a+=d):(s=i*Math.pow(2,d-1)*Math.pow(2,c),a=0)),u=r?t.len-1:0,l=r?-1:1;c>=8;e[n+u]=255&s,u+=l,s/=256,c-=8);for(a=a<<c|s,h+=c;h>0;e[n+u]=255&a,u+=l,a/=256,h-=8);e[n+u-l]|=128*o},n._sPattern="(\\d+)?([AxcbBhHsSfdiIlL])(\\(([a-zA-Z0-9]+)\\))?",n._lenLut={A:1,x:1,c:1,b:1,B:1,h:2,H:2,s:1,S:1,f:4,d:8,i:4,I:4,l:4,L:4},n._elLut={A:{en:n._EnArray,de:n._DeArray},s:{en:n._EnString,de:n._DeString},S:{en:n._EnString,de:n._DeNullString},c:{en:n._EnChar,de:n._DeChar},b:{en:n._EnInt,de:n._DeInt,len:1,bSigned:!0,min:-Math.pow(2,7),max:Math.pow(2,7)-1},B:{en:n._EnInt,de:n._DeInt,len:1,bSigned:!1,min:0,max:Math.pow(2,8)-1},h:{en:n._EnInt,de:n._DeInt,len:2,bSigned:!0,min:-Math.pow(2,15),max:Math.pow(2,15)-1},H:{en:n._EnInt,de:n._DeInt,len:2,bSigned:!1,min:0,max:Math.pow(2,16)-1},i:{en:n._EnInt,de:n._DeInt,len:4,bSigned:!0,min:-Math.pow(2,31),max:Math.pow(2,31)-1},I:{en:n._EnInt,de:n._DeInt,len:4,bSigned:!1,min:0,max:Math.pow(2,32)-1},l:{en:n._EnInt,de:n._DeInt,len:4,bSigned:!0,min:-Math.pow(2,31),max:Math.pow(2,31)-1},L:{en:n._EnInt,de:n._DeInt,len:4,bSigned:!1,min:0,max:Math.pow(2,32)-1},f:{en:n._En754,de:n._De754,len:4,mLen:23,rt:Math.pow(2,-24)-Math.pow(2,-77)},d:{en:n._En754,de:n._De754,len:8,mLen:52,rt:0}},n._UnpackSeries=function(e,r,n,i){for(var o=t.de,a=[],s=0;s<e;a.push(o(n,i+s*r)),s++);return a},n._PackSeries=function(e,r,n,i,o,a){for(var s=t.en,u=0;u<e;s(n,i+u*r,o[a+u]),u++);},n._zip=function(e,t){for(var r={},n=0;n<e.length;n++)r[e[n]]=t[n];return r},n.unpack=function(e,n,i){r="<"!=e.charAt(0),i=i||0;for(var o,a,s,u=new RegExp(this._sPattern,"g"),l=[],f=[];o=u.exec(e);){if(a=void 0==o[1]||""==o[1]?1:parseInt(o[1]),"S"===o[2]){for(a=0;0!==n[i+a];)a++;a++}if(i+a*(s=this._lenLut[o[2]])>n.length)return;switch(o[2]){case"A":case"s":case"S":f.push(this._elLut[o[2]].de(n,i,a));break;case"c":case"b":case"B":case"h":case"H":case"i":case"I":case"l":case"L":case"f":case"d":t=this._elLut[o[2]],f.push(this._UnpackSeries(a,s,n,i))}l.push(o[4]),i+=a*s}return f=Array.prototype.concat.apply([],f),-1!==l.indexOf(void 0)?f:this._zip(l,f)},n.packTo=function(e,n,i,o){r="<"!=e.charAt(0);for(var a,s,u,l,f=new RegExp(this._sPattern,"g"),c=0;a=f.exec(e);){if(s=void 0==a[1]||""==a[1]?1:parseInt(a[1]),"S"===a[2]&&(s=o[c].length+1),i+s*(u=this._lenLut[a[2]])>n.length)return!1;switch(a[2]){case"A":case"s":case"S":if(c+1>o.length)return!1;this._elLut[a[2]].en(n,i,s,o[c]),c+=1;break;case"c":case"b":case"B":case"h":case"H":case"i":case"I":case"l":case"L":case"f":case"d":if(t=this._elLut[a[2]],c+s>o.length)return!1;this._PackSeries(s,u,n,i,o,c),c+=s;break;case"x":for(l=0;l<s;l++)n[i+l]=0}i+=s*u}return n},n.pack=function(t,r){return this.packTo(t,new e(this.calcLength(t,r)),0,r)},n.calcLength=function(e,t){for(var r,n=new RegExp(this._sPattern,"g"),i=0,o=0;r=n.exec(e);){var a=(void 0==r[1]||""==r[1]?1:parseInt(r[1]))*this._lenLut[r[2]];"S"===r[2]&&(a=t[o].length+1),i+=a,o++}return i}}}).call(this)}).call(this,e("buffer").Buffer)},{buffer:23}],25:[function(e,t,r){var n,i;n=this,i=function(e){"use strict";var t=function(e,r,i){if(void 0===e&&(e=t.DEFAULT_CAPACITY),void 0===r&&(r=t.DEFAULT_ENDIAN),void 0===i&&(i=t.DEFAULT_NOASSERT),!i){if((e|=0)<0)throw RangeError("Illegal capacity");r=!!r,i=!!i}this.buffer=0===e?n:new ArrayBuffer(e),this.view=0===e?null:new Uint8Array(this.buffer),this.offset=0,this.markedOffset=-1,this.limit=e,this.littleEndian=r,this.noAssert=i};t.VERSION="5.0.1",t.LITTLE_ENDIAN=!0,t.BIG_ENDIAN=!1,t.DEFAULT_CAPACITY=16,t.DEFAULT_ENDIAN=t.BIG_ENDIAN,t.DEFAULT_NOASSERT=!1,t.Long=e||null;var r=t.prototype;r.__isByteBuffer__,Object.defineProperty(r,"__isByteBuffer__",{value:!0,enumerable:!1,configurable:!1});var n=new ArrayBuffer(0),i=String.fromCharCode;function o(e){var t=0;return function(){return t<e.length?e.charCodeAt(t++):null}}function a(){var e=[],t=[];return function(){if(0===arguments.length)return t.join("")+i.apply(String,e);e.length+arguments.length>1024&&(t.push(i.apply(String,e)),e.length=0),Array.prototype.push.apply(e,arguments)}}function s(e,t,r,n,i){var o,a,s=8*i-n-1,u=(1<<s)-1,l=u>>1,f=-7,c=r?i-1:0,h=r?-1:1,d=e[t+c];for(c+=h,o=d&(1<<-f)-1,d>>=-f,f+=s;f>0;o=256*o+e[t+c],c+=h,f-=8);for(a=o&(1<<-f)-1,o>>=-f,f+=n;f>0;a=256*a+e[t+c],c+=h,f-=8);if(0===o)o=1-l;else{if(o===u)return a?NaN:1/0*(d?-1:1);a+=Math.pow(2,n),o-=l}return(d?-1:1)*a*Math.pow(2,o-n)}function u(e,t,r,n,i,o){var a,s,u,l=8*o-i-1,f=(1<<l)-1,c=f>>1,h=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=n?0:o-1,p=n?1:-1,g=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(s=isNaN(t)?1:0,a=f):(a=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-a))<1&&(a--,u*=2),(t+=a+c>=1?h/u:h*Math.pow(2,1-c))*u>=2&&(a++,u/=2),a+c>=f?(s=0,a=f):a+c>=1?(s=(t*u-1)*Math.pow(2,i),a+=c):(s=t*Math.pow(2,c-1)*Math.pow(2,i),a=0));i>=8;e[r+d]=255&s,d+=p,s/=256,i-=8);for(a=a<<i|s,l+=i;l>0;e[r+d]=255&a,d+=p,a/=256,l-=8);e[r+d-p]|=128*g}t.accessor=function(){return Uint8Array},t.allocate=function(e,r,n){return new t(e,r,n)},t.concat=function(e,r,n,i){"boolean"!=typeof r&&"string"==typeof r||(i=n,n=r,r=void 0);for(var o,a=0,s=0,u=e.length;s<u;++s)t.isByteBuffer(e[s])||(e[s]=t.wrap(e[s],r)),(o=e[s].limit-e[s].offset)>0&&(a+=o);if(0===a)return new t(0,n,i);var l,f=new t(a,n,i);for(s=0;s<u;)(o=(l=e[s++]).limit-l.offset)<=0||(f.view.set(l.view.subarray(l.offset,l.limit),f.offset),f.offset+=o);return f.limit=f.offset,f.offset=0,f},t.isByteBuffer=function(e){return!0===(e&&e.__isByteBuffer__)},t.type=function(){return ArrayBuffer},t.wrap=function(e,n,i,o){if("string"!=typeof n&&(o=i,i=n,n=void 0),"string"==typeof e)switch(void 0===n&&(n="utf8"),n){case"base64":return t.fromBase64(e,i);case"hex":return t.fromHex(e,i);case"binary":return t.fromBinary(e,i);case"utf8":return t.fromUTF8(e,i);case"debug":return t.fromDebug(e,i);default:throw Error("Unsupported encoding: "+n)}if(null===e||"object"!=typeof e)throw TypeError("Illegal buffer");var a;if(t.isByteBuffer(e))return(a=r.clone.call(e)).markedOffset=-1,a;if(e instanceof Uint8Array)a=new t(0,i,o),e.length>0&&(a.buffer=e.buffer,a.offset=e.byteOffset,a.limit=e.byteOffset+e.byteLength,a.view=new Uint8Array(e.buffer));else if(e instanceof ArrayBuffer)a=new t(0,i,o),e.byteLength>0&&(a.buffer=e,a.offset=0,a.limit=e.byteLength,a.view=e.byteLength>0?new Uint8Array(e):null);else{if("[object Array]"!==Object.prototype.toString.call(e))throw TypeError("Illegal buffer");(a=new t(e.length,i,o)).limit=e.length;for(var s=0;s<e.length;++s)a.view[s]=e[s]}return a},r.writeBitSet=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if(!(e instanceof Array))throw TypeError("Illegal BitSet: Not an array");if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}var n,i=t,o=e.length,a=o>>3,s=0;for(t+=this.writeVarint32(o,t);a--;)n=1&!!e[s++]|(1&!!e[s++])<<1|(1&!!e[s++])<<2|(1&!!e[s++])<<3|(1&!!e[s++])<<4|(1&!!e[s++])<<5|(1&!!e[s++])<<6|(1&!!e[s++])<<7,this.writeByte(n,t++);if(s<o){var u=0;for(n=0;s<o;)n|=(1&!!e[s++])<<u++;this.writeByte(n,t++)}return r?(this.offset=t,this):t-i},r.readBitSet=function(e){var t=void 0===e;t&&(e=this.offset);var r,n=this.readVarint32(e),i=n.value,o=i>>3,a=0,s=[];for(e+=n.length;o--;)r=this.readByte(e++),s[a++]=!!(1&r),s[a++]=!!(2&r),s[a++]=!!(4&r),s[a++]=!!(8&r),s[a++]=!!(16&r),s[a++]=!!(32&r),s[a++]=!!(64&r),s[a++]=!!(128&r);if(a<i){var u=0;for(r=this.readByte(e++);a<i;)s[a++]=!!(r>>u++&1)}return t&&(this.offset=e),s},r.readBytes=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+e>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+"+e+") <= "+this.buffer.byteLength)}var n=this.slice(t,t+e);return r&&(this.offset+=e),n},r.writeBytes=r.append,r.writeInt8=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e|=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=1;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=1,this.view[t]=e,r&&(this.offset+=1),this},r.writeByte=r.writeInt8,r.readInt8=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+1) <= "+this.buffer.byteLength)}var r=this.view[e];return 128==(128&r)&&(r=-(255-r+1)),t&&(this.offset+=1),r},r.readByte=r.readInt8,r.writeUint8=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=1;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=1,this.view[t]=e,r&&(this.offset+=1),this},r.writeUInt8=r.writeUint8,r.readUint8=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+1) <= "+this.buffer.byteLength)}var r=this.view[e];return t&&(this.offset+=1),r},r.readUInt8=r.readUint8,r.writeInt16=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e|=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=2;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=2,this.littleEndian?(this.view[t+1]=(65280&e)>>>8,this.view[t]=255&e):(this.view[t]=(65280&e)>>>8,this.view[t+1]=255&e),r&&(this.offset+=2),this},r.writeShort=r.writeInt16,r.readInt16=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+2>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+2) <= "+this.buffer.byteLength)}var r=0;return this.littleEndian?(r=this.view[e],r|=this.view[e+1]<<8):(r=this.view[e]<<8,r|=this.view[e+1]),32768==(32768&r)&&(r=-(65535-r+1)),t&&(this.offset+=2),r},r.readShort=r.readInt16,r.writeUint16=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=2;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=2,this.littleEndian?(this.view[t+1]=(65280&e)>>>8,this.view[t]=255&e):(this.view[t]=(65280&e)>>>8,this.view[t+1]=255&e),r&&(this.offset+=2),this},r.writeUInt16=r.writeUint16,r.readUint16=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+2>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+2) <= "+this.buffer.byteLength)}var r=0;return this.littleEndian?(r=this.view[e],r|=this.view[e+1]<<8):(r=this.view[e]<<8,r|=this.view[e+1]),t&&(this.offset+=2),r},r.readUInt16=r.readUint16,r.writeInt32=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e|=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=4;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=4,this.littleEndian?(this.view[t+3]=e>>>24&255,this.view[t+2]=e>>>16&255,this.view[t+1]=e>>>8&255,this.view[t]=255&e):(this.view[t]=e>>>24&255,this.view[t+1]=e>>>16&255,this.view[t+2]=e>>>8&255,this.view[t+3]=255&e),r&&(this.offset+=4),this},r.writeInt=r.writeInt32,r.readInt32=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+4>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+4) <= "+this.buffer.byteLength)}var r=0;return this.littleEndian?(r=this.view[e+2]<<16,r|=this.view[e+1]<<8,r|=this.view[e],r+=this.view[e+3]<<24>>>0):(r=this.view[e+1]<<16,r|=this.view[e+2]<<8,r|=this.view[e+3],r+=this.view[e]<<24>>>0),r|=0,t&&(this.offset+=4),r},r.readInt=r.readInt32,r.writeUint32=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=4;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=4,this.littleEndian?(this.view[t+3]=e>>>24&255,this.view[t+2]=e>>>16&255,this.view[t+1]=e>>>8&255,this.view[t]=255&e):(this.view[t]=e>>>24&255,this.view[t+1]=e>>>16&255,this.view[t+2]=e>>>8&255,this.view[t+3]=255&e),r&&(this.offset+=4),this},r.writeUInt32=r.writeUint32,r.readUint32=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+4>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+4) <= "+this.buffer.byteLength)}var r=0;return this.littleEndian?(r=this.view[e+2]<<16,r|=this.view[e+1]<<8,r|=this.view[e],r+=this.view[e+3]<<24>>>0):(r=this.view[e+1]<<16,r|=this.view[e+2]<<8,r|=this.view[e+3],r+=this.view[e]<<24>>>0),t&&(this.offset+=4),r},r.readUInt32=r.readUint32,e&&(r.writeInt64=function(t,r){var n=void 0===r;if(n&&(r=this.offset),!this.noAssert){if("number"==typeof t)t=e.fromNumber(t);else if("string"==typeof t)t=e.fromString(t);else if(!(t&&t instanceof e))throw TypeError("Illegal value: "+t+" (not an integer or Long)");if("number"!=typeof r||r%1!=0)throw TypeError("Illegal offset: "+r+" (not an integer)");if((r>>>=0)<0||r+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+r+" (+0) <= "+this.buffer.byteLength)}"number"==typeof t?t=e.fromNumber(t):"string"==typeof t&&(t=e.fromString(t)),r+=8;var i=this.buffer.byteLength;r>i&&this.resize((i*=2)>r?i:r),r-=8;var o=t.low,a=t.high;return this.littleEndian?(this.view[r+3]=o>>>24&255,this.view[r+2]=o>>>16&255,this.view[r+1]=o>>>8&255,this.view[r]=255&o,r+=4,this.view[r+3]=a>>>24&255,this.view[r+2]=a>>>16&255,this.view[r+1]=a>>>8&255,this.view[r]=255&a):(this.view[r]=a>>>24&255,this.view[r+1]=a>>>16&255,this.view[r+2]=a>>>8&255,this.view[r+3]=255&a,r+=4,this.view[r]=o>>>24&255,this.view[r+1]=o>>>16&255,this.view[r+2]=o>>>8&255,this.view[r+3]=255&o),n&&(this.offset+=8),this},r.writeLong=r.writeInt64,r.readInt64=function(t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+8>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+8) <= "+this.buffer.byteLength)}var n=0,i=0;this.littleEndian?(n=this.view[t+2]<<16,n|=this.view[t+1]<<8,n|=this.view[t],n+=this.view[t+3]<<24>>>0,t+=4,i=this.view[t+2]<<16,i|=this.view[t+1]<<8,i|=this.view[t],i+=this.view[t+3]<<24>>>0):(i=this.view[t+1]<<16,i|=this.view[t+2]<<8,i|=this.view[t+3],i+=this.view[t]<<24>>>0,t+=4,n=this.view[t+1]<<16,n|=this.view[t+2]<<8,n|=this.view[t+3],n+=this.view[t]<<24>>>0);var o=new e(n,i,!1);return r&&(this.offset+=8),o},r.readLong=r.readInt64,r.writeUint64=function(t,r){var n=void 0===r;if(n&&(r=this.offset),!this.noAssert){if("number"==typeof t)t=e.fromNumber(t);else if("string"==typeof t)t=e.fromString(t);else if(!(t&&t instanceof e))throw TypeError("Illegal value: "+t+" (not an integer or Long)");if("number"!=typeof r||r%1!=0)throw TypeError("Illegal offset: "+r+" (not an integer)");if((r>>>=0)<0||r+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+r+" (+0) <= "+this.buffer.byteLength)}"number"==typeof t?t=e.fromNumber(t):"string"==typeof t&&(t=e.fromString(t)),r+=8;var i=this.buffer.byteLength;r>i&&this.resize((i*=2)>r?i:r),r-=8;var o=t.low,a=t.high;return this.littleEndian?(this.view[r+3]=o>>>24&255,this.view[r+2]=o>>>16&255,this.view[r+1]=o>>>8&255,this.view[r]=255&o,r+=4,this.view[r+3]=a>>>24&255,this.view[r+2]=a>>>16&255,this.view[r+1]=a>>>8&255,this.view[r]=255&a):(this.view[r]=a>>>24&255,this.view[r+1]=a>>>16&255,this.view[r+2]=a>>>8&255,this.view[r+3]=255&a,r+=4,this.view[r]=o>>>24&255,this.view[r+1]=o>>>16&255,this.view[r+2]=o>>>8&255,this.view[r+3]=255&o),n&&(this.offset+=8),this},r.writeUInt64=r.writeUint64,r.readUint64=function(t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+8>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+8) <= "+this.buffer.byteLength)}var n=0,i=0;this.littleEndian?(n=this.view[t+2]<<16,n|=this.view[t+1]<<8,n|=this.view[t],n+=this.view[t+3]<<24>>>0,t+=4,i=this.view[t+2]<<16,i|=this.view[t+1]<<8,i|=this.view[t],i+=this.view[t+3]<<24>>>0):(i=this.view[t+1]<<16,i|=this.view[t+2]<<8,i|=this.view[t+3],i+=this.view[t]<<24>>>0,t+=4,n=this.view[t+1]<<16,n|=this.view[t+2]<<8,n|=this.view[t+3],n+=this.view[t]<<24>>>0);var o=new e(n,i,!0);return r&&(this.offset+=8),o},r.readUInt64=r.readUint64),r.writeFloat32=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e)throw TypeError("Illegal value: "+e+" (not a number)");if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=4;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=4,u(this.view,e,t,this.littleEndian,23,4),r&&(this.offset+=4),this},r.writeFloat=r.writeFloat32,r.readFloat32=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+4>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+4) <= "+this.buffer.byteLength)}var r=s(this.view,e,this.littleEndian,23,4);return t&&(this.offset+=4),r},r.readFloat=r.readFloat32,r.writeFloat64=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e)throw TypeError("Illegal value: "+e+" (not a number)");if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=8;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=8,u(this.view,e,t,this.littleEndian,52,8),r&&(this.offset+=8),this},r.writeDouble=r.writeFloat64,r.readFloat64=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+8>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+8) <= "+this.buffer.byteLength)}var r=s(this.view,e,this.littleEndian,52,8);return t&&(this.offset+=8),r},r.readDouble=r.readFloat64,t.MAX_VARINT32_BYTES=5,t.calculateVarint32=function(e){return(e>>>=0)<128?1:e<16384?2:e<1<<21?3:e<1<<28?4:5},t.zigZagEncode32=function(e){return((e|=0)<<1^e>>31)>>>0},t.zigZagDecode32=function(e){return e>>>1^-(1&e)|0},r.writeVarint32=function(e,r){var n=void 0===r;if(n&&(r=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e|=0,"number"!=typeof r||r%1!=0)throw TypeError("Illegal offset: "+r+" (not an integer)");if((r>>>=0)<0||r+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+r+" (+0) <= "+this.buffer.byteLength)}var i,o=t.calculateVarint32(e);r+=o;var a=this.buffer.byteLength;for(r>a&&this.resize((a*=2)>r?a:r),r-=o,e>>>=0;e>=128;)i=127&e|128,this.view[r++]=i,e>>>=7;return this.view[r++]=e,n?(this.offset=r,this):o},r.writeVarint32ZigZag=function(e,r){return this.writeVarint32(t.zigZagEncode32(e),r)},r.readVarint32=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+1) <= "+this.buffer.byteLength)}var r,n=0,i=0;do{if(!this.noAssert&&e>this.limit){var o=Error("Truncated");throw o.truncated=!0,o}r=this.view[e++],n<5&&(i|=(127&r)<<7*n),++n}while(0!=(128&r));return i|=0,t?(this.offset=e,i):{value:i,length:n}},r.readVarint32ZigZag=function(e){var r=this.readVarint32(e);return"object"==typeof r?r.value=t.zigZagDecode32(r.value):r=t.zigZagDecode32(r),r},e&&(t.MAX_VARINT64_BYTES=10,t.calculateVarint64=function(t){"number"==typeof t?t=e.fromNumber(t):"string"==typeof t&&(t=e.fromString(t));var r=t.toInt()>>>0,n=t.shiftRightUnsigned(28).toInt()>>>0,i=t.shiftRightUnsigned(56).toInt()>>>0;return 0==i?0==n?r<16384?r<128?1:2:r<1<<21?3:4:n<16384?n<128?5:6:n<1<<21?7:8:i<128?9:10},t.zigZagEncode64=function(t){return"number"==typeof t?t=e.fromNumber(t,!1):"string"==typeof t?t=e.fromString(t,!1):!1!==t.unsigned&&(t=t.toSigned()),t.shiftLeft(1).xor(t.shiftRight(63)).toUnsigned()},t.zigZagDecode64=function(t){return"number"==typeof t?t=e.fromNumber(t,!1):"string"==typeof t?t=e.fromString(t,!1):!1!==t.unsigned&&(t=t.toSigned()),t.shiftRightUnsigned(1).xor(t.and(e.ONE).toSigned().negate()).toSigned()},r.writeVarint64=function(r,n){var i=void 0===n;if(i&&(n=this.offset),!this.noAssert){if("number"==typeof r)r=e.fromNumber(r);else if("string"==typeof r)r=e.fromString(r);else if(!(r&&r instanceof e))throw TypeError("Illegal value: "+r+" (not an integer or Long)");if("number"!=typeof n||n%1!=0)throw TypeError("Illegal offset: "+n+" (not an integer)");if((n>>>=0)<0||n+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+n+" (+0) <= "+this.buffer.byteLength)}"number"==typeof r?r=e.fromNumber(r,!1):"string"==typeof r?r=e.fromString(r,!1):!1!==r.unsigned&&(r=r.toSigned());var o=t.calculateVarint64(r),a=r.toInt()>>>0,s=r.shiftRightUnsigned(28).toInt()>>>0,u=r.shiftRightUnsigned(56).toInt()>>>0;n+=o;var l=this.buffer.byteLength;switch(n>l&&this.resize((l*=2)>n?l:n),n-=o,o){case 10:this.view[n+9]=u>>>7&1;case 9:this.view[n+8]=9!==o?128|u:127&u;case 8:this.view[n+7]=8!==o?s>>>21|128:s>>>21&127;case 7:this.view[n+6]=7!==o?s>>>14|128:s>>>14&127;case 6:this.view[n+5]=6!==o?s>>>7|128:s>>>7&127;case 5:this.view[n+4]=5!==o?128|s:127&s;case 4:this.view[n+3]=4!==o?a>>>21|128:a>>>21&127;case 3:this.view[n+2]=3!==o?a>>>14|128:a>>>14&127;case 2:this.view[n+1]=2!==o?a>>>7|128:a>>>7&127;case 1:this.view[n]=1!==o?128|a:127&a}return i?(this.offset+=o,this):o},r.writeVarint64ZigZag=function(e,r){return this.writeVarint64(t.zigZagEncode64(e),r)},r.readVarint64=function(t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+1) <= "+this.buffer.byteLength)}var n=t,i=0,o=0,a=0,s=0;if(i=127&(s=this.view[t++]),128&s&&(i|=(127&(s=this.view[t++]))<<7,(128&s||this.noAssert&&void 0===s)&&(i|=(127&(s=this.view[t++]))<<14,(128&s||this.noAssert&&void 0===s)&&(i|=(127&(s=this.view[t++]))<<21,(128&s||this.noAssert&&void 0===s)&&(o=127&(s=this.view[t++]),(128&s||this.noAssert&&void 0===s)&&(o|=(127&(s=this.view[t++]))<<7,(128&s||this.noAssert&&void 0===s)&&(o|=(127&(s=this.view[t++]))<<14,(128&s||this.noAssert&&void 0===s)&&(o|=(127&(s=this.view[t++]))<<21,(128&s||this.noAssert&&void 0===s)&&(a=127&(s=this.view[t++]),(128&s||this.noAssert&&void 0===s)&&(a|=(127&(s=this.view[t++]))<<7,128&s||this.noAssert&&void 0===s))))))))))throw Error("Buffer overrun");var u=e.fromBits(i|o<<28,o>>>4|a<<24,!1);return r?(this.offset=t,u):{value:u,length:t-n}},r.readVarint64ZigZag=function(r){var n=this.readVarint64(r);return n&&n.value instanceof e?n.value=t.zigZagDecode64(n.value):n=t.zigZagDecode64(n),n}),r.writeCString=function(e,t){var r=void 0===t;r&&(t=this.offset);var n,i=e.length;if(!this.noAssert){if("string"!=typeof e)throw TypeError("Illegal str: Not a string");for(n=0;n<i;++n)if(0===e.charCodeAt(n))throw RangeError("Illegal str: Contains NULL-characters");if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}i=f.calculateUTF16asUTF8(o(e))[1],t+=i+1;var a=this.buffer.byteLength;return t>a&&this.resize((a*=2)>t?a:t),t-=i+1,f.encodeUTF16toUTF8(o(e),function(e){this.view[t++]=e}.bind(this)),this.view[t++]=0,r?(this.offset=t,this):i},r.readCString=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+1) <= "+this.buffer.byteLength)}var r,n=e,i=-1;return f.decodeUTF8toUTF16(function(){if(0===i)return null;if(e>=this.limit)throw RangeError("Illegal range: Truncated data, "+e+" < "+this.limit);return 0===(i=this.view[e++])?null:i}.bind(this),r=a(),!0),t?(this.offset=e,r()):{string:r(),length:e-n}},r.writeIString=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("string"!=typeof e)throw TypeError("Illegal str: Not a string");if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}var n,i=t;n=f.calculateUTF16asUTF8(o(e),this.noAssert)[1],t+=4+n;var a=this.buffer.byteLength;if(t>a&&this.resize((a*=2)>t?a:t),t-=4+n,this.littleEndian?(this.view[t+3]=n>>>24&255,this.view[t+2]=n>>>16&255,this.view[t+1]=n>>>8&255,this.view[t]=255&n):(this.view[t]=n>>>24&255,this.view[t+1]=n>>>16&255,this.view[t+2]=n>>>8&255,this.view[t+3]=255&n),t+=4,f.encodeUTF16toUTF8(o(e),function(e){this.view[t++]=e}.bind(this)),t!==i+4+n)throw RangeError("Illegal range: Truncated data, "+t+" == "+(t+4+n));return r?(this.offset=t,this):t-i},r.readIString=function(e){var r=void 0===e;if(r&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+4>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+4) <= "+this.buffer.byteLength)}var n=e,i=this.readUint32(e),o=this.readUTF8String(i,t.METRICS_BYTES,e+=4);return e+=o.length,r?(this.offset=e,o.string):{string:o.string,length:e-n}},t.METRICS_CHARS="c",t.METRICS_BYTES="b",r.writeUTF8String=function(e,t){var r,n=void 0===t;if(n&&(t=this.offset),!this.noAssert){if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}var i=t;r=f.calculateUTF16asUTF8(o(e))[1],t+=r;var a=this.buffer.byteLength;return t>a&&this.resize((a*=2)>t?a:t),t-=r,f.encodeUTF16toUTF8(o(e),function(e){this.view[t++]=e}.bind(this)),n?(this.offset=t,this):t-i},r.writeString=r.writeUTF8String,t.calculateUTF8Chars=function(e){return f.calculateUTF16asUTF8(o(e))[0]},t.calculateUTF8Bytes=function(e){return f.calculateUTF16asUTF8(o(e))[1]},t.calculateString=t.calculateUTF8Bytes,r.readUTF8String=function(e,r,n){"number"==typeof r&&(n=r,r=void 0);var i=void 0===n;if(i&&(n=this.offset),void 0===r&&(r=t.METRICS_CHARS),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal length: "+e+" (not an integer)");if(e|=0,"number"!=typeof n||n%1!=0)throw TypeError("Illegal offset: "+n+" (not an integer)");if((n>>>=0)<0||n+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+n+" (+0) <= "+this.buffer.byteLength)}var o,s=0,u=n;if(r===t.METRICS_CHARS){if(o=a(),f.decodeUTF8(function(){return s<e&&n<this.limit?this.view[n++]:null}.bind(this),function(e){++s,f.UTF8toUTF16(e,o)}),s!==e)throw RangeError("Illegal range: Truncated data, "+s+" == "+e);return i?(this.offset=n,o()):{string:o(),length:n-u}}if(r===t.METRICS_BYTES){if(!this.noAssert){if("number"!=typeof n||n%1!=0)throw TypeError("Illegal offset: "+n+" (not an integer)");if((n>>>=0)<0||n+e>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+n+" (+"+e+") <= "+this.buffer.byteLength)}var l=n+e;if(f.decodeUTF8toUTF16(function(){return n<l?this.view[n++]:null}.bind(this),o=a(),this.noAssert),n!==l)throw RangeError("Illegal range: Truncated data, "+n+" == "+l);return i?(this.offset=n,o()):{string:o(),length:n-u}}throw TypeError("Unsupported metrics: "+r)},r.readString=r.readUTF8String,r.writeVString=function(e,r){var n=void 0===r;if(n&&(r=this.offset),!this.noAssert){if("string"!=typeof e)throw TypeError("Illegal str: Not a string");if("number"!=typeof r||r%1!=0)throw TypeError("Illegal offset: "+r+" (not an integer)");if((r>>>=0)<0||r+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+r+" (+0) <= "+this.buffer.byteLength)}var i,a,s=r;i=f.calculateUTF16asUTF8(o(e),this.noAssert)[1],a=t.calculateVarint32(i),r+=a+i;var u=this.buffer.byteLength;if(r>u&&this.resize((u*=2)>r?u:r),r-=a+i,r+=this.writeVarint32(i,r),f.encodeUTF16toUTF8(o(e),function(e){this.view[r++]=e}.bind(this)),r!==s+i+a)throw RangeError("Illegal range: Truncated data, "+r+" == "+(r+i+a));return n?(this.offset=r,this):r-s},r.readVString=function(e){var r=void 0===e;if(r&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+1) <= "+this.buffer.byteLength)}var n=e,i=this.readVarint32(e),o=this.readUTF8String(i.value,t.METRICS_BYTES,e+=i.length);return e+=o.length,r?(this.offset=e,o.string):{string:o.string,length:e-n}},r.append=function(e,r,n){"number"!=typeof r&&"string"==typeof r||(n=r,r=void 0);var i=void 0===n;if(i&&(n=this.offset),!this.noAssert){if("number"!=typeof n||n%1!=0)throw TypeError("Illegal offset: "+n+" (not an integer)");if((n>>>=0)<0||n+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+n+" (+0) <= "+this.buffer.byteLength)}e instanceof t||(e=t.wrap(e,r));var o=e.limit-e.offset;if(o<=0)return this;n+=o;var a=this.buffer.byteLength;return n>a&&this.resize((a*=2)>n?a:n),n-=o,this.view.set(e.view.subarray(e.offset,e.limit),n),e.offset+=o,i&&(this.offset+=o),this},r.appendTo=function(e,t){return e.append(this,t),this},r.assert=function(e){return this.noAssert=!e,this},r.capacity=function(){return this.buffer.byteLength},r.clear=function(){return this.offset=0,this.limit=this.buffer.byteLength,this.markedOffset=-1,this},r.clone=function(e){var r=new t(0,this.littleEndian,this.noAssert);return e?(r.buffer=new ArrayBuffer(this.buffer.byteLength),r.view=new Uint8Array(r.buffer)):(r.buffer=this.buffer,r.view=this.view),r.offset=this.offset,r.markedOffset=this.markedOffset,r.limit=this.limit,r},r.compact=function(e,t){if(void 0===e&&(e=this.offset),void 0===t&&(t=this.limit),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal end: Not an integer");if(t>>>=0,e<0||e>t||t>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+t+" <= "+this.buffer.byteLength)}if(0===e&&t===this.buffer.byteLength)return this;var r=t-e;if(0===r)return this.buffer=n,this.view=null,this.markedOffset>=0&&(this.markedOffset-=e),this.offset=0,this.limit=0,this;var i=new ArrayBuffer(r),o=new Uint8Array(i);return o.set(this.view.subarray(e,t)),this.buffer=i,this.view=o,this.markedOffset>=0&&(this.markedOffset-=e),this.offset=0,this.limit=r,this},r.copy=function(e,r){if(void 0===e&&(e=this.offset),void 0===r&&(r=this.limit),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!=typeof r||r%1!=0)throw TypeError("Illegal end: Not an integer");if(r>>>=0,e<0||e>r||r>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+r+" <= "+this.buffer.byteLength)}if(e===r)return new t(0,this.littleEndian,this.noAssert);var n=r-e,i=new t(n,this.littleEndian,this.noAssert);return i.offset=0,i.limit=n,i.markedOffset>=0&&(i.markedOffset-=e),this.copyTo(i,0,e,r),i},r.copyTo=function(e,r,n,i){var o,a;if(!this.noAssert&&!t.isByteBuffer(e))throw TypeError("Illegal target: Not a ByteBuffer");if(r=(a=void 0===r)?e.offset:0|r,n=(o=void 0===n)?this.offset:0|n,i=void 0===i?this.limit:0|i,r<0||r>e.buffer.byteLength)throw RangeError("Illegal target range: 0 <= "+r+" <= "+e.buffer.byteLength);if(n<0||i>this.buffer.byteLength)throw RangeError("Illegal source range: 0 <= "+n+" <= "+this.buffer.byteLength);var s=i-n;return 0===s?e:(e.ensureCapacity(r+s),e.view.set(this.view.subarray(n,i),r),o&&(this.offset+=s),a&&(e.offset+=s),this)},r.ensureCapacity=function(e){var t=this.buffer.byteLength;return t<e?this.resize((t*=2)>e?t:e):this},r.fill=function(e,t,r){var n=void 0===t;if(n&&(t=this.offset),"string"==typeof e&&e.length>0&&(e=e.charCodeAt(0)),void 0===t&&(t=this.offset),void 0===r&&(r=this.limit),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e|=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal begin: Not an integer");if(t>>>=0,"number"!=typeof r||r%1!=0)throw TypeError("Illegal end: Not an integer");if(r>>>=0,t<0||t>r||r>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+t+" <= "+r+" <= "+this.buffer.byteLength)}if(t>=r)return this;for(;t<r;)this.view[t++]=e;return n&&(this.offset=t),this},r.flip=function(){return this.limit=this.offset,this.offset=0,this},r.mark=function(e){if(e=void 0===e?this.offset:e,!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+0) <= "+this.buffer.byteLength)}return this.markedOffset=e,this},r.order=function(e){if(!this.noAssert&&"boolean"!=typeof e)throw TypeError("Illegal littleEndian: Not a boolean");return this.littleEndian=!!e,this},r.LE=function(e){return this.littleEndian=void 0===e||!!e,this},r.BE=function(e){return this.littleEndian=void 0!==e&&!e,this},r.prepend=function(e,r,n){"number"!=typeof r&&"string"==typeof r||(n=r,r=void 0);var i=void 0===n;if(i&&(n=this.offset),!this.noAssert){if("number"!=typeof n||n%1!=0)throw TypeError("Illegal offset: "+n+" (not an integer)");if((n>>>=0)<0||n+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+n+" (+0) <= "+this.buffer.byteLength)}e instanceof t||(e=t.wrap(e,r));var o=e.limit-e.offset;if(o<=0)return this;var a=o-n;if(a>0){var s=new ArrayBuffer(this.buffer.byteLength+a),u=new Uint8Array(s);u.set(this.view.subarray(n,this.buffer.byteLength),o),this.buffer=s,this.view=u,this.offset+=a,this.markedOffset>=0&&(this.markedOffset+=a),this.limit+=a,n+=a}else new Uint8Array(this.buffer);return this.view.set(e.view.subarray(e.offset,e.limit),n-o),e.offset=e.limit,i&&(this.offset-=o),this},r.prependTo=function(e,t){return e.prepend(this,t),this},r.printDebug=function(e){"function"!=typeof e&&(e=console.log.bind(console)),e(this.toString()+"\n-------------------------------------------------------------------\n"+this.toDebug(!0))},r.remaining=function(){return this.limit-this.offset},r.reset=function(){return this.markedOffset>=0?(this.offset=this.markedOffset,this.markedOffset=-1):this.offset=0,this},r.resize=function(e){if(!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal capacity: "+e+" (not an integer)");if((e|=0)<0)throw RangeError("Illegal capacity: 0 <= "+e)}if(this.buffer.byteLength<e){var t=new ArrayBuffer(e),r=new Uint8Array(t);r.set(this.view),this.buffer=t,this.view=r}return this},r.reverse=function(e,t){if(void 0===e&&(e=this.offset),void 0===t&&(t=this.limit),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal end: Not an integer");if(t>>>=0,e<0||e>t||t>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+t+" <= "+this.buffer.byteLength)}return e===t?this:(Array.prototype.reverse.call(this.view.subarray(e,t)),this)},r.skip=function(e){if(!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal length: "+e+" (not an integer)");e|=0}var t=this.offset+e;if(!this.noAssert&&(t<0||t>this.buffer.byteLength))throw RangeError("Illegal length: 0 <= "+this.offset+" + "+e+" <= "+this.buffer.byteLength);return this.offset=t,this},r.slice=function(e,t){if(void 0===e&&(e=this.offset),void 0===t&&(t=this.limit),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal end: Not an integer");if(t>>>=0,e<0||e>t||t>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+t+" <= "+this.buffer.byteLength)}var r=this.clone();return r.offset=e,r.limit=t,r},r.toBuffer=function(e){var t=this.offset,r=this.limit;if(!this.noAssert){if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: Not an integer");if(t>>>=0,"number"!=typeof r||r%1!=0)throw TypeError("Illegal limit: Not an integer");if(r>>>=0,t<0||t>r||r>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+t+" <= "+r+" <= "+this.buffer.byteLength)}if(!e&&0===t&&r===this.buffer.byteLength)return this.buffer;if(t===r)return n;var i=new ArrayBuffer(r-t);return new Uint8Array(i).set(new Uint8Array(this.buffer).subarray(t,r),0),i},r.toArrayBuffer=r.toBuffer,r.toString=function(e,t,r){if(void 0===e)return"ByteBufferAB(offset="+this.offset+",markedOffset="+this.markedOffset+",limit="+this.limit+",capacity="+this.capacity()+")";switch("number"==typeof e&&(r=t=e="utf8"),e){case"utf8":return this.toUTF8(t,r);case"base64":return this.toBase64(t,r);case"hex":return this.toHex(t,r);case"binary":return this.toBinary(t,r);case"debug":return this.toDebug();case"columns":return this.toColumns();default:throw Error("Unsupported encoding: "+e)}};var l=function(){for(var e={},t=[65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,48,49,50,51,52,53,54,55,56,57,43,47],r=[],n=0,i=t.length;n<i;++n)r[t[n]]=n;return e.encode=function(e,r){for(var n,i;null!==(n=e());)r(t[n>>2&63]),i=(3&n)<<4,null!==(n=e())?(r(t[63&((i|=n>>4&15)|n>>4&15)]),i=(15&n)<<2,null!==(n=e())?(r(t[63&(i|n>>6&3)]),r(t[63&n])):(r(t[63&i]),r(61))):(r(t[63&i]),r(61),r(61))},e.decode=function(e,t){var n,i,o;function a(e){throw Error("Illegal character code: "+e)}for(;null!==(n=e());)if(void 0===(i=r[n])&&a(n),null!==(n=e())&&(void 0===(o=r[n])&&a(n),t(i<<2>>>0|(48&o)>>4),null!==(n=e()))){if(void 0===(i=r[n])){if(61===n)break;a(n)}if(t((15&o)<<4>>>0|(60&i)>>2),null!==(n=e())){if(void 0===(o=r[n])){if(61===n)break;a(n)}t((3&i)<<6>>>0|o)}}},e.test=function(e){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(e)},e}();r.toBase64=function(e,t){if(void 0===e&&(e=this.offset),void 0===t&&(t=this.limit),t|=0,(e|=0)<0||t>this.capacity||e>t)throw RangeError("begin, end");var r;return l.encode(function(){return e<t?this.view[e++]:null}.bind(this),r=a()),r()},t.fromBase64=function(e,r){if("string"!=typeof e)throw TypeError("str");var n=new t(e.length/4*3,r),i=0;return l.decode(o(e),function(e){n.view[i++]=e}),n.limit=i,n},t.btoa=function(e){return t.fromBinary(e).toBase64()},t.atob=function(e){return t.fromBase64(e).toBinary()},r.toBinary=function(e,t){if(void 0===e&&(e=this.offset),void 0===t&&(t=this.limit),t|=0,(e|=0)<0||t>this.capacity()||e>t)throw RangeError("begin, end");if(e===t)return"";for(var r=[],n=[];e<t;)r.push(this.view[e++]),r.length>=1024&&(n.push(String.fromCharCode.apply(String,r)),r=[]);return n.join("")+String.fromCharCode.apply(String,r)},t.fromBinary=function(e,r){if("string"!=typeof e)throw TypeError("str");for(var n,i=0,o=e.length,a=new t(o,r);i<o;){if((n=e.charCodeAt(i))>255)throw RangeError("illegal char code: "+n);a.view[i++]=n}return a.limit=o,a},r.toDebug=function(e){for(var t,r=-1,n=this.buffer.byteLength,i="",o="",a="";r<n;){if(-1!==r&&(i+=(t=this.view[r])<16?"0"+t.toString(16).toUpperCase():t.toString(16).toUpperCase(),e&&(o+=t>32&&t<127?String.fromCharCode(t):".")),++r,e&&r>0&&r%16==0&&r!==n){for(;i.length<51;)i+=" ";a+=i+o+"\n",i=o=""}r===this.offset&&r===this.limit?i+=r===this.markedOffset?"!":"|":r===this.offset?i+=r===this.markedOffset?"[":"<":r===this.limit?i+=r===this.markedOffset?"]":">":i+=r===this.markedOffset?"'":e||0!==r&&r!==n?" ":""}if(e&&" "!==i){for(;i.length<51;)i+=" ";a+=i+o+"\n"}return e?a:i},t.fromDebug=function(e,r,n){for(var i,o,a=e.length,s=new t((a+1)/3|0,r,n),u=0,l=0,f=!1,c=!1,h=!1,d=!1,p=!1;u<a;){switch(i=e.charAt(u++)){case"!":if(!n){if(c||h||d){p=!0;break}c=h=d=!0}s.offset=s.markedOffset=s.limit=l,f=!1;break;case"|":if(!n){if(c||d){p=!0;break}c=d=!0}s.offset=s.limit=l,f=!1;break;case"[":if(!n){if(c||h){p=!0;break}c=h=!0}s.offset=s.markedOffset=l,f=!1;break;case"<":if(!n){if(c){p=!0;break}c=!0}s.offset=l,f=!1;break;case"]":if(!n){if(d||h){p=!0;break}d=h=!0}s.limit=s.markedOffset=l,f=!1;break;case">":if(!n){if(d){p=!0;break}d=!0}s.limit=l,f=!1;break;case"'":if(!n){if(h){p=!0;break}h=!0}s.markedOffset=l,f=!1;break;case" ":f=!1;break;default:if(!n&&f){p=!0;break}if(o=parseInt(i+e.charAt(u++),16),!n&&(isNaN(o)||o<0||o>255))throw TypeError("Illegal str: Not a debug encoded string");s.view[l++]=o,f=!0}if(p)throw TypeError("Illegal str: Invalid symbol at "+u)}if(!n){if(!c||!d)throw TypeError("Illegal str: Missing offset or limit");if(l<s.buffer.byteLength)throw TypeError("Illegal str: Not a debug encoded string (is it hex?) "+l+" < "+a)}return s},r.toHex=function(e,t){if(e=void 0===e?this.offset:e,t=void 0===t?this.limit:t,!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal end: Not an integer");if(t>>>=0,e<0||e>t||t>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+t+" <= "+this.buffer.byteLength)}for(var r,n=new Array(t-e);e<t;)(r=this.view[e++])<16?n.push("0",r.toString(16)):n.push(r.toString(16));return n.join("")},t.fromHex=function(e,r,n){if(!n){if("string"!=typeof e)throw TypeError("Illegal str: Not a string");if(e.length%2!=0)throw TypeError("Illegal str: Length not a multiple of 2")}for(var i,o=e.length,a=new t(o/2|0,r),s=0,u=0;s<o;s+=2){if(i=parseInt(e.substring(s,s+2),16),!n&&(!isFinite(i)||i<0||i>255))throw TypeError("Illegal str: Contains non-hex characters");a.view[u++]=i}return a.limit=u,a};var f=function(){var e={MAX_CODEPOINT:1114111,encodeUTF8:function(e,t){var r=null;for("number"==typeof e&&(r=e,e=function(){return null});null!==r||null!==(r=e());)r<128?t(127&r):r<2048?(t(r>>6&31|192),t(63&r|128)):r<65536?(t(r>>12&15|224),t(r>>6&63|128),t(63&r|128)):(t(r>>18&7|240),t(r>>12&63|128),t(r>>6&63|128),t(63&r|128)),r=null},decodeUTF8:function(e,t){for(var r,n,i,o,a=function(e){e=e.slice(0,e.indexOf(null));var t=Error(e.toString());throw t.name="TruncatedError",t.bytes=e,t};null!==(r=e());)if(0==(128&r))t(r);else if(192==(224&r))null===(n=e())&&a([r,n]),t((31&r)<<6|63&n);else if(224==(240&r))(null===(n=e())||null===(i=e()))&&a([r,n,i]),t((15&r)<<12|(63&n)<<6|63&i);else{if(240!=(248&r))throw RangeError("Illegal starting byte: "+r);(null===(n=e())||null===(i=e())||null===(o=e()))&&a([r,n,i,o]),t((7&r)<<18|(63&n)<<12|(63&i)<<6|63&o)}},UTF16toUTF8:function(e,t){for(var r,n=null;null!==(r=null!==n?n:e());)r>=55296&&r<=57343&&null!==(n=e())&&n>=56320&&n<=57343?(t(1024*(r-55296)+n-56320+65536),n=null):t(r);null!==n&&t(n)},UTF8toUTF16:function(e,t){var r=null;for("number"==typeof e&&(r=e,e=function(){return null});null!==r||null!==(r=e());)r<=65535?t(r):(t(55296+((r-=65536)>>10)),t(r%1024+56320)),r=null},encodeUTF16toUTF8:function(t,r){e.UTF16toUTF8(t,function(t){e.encodeUTF8(t,r)})},decodeUTF8toUTF16:function(t,r){e.decodeUTF8(t,function(t){e.UTF8toUTF16(t,r)})},calculateCodePoint:function(e){return e<128?1:e<2048?2:e<65536?3:4},calculateUTF8:function(e){for(var t,r=0;null!==(t=e());)r+=t<128?1:t<2048?2:t<65536?3:4;return r},calculateUTF16asUTF8:function(t){var r=0,n=0;return e.UTF16toUTF8(t,function(e){++r,n+=e<128?1:e<2048?2:e<65536?3:4}),[r,n]}};return e}();return r.toUTF8=function(e,t){if(void 0===e&&(e=this.offset),void 0===t&&(t=this.limit),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal end: Not an integer");if(t>>>=0,e<0||e>t||t>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+t+" <= "+this.buffer.byteLength)}var r;try{f.decodeUTF8toUTF16(function(){return e<t?this.view[e++]:null}.bind(this),r=a())}catch(r){if(e!==t)throw RangeError("Illegal range: Truncated data, "+e+" != "+t)}return r()},t.fromUTF8=function(e,r,n){if(!n&&"string"!=typeof e)throw TypeError("Illegal str: Not a string");var i=new t(f.calculateUTF16asUTF8(o(e),!0)[1],r,n),a=0;return f.encodeUTF16toUTF8(o(e),function(e){i.view[a++]=e}),i.limit=a,i},t},"function"==typeof e&&"object"==typeof t&&t&&t.exports?t.exports=function(){var t;try{t=e("long")}catch(e){}return i(t)}():(n.dcodeIO=n.dcodeIO||{}).ByteBuffer=i(n.dcodeIO.Long)},{long:75}],26:[function(e,t,r){"use strict";var n=e("get-intrinsic"),i=e("./"),o=i(n("String.prototype.indexOf"));t.exports=function(e,t){var r=n(e,!!t);return"function"==typeof r&&o(e,".prototype.")>-1?i(r):r}},{"./":27,"get-intrinsic":59}],27:[function(e,t,r){"use strict";var n=e("function-bind"),i=e("get-intrinsic"),o=e("set-function-length"),a=i("%TypeError%"),s=i("%Function.prototype.apply%"),u=i("%Function.prototype.call%"),l=i("%Reflect.apply%",!0)||n.call(u,s),f=i("%Object.defineProperty%",!0),c=i("%Math.max%");if(f)try{f({},"a",{value:1})}catch(e){f=null}t.exports=function(e){if("function"!=typeof e)throw new a("a function is required");var t=l(n,u,arguments);return o(t,1+c(0,e.length-(arguments.length-1)),!0)};var h=function(){return l(n,s,arguments)};f?f(t.exports,"apply",{value:h}):t.exports.apply=h},{"function-bind":58,"get-intrinsic":59,"set-function-length":113}],28:[function(e,t,r){(function(r){(function(){"use strict";(function(){var n,i,o,a,s,u,l,f=[].indexOf||function(e){for(var t=0,r=this.length;t<r;t++)if(t in this&&this[t]===e)return t;return-1};s=e("stream-to-buffer"),n=e("bufferpack"),u=e("streamifier"),l=e("zlib"),i=e("crc"),o=["CgBI","iDOT"],t.exports=function(e,t){return s(e,function(e,r){var n;if(e)return t(e);try{return n=a(r),t(null,u.createReadStream(n))}catch(e){return t(e)}})},t.exports.revert=a=function(e){let t,a,s,u,c,h,d,p,g,y,b,m,v,w,_,E=!1,x=0,T=[],k=new r(0),N=e.slice(0,8);if(x+=8,"iVBORw0KGgo="!==N.toString("base64"))throw new Error("not a png file");for(;x<e.length;){u={};let N=e.slice(x,x+4);x+=4,u.length=n.unpack("L>",N,0)[0],N=e.slice(x,x+4),x+=4,u.type=N.toString(),u.data=N=e.slice(x,x+u.length),x+=u.length;let S=e.slice(x,x+4);if(x+=4,u.crc=n.unpack("L>",S,0)[0],"CgBI"===u.type&&(E=!0),t=u.type,!(f.call(o,t)>=0))if("IHDR"===u.type&&(a=n.unpack("L>",N)[0],s=n.unpack("L>",N,4)[0]),"IDAT"===u.type&&E)k=r.concat([k,N]);else{if("IEND"===u.type&&E){c=l.inflateRawSync(k),h=new r(c.length);let e=0;for(p=d=0,g=s-1;0<=g?d<=g:d>=g;p=0<=g?++d:--d)for(h[e]=c[e],e++,m=b=0,y=a-1;0<=y?b<=y:b>=y;m=0<=y?++b:--b)h[e+0]=c[e+2],h[e+1]=c[e+1],h[e+2]=c[e+0],h[e+3]=c[e+3],e+=4;v=l.deflateSync(h),w=i.crc32("IDAT"),w=((w=i.crc32(v,w))+4294967296)%4294967296,_={type:"IDAT",length:v.length,data:v,crc:w},T.push(_)}T.push(u)}}let S=N;for(let e=0,t=T.length;e<t;e++)u=T[e],S=r.concat([S,n.pack("L>",[u.length])]),S=r.concat([S,new r(u.type)]),u.length>0&&(S=r.concat([S,new r(u.data)])),S=r.concat([S,n.pack("L>",[u.crc])]);return S}}).call(this)}).call(this)}).call(this,e("buffer").Buffer)},{buffer:23,bufferpack:24,crc:54,"stream-to-buffer":115,streamifier:117,zlib:21}],29:[function(e,t,r){function n(e){return Object.prototype.toString.call(e)}r.isArray=function(e){return Array.isArray?Array.isArray(e):"[object Array]"===n(e)},r.isBoolean=function(e){return"boolean"==typeof e},r.isNull=function(e){return null===e},r.isNullOrUndefined=function(e){return null==e},r.isNumber=function(e){return"number"==typeof e},r.isString=function(e){return"string"==typeof e},r.isSymbol=function(e){return"symbol"==typeof e},r.isUndefined=function(e){return void 0===e},r.isRegExp=function(e){return"[object RegExp]"===n(e)},r.isObject=function(e){return"object"==typeof e&&null!==e},r.isDate=function(e){return"[object Date]"===n(e)},r.isError=function(e){return"[object Error]"===n(e)||e instanceof Error},r.isFunction=function(e){return"function"==typeof e},r.isPrimitive=function(e){return null===e||"boolean"==typeof e||"number"==typeof e||"string"==typeof e||"symbol"==typeof e||void 0===e},r.isBuffer=e("buffer").Buffer.isBuffer},{buffer:23}],30:[function(e,t,r){"use strict";t.exports=e("./es6/crc1").default},{"./es6/crc1":41}],31:[function(e,t,r){"use strict";t.exports=e("./es6/crc16").default},{"./es6/crc16":42}],32:[function(e,t,r){"use strict";t.exports=e("./es6/crc16ccitt").default},{"./es6/crc16ccitt":43}],33:[function(e,t,r){"use strict";t.exports=e("./es6/crc16kermit").default},{"./es6/crc16kermit":44}],34:[function(e,t,r){"use strict";t.exports=e("./es6/crc16modbus").default},{"./es6/crc16modbus":45}],35:[function(e,t,r){"use strict";t.exports=e("./es6/crc16xmodem").default},{"./es6/crc16xmodem":46}],36:[function(e,t,r){"use strict";t.exports=e("./es6/crc24").default},{"./es6/crc24":47}],37:[function(e,t,r){"use strict";t.exports=e("./es6/crc32").default},{"./es6/crc32":48}],38:[function(e,t,r){"use strict";t.exports=e("./es6/crc8").default},{"./es6/crc8":49}],39:[function(e,t,r){"use strict";t.exports=e("./es6/crc81wire").default},{"./es6/crc81wire":50}],40:[function(e,t,r){"use strict";t.exports=e("./es6/crcjam").default},{"./es6/crcjam":51}],41:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=e("buffer"),i=o(e("./create_buffer"));function o(e){return e&&e.__esModule?e:{default:e}}var a=(0,o(e("./define_crc")).default)("crc1",function(e,t){n.Buffer.isBuffer(e)||(e=(0,i.default)(e));for(var r=~~t,o=0,a=0;a<e.length;a++){o+=e[a]}return(r+=o%256)%256});r.default=a},{"./create_buffer":52,"./define_crc":53,buffer:23}],42:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=e("buffer"),i=a(e("./create_buffer")),o=a(e("./define_crc"));function a(e){return e&&e.__esModule?e:{default:e}}var s=[0,49345,49537,320,49921,960,640,49729,50689,1728,1920,51009,1280,50625,50305,1088,52225,3264,3456,52545,3840,53185,52865,3648,2560,51905,52097,2880,51457,2496,2176,51265,55297,6336,6528,55617,6912,56257,55937,6720,7680,57025,57217,8e3,56577,7616,7296,56385,5120,54465,54657,5440,55041,6080,5760,54849,53761,4800,4992,54081,4352,53697,53377,4160,61441,12480,12672,61761,13056,62401,62081,12864,13824,63169,63361,14144,62721,13760,13440,62529,15360,64705,64897,15680,65281,16320,16e3,65089,64001,15040,15232,64321,14592,63937,63617,14400,10240,59585,59777,10560,60161,11200,10880,59969,60929,11968,12160,61249,11520,60865,60545,11328,58369,9408,9600,58689,9984,59329,59009,9792,8704,58049,58241,9024,57601,8640,8320,57409,40961,24768,24960,41281,25344,41921,41601,25152,26112,42689,42881,26432,42241,26048,25728,42049,27648,44225,44417,27968,44801,28608,28288,44609,43521,27328,27520,43841,26880,43457,43137,26688,30720,47297,47489,31040,47873,31680,31360,47681,48641,32448,32640,48961,32e3,48577,48257,31808,46081,29888,30080,46401,30464,47041,46721,30272,29184,45761,45953,29504,45313,29120,28800,45121,20480,37057,37249,20800,37633,21440,21120,37441,38401,22208,22400,38721,21760,38337,38017,21568,39937,23744,23936,40257,24320,40897,40577,24128,23040,39617,39809,23360,39169,22976,22656,38977,34817,18624,18816,35137,19200,35777,35457,19008,19968,36545,36737,20288,36097,19904,19584,35905,17408,33985,34177,17728,34561,18368,18048,34369,33281,17088,17280,33601,16640,33217,32897,16448];"undefined"!=typeof Int32Array&&(s=new Int32Array(s));var u=(0,o.default)("crc-16",function(e,t){n.Buffer.isBuffer(e)||(e=(0,i.default)(e));for(var r=~~t,o=0;o<e.length;o++){var a=e[o];r=65535&(s[255&(r^a)]^r>>8)}return r});r.default=u},{"./create_buffer":52,"./define_crc":53,buffer:23}],43:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=e("buffer"),i=a(e("./create_buffer")),o=a(e("./define_crc"));function a(e){return e&&e.__esModule?e:{default:e}}var s=[0,4129,8258,12387,16516,20645,24774,28903,33032,37161,41290,45419,49548,53677,57806,61935,4657,528,12915,8786,21173,17044,29431,25302,37689,33560,45947,41818,54205,50076,62463,58334,9314,13379,1056,5121,25830,29895,17572,21637,42346,46411,34088,38153,58862,62927,50604,54669,13907,9842,5649,1584,30423,26358,22165,18100,46939,42874,38681,34616,63455,59390,55197,51132,18628,22757,26758,30887,2112,6241,10242,14371,51660,55789,59790,63919,35144,39273,43274,47403,23285,19156,31415,27286,6769,2640,14899,10770,56317,52188,64447,60318,39801,35672,47931,43802,27814,31879,19684,23749,11298,15363,3168,7233,60846,64911,52716,56781,44330,48395,36200,40265,32407,28342,24277,20212,15891,11826,7761,3696,65439,61374,57309,53244,48923,44858,40793,36728,37256,33193,45514,41451,53516,49453,61774,57711,4224,161,12482,8419,20484,16421,28742,24679,33721,37784,41979,46042,49981,54044,58239,62302,689,4752,8947,13010,16949,21012,25207,29270,46570,42443,38312,34185,62830,58703,54572,50445,13538,9411,5280,1153,29798,25671,21540,17413,42971,47098,34713,38840,59231,63358,50973,55100,9939,14066,1681,5808,26199,30326,17941,22068,55628,51565,63758,59695,39368,35305,47498,43435,22596,18533,30726,26663,6336,2273,14466,10403,52093,56156,60223,64286,35833,39896,43963,48026,19061,23124,27191,31254,2801,6864,10931,14994,64814,60687,56684,52557,48554,44427,40424,36297,31782,27655,23652,19525,15522,11395,7392,3265,61215,65342,53085,57212,44955,49082,36825,40952,28183,32310,20053,24180,11923,16050,3793,7920];"undefined"!=typeof Int32Array&&(s=new Int32Array(s));var u=(0,o.default)("ccitt",function(e,t){n.Buffer.isBuffer(e)||(e=(0,i.default)(e));for(var r=void 0!==t?~~t:65535,o=0;o<e.length;o++){var a=e[o];r=65535&(s[255&(r>>8^a)]^r<<8)}return r});r.default=u},{"./create_buffer":52,"./define_crc":53,buffer:23}],44:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=e("buffer"),i=a(e("./create_buffer")),o=a(e("./define_crc"));function a(e){return e&&e.__esModule?e:{default:e}}var s=[0,4489,8978,12955,17956,22445,25910,29887,35912,40385,44890,48851,51820,56293,59774,63735,4225,264,13203,8730,22181,18220,30135,25662,40137,36160,49115,44626,56045,52068,63999,59510,8450,12427,528,5017,26406,30383,17460,21949,44362,48323,36440,40913,60270,64231,51324,55797,12675,8202,4753,792,30631,26158,21685,17724,48587,44098,40665,36688,64495,60006,55549,51572,16900,21389,24854,28831,1056,5545,10034,14011,52812,57285,60766,64727,34920,39393,43898,47859,21125,17164,29079,24606,5281,1320,14259,9786,57037,53060,64991,60502,39145,35168,48123,43634,25350,29327,16404,20893,9506,13483,1584,6073,61262,65223,52316,56789,43370,47331,35448,39921,29575,25102,20629,16668,13731,9258,5809,1848,65487,60998,56541,52564,47595,43106,39673,35696,33800,38273,42778,46739,49708,54181,57662,61623,2112,6601,11090,15067,20068,24557,28022,31999,38025,34048,47003,42514,53933,49956,61887,57398,6337,2376,15315,10842,24293,20332,32247,27774,42250,46211,34328,38801,58158,62119,49212,53685,10562,14539,2640,7129,28518,32495,19572,24061,46475,41986,38553,34576,62383,57894,53437,49460,14787,10314,6865,2904,32743,28270,23797,19836,50700,55173,58654,62615,32808,37281,41786,45747,19012,23501,26966,30943,3168,7657,12146,16123,54925,50948,62879,58390,37033,33056,46011,41522,23237,19276,31191,26718,7393,3432,16371,11898,59150,63111,50204,54677,41258,45219,33336,37809,27462,31439,18516,23005,11618,15595,3696,8185,63375,58886,54429,50452,45483,40994,37561,33584,31687,27214,22741,18780,15843,11370,7921,3960];"undefined"!=typeof Int32Array&&(s=new Int32Array(s));var u=(0,o.default)("kermit",function(e,t){n.Buffer.isBuffer(e)||(e=(0,i.default)(e));for(var r=void 0!==t?~~t:0,o=0;o<e.length;o++){var a=e[o];r=65535&(s[255&(r^a)]^r>>8)}return r});r.default=u},{"./create_buffer":52,"./define_crc":53,buffer:23}],45:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=e("buffer"),i=a(e("./create_buffer")),o=a(e("./define_crc"));function a(e){return e&&e.__esModule?e:{default:e}}var s=[0,49345,49537,320,49921,960,640,49729,50689,1728,1920,51009,1280,50625,50305,1088,52225,3264,3456,52545,3840,53185,52865,3648,2560,51905,52097,2880,51457,2496,2176,51265,55297,6336,6528,55617,6912,56257,55937,6720,7680,57025,57217,8e3,56577,7616,7296,56385,5120,54465,54657,5440,55041,6080,5760,54849,53761,4800,4992,54081,4352,53697,53377,4160,61441,12480,12672,61761,13056,62401,62081,12864,13824,63169,63361,14144,62721,13760,13440,62529,15360,64705,64897,15680,65281,16320,16e3,65089,64001,15040,15232,64321,14592,63937,63617,14400,10240,59585,59777,10560,60161,11200,10880,59969,60929,11968,12160,61249,11520,60865,60545,11328,58369,9408,9600,58689,9984,59329,59009,9792,8704,58049,58241,9024,57601,8640,8320,57409,40961,24768,24960,41281,25344,41921,41601,25152,26112,42689,42881,26432,42241,26048,25728,42049,27648,44225,44417,27968,44801,28608,28288,44609,43521,27328,27520,43841,26880,43457,43137,26688,30720,47297,47489,31040,47873,31680,31360,47681,48641,32448,32640,48961,32e3,48577,48257,31808,46081,29888,30080,46401,30464,47041,46721,30272,29184,45761,45953,29504,45313,29120,28800,45121,20480,37057,37249,20800,37633,21440,21120,37441,38401,22208,22400,38721,21760,38337,38017,21568,39937,23744,23936,40257,24320,40897,40577,24128,23040,39617,39809,23360,39169,22976,22656,38977,34817,18624,18816,35137,19200,35777,35457,19008,19968,36545,36737,20288,36097,19904,19584,35905,17408,33985,34177,17728,34561,18368,18048,34369,33281,17088,17280,33601,16640,33217,32897,16448];"undefined"!=typeof Int32Array&&(s=new Int32Array(s));var u=(0,o.default)("crc-16-modbus",function(e,t){n.Buffer.isBuffer(e)||(e=(0,i.default)(e));for(var r=void 0!==t?~~t:65535,o=0;o<e.length;o++){var a=e[o];r=65535&(s[255&(r^a)]^r>>8)}return r});r.default=u},{"./create_buffer":52,"./define_crc":53,buffer:23}],46:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=e("buffer"),i=o(e("./create_buffer"));function o(e){return e&&e.__esModule?e:{default:e}}var a=(0,o(e("./define_crc")).default)("xmodem",function(e,t){n.Buffer.isBuffer(e)||(e=(0,i.default)(e));for(var r=void 0!==t?~~t:0,o=0;o<e.length;o++){var a=r>>>8&255;a^=255&e[o],r=r<<8&65535,r^=a^=a>>>4,r^=a=a<<5&65535,r^=a=a<<7&65535}return r});r.default=a},{"./create_buffer":52,"./define_crc":53,buffer:23}],47:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=e("buffer"),i=a(e("./create_buffer")),o=a(e("./define_crc"));function a(e){return e&&e.__esModule?e:{default:e}}var s=[0,8801531,9098509,825846,9692897,1419802,1651692,10452759,10584377,2608578,2839604,11344079,3303384,11807523,12104405,4128302,12930697,4391538,5217156,13227903,5679208,13690003,14450021,5910942,6606768,14844747,15604413,6837830,16197969,7431594,8256604,16494759,840169,9084178,8783076,18463,10434312,1670131,1434117,9678590,11358416,2825259,2590173,10602790,4109873,12122826,11821884,3289031,13213536,5231515,4409965,12912278,5929345,14431610,13675660,5693559,6823513,15618722,14863188,6588335,16513208,8238147,7417269,16212302,1680338,10481449,9664223,1391140,9061683,788936,36926,8838341,12067563,4091408,3340262,11844381,2868234,11372785,10555655,2579964,14478683,5939616,5650518,13661357,5180346,13190977,12967607,4428364,8219746,16457881,16234863,7468436,15633027,6866552,6578062,14816117,1405499,9649856,10463030,1698765,8819930,55329,803287,9047340,11858690,3325945,4072975,12086004,2561507,10574104,11387118,2853909,13647026,5664841,5958079,14460228,4446803,12949160,13176670,5194661,7454091,16249200,16476294,8201341,14834538,6559633,6852199,15647388,3360676,11864927,12161705,4185682,10527045,2551230,2782280,11286707,9619101,1346150,1577872,10379115,73852,8875143,9172337,899466,16124205,7357910,8182816,16421083,6680524,14918455,15678145,6911546,5736468,13747439,14507289,5968354,12873461,4334094,5159928,13170435,4167245,12180150,11879232,3346363,11301036,2767959,2532769,10545498,10360692,1596303,1360505,9604738,913813,9157998,8856728,92259,16439492,8164415,7343561,16138546,6897189,15692510,14936872,6662099,5986813,14488838,13733104,5750795,13156124,5174247,4352529,12855018,2810998,11315341,10498427,2522496,12124823,4148844,3397530,11901793,9135439,862644,110658,8912057,1606574,10407765,9590435,1317464,15706879,6940164,6651890,14889737,8145950,16384229,16161043,7394792,5123014,13133629,12910283,4370992,14535975,5997020,5707818,13718737,2504095,10516836,11329682,2796649,11916158,3383173,4130419,12143240,8893606,129117,876971,9121104,1331783,9576124,10389322,1625009,14908182,6633453,6925851,15721184,7380471,16175372,16402682,8127489,4389423,12891860,13119266,5137369,13704398,5722165,6015427,14517560];"undefined"!=typeof Int32Array&&(s=new Int32Array(s));var u=(0,o.default)("crc-24",function(e,t){n.Buffer.isBuffer(e)||(e=(0,i.default)(e));for(var r=void 0!==t?~~t:11994318,o=0;o<e.length;o++){var a=e[o];r=16777215&(s[255&(r>>16^a)]^r<<8)}return r});r.default=u},{"./create_buffer":52,"./define_crc":53,buffer:23}],48:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=e("buffer"),i=a(e("./create_buffer")),o=a(e("./define_crc"));function a(e){return e&&e.__esModule?e:{default:e}}var s=[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117];"undefined"!=typeof Int32Array&&(s=new Int32Array(s));var u=(0,o.default)("crc-32",function(e,t){n.Buffer.isBuffer(e)||(e=(0,i.default)(e));for(var r=0===t?0:-1^~~t,o=0;o<e.length;o++){var a=e[o];r=s[255&(r^a)]^r>>>8}return-1^r});r.default=u},{"./create_buffer":52,"./define_crc":53,buffer:23}],49:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=e("buffer"),i=a(e("./create_buffer")),o=a(e("./define_crc"));function a(e){return e&&e.__esModule?e:{default:e}}var s=[0,7,14,9,28,27,18,21,56,63,54,49,36,35,42,45,112,119,126,121,108,107,98,101,72,79,70,65,84,83,90,93,224,231,238,233,252,251,242,245,216,223,214,209,196,195,202,205,144,151,158,153,140,139,130,133,168,175,166,161,180,179,186,189,199,192,201,206,219,220,213,210,255,248,241,246,227,228,237,234,183,176,185,190,171,172,165,162,143,136,129,134,147,148,157,154,39,32,41,46,59,60,53,50,31,24,17,22,3,4,13,10,87,80,89,94,75,76,69,66,111,104,97,102,115,116,125,122,137,142,135,128,149,146,155,156,177,182,191,184,173,170,163,164,249,254,247,240,229,226,235,236,193,198,207,200,221,218,211,212,105,110,103,96,117,114,123,124,81,86,95,88,77,74,67,68,25,30,23,16,5,2,11,12,33,38,47,40,61,58,51,52,78,73,64,71,82,85,92,91,118,113,120,127,106,109,100,99,62,57,48,55,34,37,44,43,6,1,8,15,26,29,20,19,174,169,160,167,178,181,188,187,150,145,152,159,138,141,132,131,222,217,208,215,194,197,204,203,230,225,232,239,250,253,244,243];"undefined"!=typeof Int32Array&&(s=new Int32Array(s));var u=(0,o.default)("crc-8",function(e,t){n.Buffer.isBuffer(e)||(e=(0,i.default)(e));for(var r=~~t,o=0;o<e.length;o++){var a=e[o];r=255&s[255&(r^a)]}return r});r.default=u},{"./create_buffer":52,"./define_crc":53,buffer:23}],50:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=e("buffer"),i=a(e("./create_buffer")),o=a(e("./define_crc"));function a(e){return e&&e.__esModule?e:{default:e}}var s=[0,94,188,226,97,63,221,131,194,156,126,32,163,253,31,65,157,195,33,127,252,162,64,30,95,1,227,189,62,96,130,220,35,125,159,193,66,28,254,160,225,191,93,3,128,222,60,98,190,224,2,92,223,129,99,61,124,34,192,158,29,67,161,255,70,24,250,164,39,121,155,197,132,218,56,102,229,187,89,7,219,133,103,57,186,228,6,88,25,71,165,251,120,38,196,154,101,59,217,135,4,90,184,230,167,249,27,69,198,152,122,36,248,166,68,26,153,199,37,123,58,100,134,216,91,5,231,185,140,210,48,110,237,179,81,15,78,16,242,172,47,113,147,205,17,79,173,243,112,46,204,146,211,141,111,49,178,236,14,80,175,241,19,77,206,144,114,44,109,51,209,143,12,82,176,238,50,108,142,208,83,13,239,177,240,174,76,18,145,207,45,115,202,148,118,40,171,245,23,73,8,86,180,234,105,55,213,139,87,9,235,181,54,104,138,212,149,203,41,119,244,170,72,22,233,183,85,11,136,214,52,106,43,117,151,201,74,20,246,168,116,42,200,150,21,75,169,247,182,232,10,84,215,137,107,53];"undefined"!=typeof Int32Array&&(s=new Int32Array(s));var u=(0,o.default)("dallas-1-wire",function(e,t){n.Buffer.isBuffer(e)||(e=(0,i.default)(e));for(var r=~~t,o=0;o<e.length;o++){var a=e[o];r=255&s[255&(r^a)]}return r});r.default=u},{"./create_buffer":52,"./define_crc":53,buffer:23}],51:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=e("buffer"),i=a(e("./create_buffer")),o=a(e("./define_crc"));function a(e){return e&&e.__esModule?e:{default:e}}var s=[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117];"undefined"!=typeof Int32Array&&(s=new Int32Array(s));var u=(0,o.default)("jam",function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:-1;n.Buffer.isBuffer(e)||(e=(0,i.default)(e));for(var r=0===t?0:~~t,o=0;o<e.length;o++){var a=e[o];r=s[255&(r^a)]^r>>>8}return r});r.default=u},{"./create_buffer":52,"./define_crc":53,buffer:23}],52:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=e("buffer"),i=n.Buffer.from&&n.Buffer.alloc&&n.Buffer.allocUnsafe&&n.Buffer.allocUnsafeSlow?n.Buffer.from:function(e){return new n.Buffer(e)};r.default=i},{buffer:23}],53:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,t){var r=function(e,r){return t(e,r)>>>0};return r.signed=t,r.unsigned=r,r.model=e,r}},{}],54:[function(e,t,r){"use strict";t.exports={crc1:e("./crc1"),crc8:e("./crc8"),crc81wire:e("./crc8_1wire"),crc16:e("./crc16"),crc16ccitt:e("./crc16_ccitt"),crc16modbus:e("./crc16_modbus"),crc16xmodem:e("./crc16_xmodem"),crc16kermit:e("./crc16_kermit"),crc24:e("./crc24"),crc32:e("./crc32"),crcjam:e("./crcjam")}},{"./crc1":30,"./crc16":31,"./crc16_ccitt":32,"./crc16_kermit":33,"./crc16_modbus":34,"./crc16_xmodem":35,"./crc24":36,"./crc32":37,"./crc8":38,"./crc8_1wire":39,"./crcjam":40}],55:[function(e,t,r){"use strict";var n=e("has-property-descriptors")(),i=e("get-intrinsic"),o=n&&i("%Object.defineProperty%",!0);if(o)try{o({},"a",{value:1})}catch(e){o=!1}var a=i("%SyntaxError%"),s=i("%TypeError%"),u=e("gopd");t.exports=function(e,t,r){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new s("`obj` must be an object or a function`");if("string"!=typeof t&&"symbol"!=typeof t)throw new s("`property` must be a string or a symbol`");if(arguments.length>3&&"boolean"!=typeof arguments[3]&&null!==arguments[3])throw new s("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&"boolean"!=typeof arguments[4]&&null!==arguments[4])throw new s("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&"boolean"!=typeof arguments[5]&&null!==arguments[5])throw new s("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&"boolean"!=typeof arguments[6])throw new s("`loose`, if provided, must be a boolean");var n=arguments.length>3?arguments[3]:null,i=arguments.length>4?arguments[4]:null,l=arguments.length>5?arguments[5]:null,f=arguments.length>6&&arguments[6],c=!!u&&u(e,t);if(o)o(e,t,{configurable:null===l&&c?c.configurable:!l,enumerable:null===n&&c?c.enumerable:!n,value:r,writable:null===i&&c?c.writable:!i});else{if(!f&&(n||i||l))throw new a("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.");e[t]=r}}},{"get-intrinsic":59,gopd:60,"has-property-descriptors":61}],56:[function(e,t,r){var n=Object.create||function(e){var t=function(){};return t.prototype=e,new t},i=Object.keys||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return r},o=Function.prototype.bind||function(e){var t=this;return function(){return t.apply(e,arguments)}};function a(){this._events&&Object.prototype.hasOwnProperty.call(this,"_events")||(this._events=n(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0}t.exports=a,a.EventEmitter=a,a.prototype._events=void 0,a.prototype._maxListeners=void 0;var s,u=10;try{var l={};Object.defineProperty&&Object.defineProperty(l,"x",{value:0}),s=0===l.x}catch(e){s=!1}function f(e){return void 0===e._maxListeners?a.defaultMaxListeners:e._maxListeners}function c(e,t,r,i){var o,a,s;if("function"!=typeof r)throw new TypeError('"listener" argument must be a function');if((a=e._events)?(a.newListener&&(e.emit("newListener",t,r.listener?r.listener:r),a=e._events),s=a[t]):(a=e._events=n(null),e._eventsCount=0),s){if("function"==typeof s?s=a[t]=i?[r,s]:[s,r]:i?s.unshift(r):s.push(r),!s.warned&&(o=f(e))&&o>0&&s.length>o){s.warned=!0;var u=new Error("Possible EventEmitter memory leak detected. "+s.length+' "'+String(t)+'" listeners added. Use emitter.setMaxListeners() to increase limit.');u.name="MaxListenersExceededWarning",u.emitter=e,u.type=t,u.count=s.length,"object"==typeof console&&console.warn&&console.warn("%s: %s",u.name,u.message)}}else s=a[t]=r,++e._eventsCount;return e}function h(){if(!this.fired)switch(this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length){case 0:return this.listener.call(this.target);case 1:return this.listener.call(this.target,arguments[0]);case 2:return this.listener.call(this.target,arguments[0],arguments[1]);case 3:return this.listener.call(this.target,arguments[0],arguments[1],arguments[2]);default:for(var e=new Array(arguments.length),t=0;t<e.length;++t)e[t]=arguments[t];this.listener.apply(this.target,e)}}function d(e,t,r){var n={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},i=o.call(h,n);return i.listener=r,n.wrapFn=i,i}function p(e,t,r){var n=e._events;if(!n)return[];var i=n[t];return i?"function"==typeof i?r?[i.listener||i]:[i]:r?function(e){for(var t=new Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(i):y(i,i.length):[]}function g(e){var t=this._events;if(t){var r=t[e];if("function"==typeof r)return 1;if(r)return r.length}return 0}function y(e,t){for(var r=new Array(t),n=0;n<t;++n)r[n]=e[n];return r}s?Object.defineProperty(a,"defaultMaxListeners",{enumerable:!0,get:function(){return u},set:function(e){if("number"!=typeof e||e<0||e!=e)throw new TypeError('"defaultMaxListeners" must be a positive number');u=e}}):a.defaultMaxListeners=u,a.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||isNaN(e))throw new TypeError('"n" argument must be a positive number');return this._maxListeners=e,this},a.prototype.getMaxListeners=function(){return f(this)},a.prototype.emit=function(e){var t,r,n,i,o,a,s="error"===e;if(a=this._events)s=s&&null==a.error;else if(!s)return!1;if(s){if(arguments.length>1&&(t=arguments[1]),t instanceof Error)throw t;var u=new Error('Unhandled "error" event. ('+t+")");throw u.context=t,u}if(!(r=a[e]))return!1;var l="function"==typeof r;switch(n=arguments.length){case 1:!function(e,t,r){if(t)e.call(r);else for(var n=e.length,i=y(e,n),o=0;o<n;++o)i[o].call(r)}(r,l,this);break;case 2:!function(e,t,r,n){if(t)e.call(r,n);else for(var i=e.length,o=y(e,i),a=0;a<i;++a)o[a].call(r,n)}(r,l,this,arguments[1]);break;case 3:!function(e,t,r,n,i){if(t)e.call(r,n,i);else for(var o=e.length,a=y(e,o),s=0;s<o;++s)a[s].call(r,n,i)}(r,l,this,arguments[1],arguments[2]);break;case 4:!function(e,t,r,n,i,o){if(t)e.call(r,n,i,o);else for(var a=e.length,s=y(e,a),u=0;u<a;++u)s[u].call(r,n,i,o)}(r,l,this,arguments[1],arguments[2],arguments[3]);break;default:for(i=new Array(n-1),o=1;o<n;o++)i[o-1]=arguments[o];!function(e,t,r,n){if(t)e.apply(r,n);else for(var i=e.length,o=y(e,i),a=0;a<i;++a)o[a].apply(r,n)}(r,l,this,i)}return!0},a.prototype.addListener=function(e,t){return c(this,e,t,!1)},a.prototype.on=a.prototype.addListener,a.prototype.prependListener=function(e,t){return c(this,e,t,!0)},a.prototype.once=function(e,t){if("function"!=typeof t)throw new TypeError('"listener" argument must be a function');return this.on(e,d(this,e,t)),this},a.prototype.prependOnceListener=function(e,t){if("function"!=typeof t)throw new TypeError('"listener" argument must be a function');return this.prependListener(e,d(this,e,t)),this},a.prototype.removeListener=function(e,t){var r,i,o,a,s;if("function"!=typeof t)throw new TypeError('"listener" argument must be a function');if(!(i=this._events))return this;if(!(r=i[e]))return this;if(r===t||r.listener===t)0==--this._eventsCount?this._events=n(null):(delete i[e],i.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(o=-1,a=r.length-1;a>=0;a--)if(r[a]===t||r[a].listener===t){s=r[a].listener,o=a;break}if(o<0)return this;0===o?r.shift():function(e,t){for(var r=t,n=r+1,i=e.length;n<i;r+=1,n+=1)e[r]=e[n];e.pop()}(r,o),1===r.length&&(i[e]=r[0]),i.removeListener&&this.emit("removeListener",e,s||t)}return this},a.prototype.removeAllListeners=function(e){var t,r,o;if(!(r=this._events))return this;if(!r.removeListener)return 0===arguments.length?(this._events=n(null),this._eventsCount=0):r[e]&&(0==--this._eventsCount?this._events=n(null):delete r[e]),this;if(0===arguments.length){var a,s=i(r);for(o=0;o<s.length;++o)"removeListener"!==(a=s[o])&&this.removeAllListeners(a);return this.removeAllListeners("removeListener"),this._events=n(null),this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(t)for(o=t.length-1;o>=0;o--)this.removeListener(e,t[o]);return this},a.prototype.listeners=function(e){return p(this,e,!0)},a.prototype.rawListeners=function(e){return p(this,e,!1)},a.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):g.call(e,t)},a.prototype.listenerCount=g,a.prototype.eventNames=function(){return this._eventsCount>0?Reflect.ownKeys(this._events):[]}},{}],57:[function(e,t,r){"use strict";var n=Object.prototype.toString,i=Math.max,o=function(e,t){for(var r=[],n=0;n<e.length;n+=1)r[n]=e[n];for(var i=0;i<t.length;i+=1)r[i+e.length]=t[i];return r};t.exports=function(e){var t=this;if("function"!=typeof t||"[object Function]"!==n.apply(t))throw new TypeError("Function.prototype.bind called on incompatible "+t);for(var r,a=function(e,t){for(var r=[],n=t||0,i=0;n<e.length;n+=1,i+=1)r[i]=e[n];return r}(arguments,1),s=i(0,t.length-a.length),u=[],l=0;l<s;l++)u[l]="$"+l;if(r=Function("binder","return function ("+function(e,t){for(var r="",n=0;n<e.length;n+=1)r+=e[n],n+1<e.length&&(r+=t);return r}(u,",")+"){ return binder.apply(this,arguments); }")(function(){if(this instanceof r){var n=t.apply(this,o(a,arguments));return Object(n)===n?n:this}return t.apply(e,o(a,arguments))}),t.prototype){var f=function(){};f.prototype=t.prototype,r.prototype=new f,f.prototype=null}return r}},{}],58:[function(e,t,r){"use strict";var n=e("./implementation");t.exports=Function.prototype.bind||n},{"./implementation":57}],59:[function(e,t,r){"use strict";var n=SyntaxError,i=Function,o=TypeError,a=function(e){try{return i('"use strict"; return ('+e+").constructor;")()}catch(e){}},s=Object.getOwnPropertyDescriptor;if(s)try{s({},"")}catch(e){s=null}var u=function(){throw new o},l=s?function(){try{return arguments.callee,u}catch(e){try{return s(arguments,"callee").get}catch(e){return u}}}():u,f=e("has-symbols")(),c=e("has-proto")(),h=Object.getPrototypeOf||(c?function(e){return e.__proto__}:null),d={},p="undefined"!=typeof Uint8Array&&h?h(Uint8Array):void 0,g={"%AggregateError%":"undefined"==typeof AggregateError?void 0:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?void 0:ArrayBuffer,"%ArrayIteratorPrototype%":f&&h?h([][Symbol.iterator]()):void 0,"%AsyncFromSyncIteratorPrototype%":void 0,"%AsyncFunction%":d,"%AsyncGenerator%":d,"%AsyncGeneratorFunction%":d,"%AsyncIteratorPrototype%":d,"%Atomics%":"undefined"==typeof Atomics?void 0:Atomics,"%BigInt%":"undefined"==typeof BigInt?void 0:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?void 0:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?void 0:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?void 0:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":"undefined"==typeof Float32Array?void 0:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?void 0:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?void 0:FinalizationRegistry,"%Function%":i,"%GeneratorFunction%":d,"%Int8Array%":"undefined"==typeof Int8Array?void 0:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?void 0:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?void 0:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":f&&h?h(h([][Symbol.iterator]())):void 0,"%JSON%":"object"==typeof JSON?JSON:void 0,"%Map%":"undefined"==typeof Map?void 0:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&f&&h?h((new Map)[Symbol.iterator]()):void 0,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?void 0:Promise,"%Proxy%":"undefined"==typeof Proxy?void 0:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":"undefined"==typeof Reflect?void 0:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?void 0:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&f&&h?h((new Set)[Symbol.iterator]()):void 0,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?void 0:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":f&&h?h(""[Symbol.iterator]()):void 0,"%Symbol%":f?Symbol:void 0,"%SyntaxError%":n,"%ThrowTypeError%":l,"%TypedArray%":p,"%TypeError%":o,"%Uint8Array%":"undefined"==typeof Uint8Array?void 0:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?void 0:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?void 0:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?void 0:Uint32Array,"%URIError%":URIError,"%WeakMap%":"undefined"==typeof WeakMap?void 0:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?void 0:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?void 0:WeakSet};if(h)try{null.error}catch(e){var y=h(h(e));g["%Error.prototype%"]=y}var b={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},m=e("function-bind"),v=e("hasown"),w=m.call(Function.call,Array.prototype.concat),_=m.call(Function.apply,Array.prototype.splice),E=m.call(Function.call,String.prototype.replace),x=m.call(Function.call,String.prototype.slice),T=m.call(Function.call,RegExp.prototype.exec),k=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,N=/\\(\\)?/g,S=function(e,t){var r,i=e;if(v(b,i)&&(i="%"+(r=b[i])[0]+"%"),v(g,i)){var s=g[i];if(s===d&&(s=function e(t){var r;if("%AsyncFunction%"===t)r=a("async function () {}");else if("%GeneratorFunction%"===t)r=a("function* () {}");else if("%AsyncGeneratorFunction%"===t)r=a("async function* () {}");else if("%AsyncGenerator%"===t){var n=e("%AsyncGeneratorFunction%");n&&(r=n.prototype)}else if("%AsyncIteratorPrototype%"===t){var i=e("%AsyncGenerator%");i&&h&&(r=h(i.prototype))}return g[t]=r,r}(i)),void 0===s&&!t)throw new o("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:i,value:s}}throw new n("intrinsic "+e+" does not exist!")};t.exports=function(e,t){if("string"!=typeof e||0===e.length)throw new o("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new o('"allowMissing" argument must be a boolean');if(null===T(/^%?[^%]*%?$/,e))throw new n("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=function(e){var t=x(e,0,1),r=x(e,-1);if("%"===t&&"%"!==r)throw new n("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==t)throw new n("invalid intrinsic syntax, expected opening `%`");var i=[];return E(e,k,function(e,t,r,n){i[i.length]=r?E(n,N,"$1"):t||e}),i}(e),i=r.length>0?r[0]:"",a=S("%"+i+"%",t),u=a.name,l=a.value,f=!1,c=a.alias;c&&(i=c[0],_(r,w([0,1],c)));for(var h=1,d=!0;h<r.length;h+=1){var p=r[h],y=x(p,0,1),b=x(p,-1);if(('"'===y||"'"===y||"`"===y||'"'===b||"'"===b||"`"===b)&&y!==b)throw new n("property names with quotes must have matching quotes");if("constructor"!==p&&d||(f=!0),v(g,u="%"+(i+="."+p)+"%"))l=g[u];else if(null!=l){if(!(p in l)){if(!t)throw new o("base intrinsic for "+e+" exists, but the property is not available.");return}if(s&&h+1>=r.length){var m=s(l,p);l=(d=!!m)&&"get"in m&&!("originalValue"in m.get)?m.get:l[p]}else d=v(l,p),l=l[p];d&&!f&&(g[u]=l)}}return l}},{"function-bind":58,"has-proto":62,"has-symbols":63,hasown:65}],60:[function(e,t,r){"use strict";var n=e("get-intrinsic")("%Object.getOwnPropertyDescriptor%",!0);if(n)try{n([],"length")}catch(e){n=null}t.exports=n},{"get-intrinsic":59}],61:[function(e,t,r){"use strict";var n=e("get-intrinsic")("%Object.defineProperty%",!0),i=function(){if(n)try{return n({},"a",{value:1}),!0}catch(e){return!1}return!1};i.hasArrayLengthDefineBug=function(){if(!i())return null;try{return 1!==n([],"length",{value:1}).length}catch(e){return!0}},t.exports=i},{"get-intrinsic":59}],62:[function(e,t,r){"use strict";var n={foo:{}},i=Object;t.exports=function(){return{__proto__:n}.foo===n.foo&&!({__proto__:null}instanceof i)}},{}],63:[function(e,t,r){"use strict";var n="undefined"!=typeof Symbol&&Symbol,i=e("./shams");t.exports=function(){return"function"==typeof n&&("function"==typeof Symbol&&("symbol"==typeof n("foo")&&("symbol"==typeof Symbol("bar")&&i())))}},{"./shams":64}],64:[function(e,t,r){"use strict";t.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t)return!1;if("[object Symbol]"!==Object.prototype.toString.call(t))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(t in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var n=Object.getOwnPropertySymbols(e);if(1!==n.length||n[0]!==t)return!1;if(!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var i=Object.getOwnPropertyDescriptor(e,t);if(42!==i.value||!0!==i.enumerable)return!1}return!0}},{}],65:[function(e,t,r){"use strict";var n=Function.prototype.call,i=Object.prototype.hasOwnProperty,o=e("function-bind");t.exports=o.call(n,i)},{"function-bind":58}],66:[function(e,t,r){r.read=function(e,t,r,n,i){var o,a,s=8*i-n-1,u=(1<<s)-1,l=u>>1,f=-7,c=r?i-1:0,h=r?-1:1,d=e[t+c];for(c+=h,o=d&(1<<-f)-1,d>>=-f,f+=s;f>0;o=256*o+e[t+c],c+=h,f-=8);for(a=o&(1<<-f)-1,o>>=-f,f+=n;f>0;a=256*a+e[t+c],c+=h,f-=8);if(0===o)o=1-l;else{if(o===u)return a?NaN:1/0*(d?-1:1);a+=Math.pow(2,n),o-=l}return(d?-1:1)*a*Math.pow(2,o-n)},r.write=function(e,t,r,n,i,o){var a,s,u,l=8*o-i-1,f=(1<<l)-1,c=f>>1,h=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=n?0:o-1,p=n?1:-1,g=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(s=isNaN(t)?1:0,a=f):(a=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-a))<1&&(a--,u*=2),(t+=a+c>=1?h/u:h*Math.pow(2,1-c))*u>=2&&(a++,u/=2),a+c>=f?(s=0,a=f):a+c>=1?(s=(t*u-1)*Math.pow(2,i),a+=c):(s=t*Math.pow(2,c-1)*Math.pow(2,i),a=0));i>=8;e[r+d]=255&s,d+=p,s/=256,i-=8);for(a=a<<i|s,l+=i;l>0;e[r+d]=255&a,d+=p,a/=256,l-=8);e[r+d-p]|=128*g}},{}],67:[function(e,t,r){"function"==typeof Object.create?t.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:t.exports=function(e,t){if(t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}}},{}],68:[function(e,t,r){function n(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}t.exports=function(e){return null!=e&&(n(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&n(e.slice(0,0))}(e)||!!e._isBuffer)}},{}],69:[function(e,t,r){var n={}.toString;t.exports=Array.isArray||function(e){return"[object Array]"==n.call(e)}},{}],70:[function(e,t,r){var n=e("buffer").Buffer;t.exports=function(e,t){if("undefined"==typeof Blob||!(e instanceof Blob))throw new Error("first argument must be a Blob");if("function"!=typeof t)throw new Error("second argument must be a function");var r=new FileReader;r.addEventListener("loadend",function e(i){r.removeEventListener("loadend",e,!1),i.error?t(i.error):t(null,new n(r.result))},!1),r.readAsArrayBuffer(e)}},{buffer:23}],71:[function(e,t,r){t.exports='function inflate(a){"use strict";function w(){function m(a,b,d,h,m,n,o,p,q,r,s){var t,u,w,x,z,A,B,C,E,F,G,H,I,D=0,y=d;do e[a[b+D]]++,D++,y--;while(0!==y);if(e[0]==d)return o[0]=-1,p[0]=0,c;for(B=p[0],z=1;v>=z&&0===e[z];z++);for(A=z,z>B&&(B=z),y=v;0!==y&&0===e[y];y--);for(w=y,B>y&&(B=y),p[0]=B,H=1<<z;y>z;z++,H<<=1)if((H-=e[z])<0)return g;if((H-=e[y])<0)return g;for(e[y]+=H,l[1]=z=0,D=1,G=2;0!==--y;)l[G]=z+=e[D],G++,D++;y=0,D=0;do 0!==(z=a[b+D])&&(s[l[z]++]=y),D++;while(++y<d);for(d=l[w],l[0]=y=0,D=0,x=-1,F=-B,j[0]=0,E=0,I=0;w>=A;A++)for(t=e[A];0!==t--;){for(;A>F+B;){if(x++,F+=B,I=w-F,I=I>B?B:I,(u=1<<(z=A-F))>t+1&&(u-=t+1,G=A,I>z))for(;++z<I&&!((u<<=1)<=e[++G]);)u-=e[G];if(I=1<<z,r[0]+I>k)return g;j[x]=E=r[0],r[0]+=I,0!==x?(l[x]=y,f[0]=z,f[1]=B,z=y>>>F-B,f[2]=E-j[x-1]-z,q.set(f,3*(j[x-1]+z))):o[0]=E}for(f[1]=A-F,D>=d?f[0]=192:s[D]<h?(f[0]=s[D]<256?0:96,f[2]=s[D++]):(f[0]=n[s[D]-h]+16+64,f[2]=m[s[D++]-h]),u=1<<A-F,z=y>>>F;I>z;z+=u)q.set(f,3*(E+z));for(z=1<<A-1;0!==(y&z);z>>>=1)y^=z;for(y^=z,C=(1<<F)-1;(y&C)!=l[x];)x--,F-=B,C=(1<<F)-1}return 0!==H&&1!=w?i:c}function n(a){var c;for(b||(b=[],d=[],e=new Int32Array(v+1),f=[],j=new Int32Array(v),l=new Int32Array(v+1)),d.length<a&&(d=[]),c=0;a>c;c++)d[c]=0;for(c=0;v+1>c;c++)e[c]=0;for(c=0;3>c;c++)f[c]=0;j.set(e.subarray(0,v),0),l.set(e.subarray(0,v+1),0)}var b,d,e,f,j,l,a=this;a.inflate_trees_bits=function(a,c,e,f,h){var j;return n(19),b[0]=0,j=m(a,0,19,19,null,null,e,c,f,b,d),j==g?h.msg="oversubscribed dynamic bit lengths tree":(j==i||0===c[0])&&(h.msg="incomplete dynamic bit lengths tree",j=g),j},a.inflate_trees_dynamic=function(a,e,f,j,k,l,o,p,q){var v;return n(288),b[0]=0,v=m(f,0,a,257,r,s,l,j,p,b,d),v!=c||0===j[0]?(v==g?q.msg="oversubscribed literal/length tree":v!=h&&(q.msg="incomplete literal/length tree",v=g),v):(n(288),v=m(f,a,e,0,t,u,o,k,p,b,d),v!=c||0===k[0]&&a>257?(v==g?q.msg="oversubscribed distance tree":v==i?(q.msg="incomplete distance tree",v=g):v!=h&&(q.msg="empty distance tree with lengths",v=g),v):c)}}function H(){function u(a,b,e,f,h,i,k,l){var m,n,o,p,y,z,A,B,s=l.next_in_index,t=l.avail_in,q=k.bitb,r=k.bitk,u=k.write,v=u<k.read?k.read-u-1:k.end-u,w=j[a],x=j[b];do{for(;20>r;)t--,q|=(255&l.read_byte(s++))<<r,r+=8;if(m=q&w,n=e,o=f,B=3*(o+m),0!==(p=n[B]))for(;;){if(q>>=n[B+1],r-=n[B+1],0!==(16&p)){for(p&=15,y=n[B+2]+(q&j[p]),q>>=p,r-=p;15>r;)t--,q|=(255&l.read_byte(s++))<<r,r+=8;for(m=q&x,n=h,o=i,B=3*(o+m),p=n[B];;){if(q>>=n[B+1],r-=n[B+1],0!==(16&p)){for(p&=15;p>r;)t--,q|=(255&l.read_byte(s++))<<r,r+=8;if(z=n[B+2]+(q&j[p]),q>>=p,r-=p,v-=y,u>=z)A=u-z,u-A>0&&2>u-A?(k.window[u++]=k.window[A++],k.window[u++]=k.window[A++],y-=2):(k.window.set(k.window.subarray(A,A+2),u),u+=2,A+=2,y-=2);else{A=u-z;do A+=k.end;while(0>A);if(p=k.end-A,y>p){if(y-=p,u-A>0&&p>u-A){do k.window[u++]=k.window[A++];while(0!==--p)}else k.window.set(k.window.subarray(A,A+p),u),u+=p,A+=p,p=0;A=0}}if(u-A>0&&y>u-A){do k.window[u++]=k.window[A++];while(0!==--y)}else k.window.set(k.window.subarray(A,A+y),u),u+=y,A+=y,y=0;break}if(0!==(64&p))return l.msg="invalid distance code",y=l.avail_in-t,y=y>r>>3?r>>3:y,t+=y,s-=y,r-=y<<3,k.bitb=q,k.bitk=r,l.avail_in=t,l.total_in+=s-l.next_in_index,l.next_in_index=s,k.write=u,g;m+=n[B+2],m+=q&j[p],B=3*(o+m),p=n[B]}break}if(0!==(64&p))return 0!==(32&p)?(y=l.avail_in-t,y=y>r>>3?r>>3:y,t+=y,s-=y,r-=y<<3,k.bitb=q,k.bitk=r,l.avail_in=t,l.total_in+=s-l.next_in_index,l.next_in_index=s,k.write=u,d):(l.msg="invalid literal/length code",y=l.avail_in-t,y=y>r>>3?r>>3:y,t+=y,s-=y,r-=y<<3,k.bitb=q,k.bitk=r,l.avail_in=t,l.total_in+=s-l.next_in_index,l.next_in_index=s,k.write=u,g);if(m+=n[B+2],m+=q&j[p],B=3*(o+m),0===(p=n[B])){q>>=n[B+1],r-=n[B+1],k.window[u++]=n[B+2],v--;break}}else q>>=n[B+1],r-=n[B+1],k.window[u++]=n[B+2],v--}while(v>=258&&t>=10);return y=l.avail_in-t,y=y>r>>3?r>>3:y,t+=y,s-=y,r-=y<<3,k.bitb=q,k.bitk=r,l.avail_in=t,l.total_in+=s-l.next_in_index,l.next_in_index=s,k.write=u,c}var b,h,q,s,a=this,e=0,i=0,k=0,l=0,m=0,n=0,o=0,p=0,r=0,t=0;a.init=function(a,c,d,e,f,g){b=x,o=a,p=c,q=d,r=e,s=f,t=g,h=null},a.proc=function(a,v,w){var H,I,J,N,O,P,Q,K=0,L=0,M=0;for(M=v.next_in_index,N=v.avail_in,K=a.bitb,L=a.bitk,O=a.write,P=O<a.read?a.read-O-1:a.end-O;;)switch(b){case x:if(P>=258&&N>=10&&(a.bitb=K,a.bitk=L,v.avail_in=N,v.total_in+=M-v.next_in_index,v.next_in_index=M,a.write=O,w=u(o,p,q,r,s,t,a,v),M=v.next_in_index,N=v.avail_in,K=a.bitb,L=a.bitk,O=a.write,P=O<a.read?a.read-O-1:a.end-O,w!=c)){b=w==d?E:G;break}k=o,h=q,i=r,b=y;case y:for(H=k;H>L;){if(0===N)return a.bitb=K,a.bitk=L,v.avail_in=N,v.total_in+=M-v.next_in_index,v.next_in_index=M,a.write=O,a.inflate_flush(v,w);w=c,N--,K|=(255&v.read_byte(M++))<<L,L+=8}if(I=3*(i+(K&j[H])),K>>>=h[I+1],L-=h[I+1],J=h[I],0===J){l=h[I+2],b=D;break}if(0!==(16&J)){m=15&J,e=h[I+2],b=z;break}if(0===(64&J)){k=J,i=I/3+h[I+2];break}if(0!==(32&J)){b=E;break}return b=G,v.msg="invalid literal/length code",w=g,a.bitb=K,a.bitk=L,v.avail_in=N,v.total_in+=M-v.next_in_index,v.next_in_index=M,a.write=O,a.inflate_flush(v,w);case z:for(H=m;H>L;){if(0===N)return a.bitb=K,a.bitk=L,v.avail_in=N,v.total_in+=M-v.next_in_index,v.next_in_index=M,a.write=O,a.inflate_flush(v,w);w=c,N--,K|=(255&v.read_byte(M++))<<L,L+=8}e+=K&j[H],K>>=H,L-=H,k=p,h=s,i=t,b=A;case A:for(H=k;H>L;){if(0===N)return a.bitb=K,a.bitk=L,v.avail_in=N,v.total_in+=M-v.next_in_index,v.next_in_index=M,a.write=O,a.inflate_flush(v,w);w=c,N--,K|=(255&v.read_byte(M++))<<L,L+=8}if(I=3*(i+(K&j[H])),K>>=h[I+1],L-=h[I+1],J=h[I],0!==(16&J)){m=15&J,n=h[I+2],b=B;break}if(0===(64&J)){k=J,i=I/3+h[I+2];break}return b=G,v.msg="invalid distance code",w=g,a.bitb=K,a.bitk=L,v.avail_in=N,v.total_in+=M-v.next_in_index,v.next_in_index=M,a.write=O,a.inflate_flush(v,w);case B:for(H=m;H>L;){if(0===N)return a.bitb=K,a.bitk=L,v.avail_in=N,v.total_in+=M-v.next_in_index,v.next_in_index=M,a.write=O,a.inflate_flush(v,w);w=c,N--,K|=(255&v.read_byte(M++))<<L,L+=8}n+=K&j[H],K>>=H,L-=H,b=C;case C:for(Q=O-n;0>Q;)Q+=a.end;for(;0!==e;){if(0===P&&(O==a.end&&0!==a.read&&(O=0,P=O<a.read?a.read-O-1:a.end-O),0===P&&(a.write=O,w=a.inflate_flush(v,w),O=a.write,P=O<a.read?a.read-O-1:a.end-O,O==a.end&&0!==a.read&&(O=0,P=O<a.read?a.read-O-1:a.end-O),0===P)))return a.bitb=K,a.bitk=L,v.avail_in=N,v.total_in+=M-v.next_in_index,v.next_in_index=M,a.write=O,a.inflate_flush(v,w);a.window[O++]=a.window[Q++],P--,Q==a.end&&(Q=0),e--}b=x;break;case D:if(0===P&&(O==a.end&&0!==a.read&&(O=0,P=O<a.read?a.read-O-1:a.end-O),0===P&&(a.write=O,w=a.inflate_flush(v,w),O=a.write,P=O<a.read?a.read-O-1:a.end-O,O==a.end&&0!==a.read&&(O=0,P=O<a.read?a.read-O-1:a.end-O),0===P)))return a.bitb=K,a.bitk=L,v.avail_in=N,v.total_in+=M-v.next_in_index,v.next_in_index=M,a.write=O,a.inflate_flush(v,w);w=c,a.window[O++]=l,P--,b=x;break;case E:if(L>7&&(L-=8,N++,M--),a.write=O,w=a.inflate_flush(v,w),O=a.write,P=O<a.read?a.read-O-1:a.end-O,a.read!=a.write)return a.bitb=K,a.bitk=L,v.avail_in=N,v.total_in+=M-v.next_in_index,v.next_in_index=M,a.write=O,a.inflate_flush(v,w);b=F;case F:return w=d,a.bitb=K,a.bitk=L,v.avail_in=N,v.total_in+=M-v.next_in_index,v.next_in_index=M,a.write=O,a.inflate_flush(v,w);case G:return w=g,a.bitb=K,a.bitk=L,v.avail_in=N,v.total_in+=M-v.next_in_index,v.next_in_index=M,a.write=O,a.inflate_flush(v,w);default:return w=f,a.bitb=K,a.bitk=L,v.avail_in=N,v.total_in+=M-v.next_in_index,v.next_in_index=M,a.write=O,a.inflate_flush(v,w)}},a.free=function(){}}function T(a,b){var o,e=this,h=J,l=0,m=0,n=0,p=[0],q=[0],r=new H,s=0,t=new Int32Array(3*k),u=0,v=new w;e.bitk=0,e.bitb=0,e.window=new Uint8Array(b),e.end=b,e.read=0,e.write=0,e.reset=function(a,b){b&&(b[0]=u),h==P&&r.free(a),h=J,e.bitk=0,e.bitb=0,e.read=e.write=0},e.reset(a,null),e.inflate_flush=function(a,b){var f=a.next_out_index,g=e.read,d=(g<=e.write?e.write:e.end)-g;return d>a.avail_out&&(d=a.avail_out),0!==d&&b==i&&(b=c),a.avail_out-=d,a.total_out+=d,a.next_out.set(e.window.subarray(g,g+d),f),f+=d,g+=d,g==e.end&&(g=0,e.write==e.end&&(e.write=0),d=e.write-g,d>a.avail_out&&(d=a.avail_out),0!==d&&b==i&&(b=c),a.avail_out-=d,a.total_out+=d,a.next_out.set(e.window.subarray(g,g+d),f),f+=d,g+=d),a.next_out_index=f,e.read=g,b},e.proc=function(a,b){for(var i,B,C,D,E,F,G,H,T,U,V,W,x=a.next_in_index,y=a.avail_in,k=e.bitb,u=e.bitk,z=e.write,A=z<e.read?e.read-z-1:e.end-z;;)switch(h){case J:for(;3>u;){if(0===y)return e.bitb=k,e.bitk=u,a.avail_in=y,a.total_in+=x-a.next_in_index,a.next_in_index=x,e.write=z,e.inflate_flush(a,b);b=c,y--,k|=(255&a.read_byte(x++))<<u,u+=8}switch(i=7&k,s=1&i,i>>>1){case 0:k>>>=3,u-=3,i=7&u,k>>>=i,u-=i,h=K;break;case 1:C=[],D=[],E=[[]],F=[[]],w.inflate_trees_fixed(C,D,E,F),r.init(C[0],D[0],E[0],0,F[0],0),k>>>=3,u-=3,h=P;break;case 2:k>>>=3,u-=3,h=M;break;case 3:return k>>>=3,u-=3,h=S,a.msg="invalid block type",b=g,e.bitb=k,e.bitk=u,a.avail_in=y,a.total_in+=x-a.next_in_index,a.next_in_index=x,e.write=z,e.inflate_flush(a,b)}break;case K:for(;32>u;){if(0===y)return e.bitb=k,e.bitk=u,a.avail_in=y,a.total_in+=x-a.next_in_index,a.next_in_index=x,e.write=z,e.inflate_flush(a,b);b=c,y--,k|=(255&a.read_byte(x++))<<u,u+=8}if((65535&~k>>>16)!=(65535&k))return h=S,a.msg="invalid stored block lengths",b=g,e.bitb=k,e.bitk=u,a.avail_in=y,a.total_in+=x-a.next_in_index,a.next_in_index=x,e.write=z,e.inflate_flush(a,b);l=65535&k,k=u=0,h=0!==l?L:0!==s?Q:J;break;case L:if(0===y)return e.bitb=k,e.bitk=u,a.avail_in=y,a.total_in+=x-a.next_in_index,a.next_in_index=x,e.write=z,e.inflate_flush(a,b);if(0===A&&(z==e.end&&0!==e.read&&(z=0,A=z<e.read?e.read-z-1:e.end-z),0===A&&(e.write=z,b=e.inflate_flush(a,b),z=e.write,A=z<e.read?e.read-z-1:e.end-z,z==e.end&&0!==e.read&&(z=0,A=z<e.read?e.read-z-1:e.end-z),0===A)))return e.bitb=k,e.bitk=u,a.avail_in=y,a.total_in+=x-a.next_in_index,a.next_in_index=x,e.write=z,e.inflate_flush(a,b);if(b=c,i=l,i>y&&(i=y),i>A&&(i=A),e.window.set(a.read_buf(x,i),z),x+=i,y-=i,z+=i,A-=i,0!==(l-=i))break;h=0!==s?Q:J;break;case M:for(;14>u;){if(0===y)return e.bitb=k,e.bitk=u,a.avail_in=y,a.total_in+=x-a.next_in_index,a.next_in_index=x,e.write=z,e.inflate_flush(a,b);b=c,y--,k|=(255&a.read_byte(x++))<<u,u+=8}if(m=i=16383&k,(31&i)>29||(31&i>>5)>29)return h=S,a.msg="too many length or distance symbols",b=g,e.bitb=k,e.bitk=u,a.avail_in=y,a.total_in+=x-a.next_in_index,a.next_in_index=x,e.write=z,e.inflate_flush(a,b);if(i=258+(31&i)+(31&i>>5),!o||o.length<i)o=[];else for(B=0;i>B;B++)o[B]=0;k>>>=14,u-=14,n=0,h=N;case N:for(;4+(m>>>10)>n;){for(;3>u;){if(0===y)return e.bitb=k,e.bitk=u,a.avail_in=y,a.total_in+=x-a.next_in_index,a.next_in_index=x,e.write=z,e.inflate_flush(a,b);b=c,y--,k|=(255&a.read_byte(x++))<<u,u+=8}o[I[n++]]=7&k,k>>>=3,u-=3}for(;19>n;)o[I[n++]]=0;if(p[0]=7,i=v.inflate_trees_bits(o,p,q,t,a),i!=c)return b=i,b==g&&(o=null,h=S),e.bitb=k,e.bitk=u,a.avail_in=y,a.total_in+=x-a.next_in_index,a.next_in_index=x,e.write=z,e.inflate_flush(a,b);n=0,h=O;case O:for(;;){if(i=m,n>=258+(31&i)+(31&i>>5))break;for(i=p[0];i>u;){if(0===y)return e.bitb=k,e.bitk=u,a.avail_in=y,a.total_in+=x-a.next_in_index,a.next_in_index=x,e.write=z,e.inflate_flush(a,b);b=c,y--,k|=(255&a.read_byte(x++))<<u,u+=8}if(i=t[3*(q[0]+(k&j[i]))+1],H=t[3*(q[0]+(k&j[i]))+2],16>H)k>>>=i,u-=i,o[n++]=H;else{for(B=18==H?7:H-14,G=18==H?11:3;i+B>u;){if(0===y)return e.bitb=k,e.bitk=u,a.avail_in=y,a.total_in+=x-a.next_in_index,a.next_in_index=x,e.write=z,e.inflate_flush(a,b);b=c,y--,k|=(255&a.read_byte(x++))<<u,u+=8}if(k>>>=i,u-=i,G+=k&j[B],k>>>=B,u-=B,B=n,i=m,B+G>258+(31&i)+(31&i>>5)||16==H&&1>B)return o=null,h=S,a.msg="invalid bit length repeat",b=g,e.bitb=k,e.bitk=u,a.avail_in=y,a.total_in+=x-a.next_in_index,a.next_in_index=x,e.write=z,e.inflate_flush(a,b);H=16==H?o[B-1]:0;do o[B++]=H;while(0!==--G);n=B}}if(q[0]=-1,T=[],U=[],V=[],W=[],T[0]=9,U[0]=6,i=m,i=v.inflate_trees_dynamic(257+(31&i),1+(31&i>>5),o,T,U,V,W,t,a),i!=c)return i==g&&(o=null,h=S),b=i,e.bitb=k,e.bitk=u,a.avail_in=y,a.total_in+=x-a.next_in_index,a.next_in_index=x,e.write=z,e.inflate_flush(a,b);r.init(T[0],U[0],t,V[0],t,W[0]),h=P;case P:if(e.bitb=k,e.bitk=u,a.avail_in=y,a.total_in+=x-a.next_in_index,a.next_in_index=x,e.write=z,(b=r.proc(e,a,b))!=d)return e.inflate_flush(a,b);if(b=c,r.free(a),x=a.next_in_index,y=a.avail_in,k=e.bitb,u=e.bitk,z=e.write,A=z<e.read?e.read-z-1:e.end-z,0===s){h=J;break}h=Q;case Q:if(e.write=z,b=e.inflate_flush(a,b),z=e.write,A=z<e.read?e.read-z-1:e.end-z,e.read!=e.write)return e.bitb=k,e.bitk=u,a.avail_in=y,a.total_in+=x-a.next_in_index,a.next_in_index=x,e.write=z,e.inflate_flush(a,b);h=R;case R:return b=d,e.bitb=k,e.bitk=u,a.avail_in=y,a.total_in+=x-a.next_in_index,a.next_in_index=x,e.write=z,e.inflate_flush(a,b);case S:return b=g,e.bitb=k,e.bitk=u,a.avail_in=y,a.total_in+=x-a.next_in_index,a.next_in_index=x,e.write=z,e.inflate_flush(a,b);default:return b=f,e.bitb=k,e.bitk=u,a.avail_in=y,a.total_in+=x-a.next_in_index,a.next_in_index=x,e.write=z,e.inflate_flush(a,b)}},e.free=function(a){e.reset(a,null),e.window=null,t=null},e.set_dictionary=function(a,b,c){e.window.set(a.subarray(b,b+c),0),e.read=e.write=c},e.sync_point=function(){return h==K?1:0}}function fb(){function b(a){return a&&a.istate?(a.total_in=a.total_out=0,a.msg=null,a.istate.mode=bb,a.istate.blocks.reset(a,null),c):f}var a=this;a.mode=0,a.method=0,a.was=[0],a.need=0,a.marker=0,a.wbits=0,a.inflateEnd=function(b){return a.blocks&&a.blocks.free(b),a.blocks=null,c},a.inflateInit=function(d,e){return d.msg=null,a.blocks=null,8>e||e>15?(a.inflateEnd(d),f):(a.wbits=e,d.istate.blocks=new T(d,1<<e),b(d),c)},a.inflate=function(a,b){var h,j;if(!a||!a.istate||!a.next_in)return f;for(b=b==m?i:c,h=i;;)switch(a.istate.mode){case W:if(0===a.avail_in)return h;if(h=b,a.avail_in--,a.total_in++,(15&(a.istate.method=a.read_byte(a.next_in_index++)))!=V){a.istate.mode=db,a.msg="unknown compression method",a.istate.marker=5;break}if((a.istate.method>>4)+8>a.istate.wbits){a.istate.mode=db,a.msg="invalid window size",a.istate.marker=5;break}a.istate.mode=X;case X:if(0===a.avail_in)return h;if(h=b,a.avail_in--,a.total_in++,j=255&a.read_byte(a.next_in_index++),0!==((a.istate.method<<8)+j)%31){a.istate.mode=db,a.msg="incorrect header check",a.istate.marker=5;break}if(0===(j&U)){a.istate.mode=bb;break}a.istate.mode=Y;case Y:if(0===a.avail_in)return h;h=b,a.avail_in--,a.total_in++,a.istate.need=4278190080&(255&a.read_byte(a.next_in_index++))<<24,a.istate.mode=Z;case Z:if(0===a.avail_in)return h;h=b,a.avail_in--,a.total_in++,a.istate.need+=16711680&(255&a.read_byte(a.next_in_index++))<<16,a.istate.mode=$;case $:if(0===a.avail_in)return h;h=b,a.avail_in--,a.total_in++,a.istate.need+=65280&(255&a.read_byte(a.next_in_index++))<<8,a.istate.mode=_;case _:return 0===a.avail_in?h:(h=b,a.avail_in--,a.total_in++,a.istate.need+=255&a.read_byte(a.next_in_index++),a.istate.mode=ab,e);case ab:return a.istate.mode=db,a.msg="need dictionary",a.istate.marker=0,f;case bb:if(h=a.istate.blocks.proc(a,h),h==g){a.istate.mode=db,a.istate.marker=0;break}if(h==c&&(h=b),h!=d)return h;h=b,a.istate.blocks.reset(a,a.istate.was),a.istate.mode=cb;case cb:return d;case db:return g;default:return f}},a.inflateSetDictionary=function(a,b,d){var e=0,g=d;return a&&a.istate&&a.istate.mode==ab?(g>=1<<a.istate.wbits&&(g=(1<<a.istate.wbits)-1,e=d-g),a.istate.blocks.set_dictionary(b,e,g),a.istate.mode=bb,c):f},a.inflateSync=function(a){var d,e,h,j,k;if(!a||!a.istate)return f;if(a.istate.mode!=db&&(a.istate.mode=db,a.istate.marker=0),0===(d=a.avail_in))return i;for(e=a.next_in_index,h=a.istate.marker;0!==d&&4>h;)a.read_byte(e)==eb[h]?h++:h=0!==a.read_byte(e)?0:4-h,e++,d--;return a.total_in+=e-a.next_in_index,a.next_in_index=e,a.avail_in=d,a.istate.marker=h,4!=h?g:(j=a.total_in,k=a.total_out,b(a),a.total_in=j,a.total_out=k,a.istate.mode=bb,c)},a.inflateSyncPoint=function(a){return a&&a.istate&&a.istate.blocks?a.istate.blocks.sync_point():f}}function gb(){}function hb(){var a=this,b=new gb,e=512,f=l,g=new Uint8Array(e),h=!1;b.inflateInit(),b.next_out=g,a.append=function(a,j){var k,p,l=[],m=0,n=0,o=0;if(0!==a.length){b.next_in_index=0,b.next_in=a,b.avail_in=a.length;do{if(b.next_out_index=0,b.avail_out=e,0!==b.avail_in||h||(b.next_in_index=0,h=!0),k=b.inflate(f),h&&k===i){if(0!==b.avail_in)throw new Error("inflating: bad input")}else if(k!==c&&k!==d)throw new Error("inflating: "+b.msg);if((h||k===d)&&b.avail_in===a.length)throw new Error("inflating: bad input");b.next_out_index&&(b.next_out_index===e?l.push(new Uint8Array(g)):l.push(new Uint8Array(g.subarray(0,b.next_out_index)))),o+=b.next_out_index,j&&b.next_in_index>0&&b.next_in_index!=m&&(j(b.next_in_index),m=b.next_in_index)}while(b.avail_in>0||0===b.avail_out);return p=new Uint8Array(o),l.forEach(function(a){p.set(a,n),n+=a.length}),p}},a.flush=function(){b.inflateEnd()}}var x,y,z,A,B,C,D,E,F,G,I,J,K,L,M,N,O,P,Q,R,S,U,V,W,X,Y,Z,$,_,ab,bb,cb,db,eb,ib,b=15,c=0,d=1,e=2,f=-2,g=-3,h=-4,i=-5,j=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535],k=1440,l=0,m=4,n=9,o=5,p=[96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,192,80,7,10,0,8,96,0,8,32,0,9,160,0,8,0,0,8,128,0,8,64,0,9,224,80,7,6,0,8,88,0,8,24,0,9,144,83,7,59,0,8,120,0,8,56,0,9,208,81,7,17,0,8,104,0,8,40,0,9,176,0,8,8,0,8,136,0,8,72,0,9,240,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,200,81,7,13,0,8,100,0,8,36,0,9,168,0,8,4,0,8,132,0,8,68,0,9,232,80,7,8,0,8,92,0,8,28,0,9,152,84,7,83,0,8,124,0,8,60,0,9,216,82,7,23,0,8,108,0,8,44,0,9,184,0,8,12,0,8,140,0,8,76,0,9,248,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,196,81,7,11,0,8,98,0,8,34,0,9,164,0,8,2,0,8,130,0,8,66,0,9,228,80,7,7,0,8,90,0,8,26,0,9,148,84,7,67,0,8,122,0,8,58,0,9,212,82,7,19,0,8,106,0,8,42,0,9,180,0,8,10,0,8,138,0,8,74,0,9,244,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,204,81,7,15,0,8,102,0,8,38,0,9,172,0,8,6,0,8,134,0,8,70,0,9,236,80,7,9,0,8,94,0,8,30,0,9,156,84,7,99,0,8,126,0,8,62,0,9,220,82,7,27,0,8,110,0,8,46,0,9,188,0,8,14,0,8,142,0,8,78,0,9,252,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,194,80,7,10,0,8,97,0,8,33,0,9,162,0,8,1,0,8,129,0,8,65,0,9,226,80,7,6,0,8,89,0,8,25,0,9,146,83,7,59,0,8,121,0,8,57,0,9,210,81,7,17,0,8,105,0,8,41,0,9,178,0,8,9,0,8,137,0,8,73,0,9,242,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,202,81,7,13,0,8,101,0,8,37,0,9,170,0,8,5,0,8,133,0,8,69,0,9,234,80,7,8,0,8,93,0,8,29,0,9,154,84,7,83,0,8,125,0,8,61,0,9,218,82,7,23,0,8,109,0,8,45,0,9,186,0,8,13,0,8,141,0,8,77,0,9,250,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,198,81,7,11,0,8,99,0,8,35,0,9,166,0,8,3,0,8,131,0,8,67,0,9,230,80,7,7,0,8,91,0,8,27,0,9,150,84,7,67,0,8,123,0,8,59,0,9,214,82,7,19,0,8,107,0,8,43,0,9,182,0,8,11,0,8,139,0,8,75,0,9,246,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,206,81,7,15,0,8,103,0,8,39,0,9,174,0,8,7,0,8,135,0,8,71,0,9,238,80,7,9,0,8,95,0,8,31,0,9,158,84,7,99,0,8,127,0,8,63,0,9,222,82,7,27,0,8,111,0,8,47,0,9,190,0,8,15,0,8,143,0,8,79,0,9,254,96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,193,80,7,10,0,8,96,0,8,32,0,9,161,0,8,0,0,8,128,0,8,64,0,9,225,80,7,6,0,8,88,0,8,24,0,9,145,83,7,59,0,8,120,0,8,56,0,9,209,81,7,17,0,8,104,0,8,40,0,9,177,0,8,8,0,8,136,0,8,72,0,9,241,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,201,81,7,13,0,8,100,0,8,36,0,9,169,0,8,4,0,8,132,0,8,68,0,9,233,80,7,8,0,8,92,0,8,28,0,9,153,84,7,83,0,8,124,0,8,60,0,9,217,82,7,23,0,8,108,0,8,44,0,9,185,0,8,12,0,8,140,0,8,76,0,9,249,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,197,81,7,11,0,8,98,0,8,34,0,9,165,0,8,2,0,8,130,0,8,66,0,9,229,80,7,7,0,8,90,0,8,26,0,9,149,84,7,67,0,8,122,0,8,58,0,9,213,82,7,19,0,8,106,0,8,42,0,9,181,0,8,10,0,8,138,0,8,74,0,9,245,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,205,81,7,15,0,8,102,0,8,38,0,9,173,0,8,6,0,8,134,0,8,70,0,9,237,80,7,9,0,8,94,0,8,30,0,9,157,84,7,99,0,8,126,0,8,62,0,9,221,82,7,27,0,8,110,0,8,46,0,9,189,0,8,14,0,8,142,0,8,78,0,9,253,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,195,80,7,10,0,8,97,0,8,33,0,9,163,0,8,1,0,8,129,0,8,65,0,9,227,80,7,6,0,8,89,0,8,25,0,9,147,83,7,59,0,8,121,0,8,57,0,9,211,81,7,17,0,8,105,0,8,41,0,9,179,0,8,9,0,8,137,0,8,73,0,9,243,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,203,81,7,13,0,8,101,0,8,37,0,9,171,0,8,5,0,8,133,0,8,69,0,9,235,80,7,8,0,8,93,0,8,29,0,9,155,84,7,83,0,8,125,0,8,61,0,9,219,82,7,23,0,8,109,0,8,45,0,9,187,0,8,13,0,8,141,0,8,77,0,9,251,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,199,81,7,11,0,8,99,0,8,35,0,9,167,0,8,3,0,8,131,0,8,67,0,9,231,80,7,7,0,8,91,0,8,27,0,9,151,84,7,67,0,8,123,0,8,59,0,9,215,82,7,19,0,8,107,0,8,43,0,9,183,0,8,11,0,8,139,0,8,75,0,9,247,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,207,81,7,15,0,8,103,0,8,39,0,9,175,0,8,7,0,8,135,0,8,71,0,9,239,80,7,9,0,8,95,0,8,31,0,9,159,84,7,99,0,8,127,0,8,63,0,9,223,82,7,27,0,8,111,0,8,47,0,9,191,0,8,15,0,8,143,0,8,79,0,9,255],q=[80,5,1,87,5,257,83,5,17,91,5,4097,81,5,5,89,5,1025,85,5,65,93,5,16385,80,5,3,88,5,513,84,5,33,92,5,8193,82,5,9,90,5,2049,86,5,129,192,5,24577,80,5,2,87,5,385,83,5,25,91,5,6145,81,5,7,89,5,1537,85,5,97,93,5,24577,80,5,4,88,5,769,84,5,49,92,5,12289,82,5,13,90,5,3073,86,5,193,192,5,24577],r=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],s=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,112,112],t=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],u=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],v=15;w.inflate_trees_fixed=function(a,b,d,e){return a[0]=n,b[0]=o,d[0]=p,e[0]=q,c},x=0,y=1,z=2,A=3,B=4,C=5,D=6,E=7,F=8,G=9,I=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],J=0,K=1,L=2,M=3,N=4,O=5,P=6,Q=7,R=8,S=9,U=32,V=8,W=0,X=1,Y=2,Z=3,$=4,_=5,ab=6,bb=7,cb=12,db=13,eb=[0,0,255,255],gb.prototype={inflateInit:function(a){var c=this;return c.istate=new fb,a||(a=b),c.istate.inflateInit(c,a)},inflate:function(a){var b=this;return b.istate?b.istate.inflate(b,a):f},inflateEnd:function(){var b,a=this;return a.istate?(b=a.istate.inflateEnd(a),a.istate=null,b):f},inflateSync:function(){var a=this;return a.istate?a.istate.inflateSync(a):f},inflateSetDictionary:function(a,b){var c=this;return c.istate?c.istate.inflateSetDictionary(c,a,b):f},read_byte:function(a){var b=this;return b.next_in.subarray(a,a+1)[0]},read_buf:function(a,b){var c=this;return c.next_in.subarray(a,a+b)}},ib=a.zip||a,ib.Inflater=ib._jzlib_Inflater=hb}!function(a){"use strict";function d(){inflate(a),postMessage({type:"importScripts"})}function e(b){var d=a[b.codecClass],e=b.sn;if(c[e])throw Error("duplicated sn");c[e]={codec:new d(b.options),crcInput:"input"===b.crcType,crcOutput:"output"===b.crcType,crc:new j},postMessage({type:"newTask",sn:e})}function g(a){var i,j,k,m,n,o,p,b=a.sn,d=a.type,g=a.data,h=c[b];if(!h&&a.codecClass&&(e(a),h=c[b]),i="append"===d,j=f(),i)try{k=h.codec.append(g,function(a){postMessage({type:"progress",sn:b,loaded:a})})}catch(l){throw delete c[b],l}else delete c[b],k=h.codec.flush();m=f()-j,j=f(),g&&h.crcInput&&h.crc.append(g),k&&h.crcOutput&&h.crc.append(k),n=f()-j,o={type:d,sn:b,codecTime:m,crcTime:n},p=[],k&&(o.data=k,p.push(k.buffer)),i||!h.crcInput&&!h.crcOutput||(o.crc=h.crc.get());try{postMessage(o,p)}catch(q){postMessage(o)}}function h(a,b,c){var d={type:a,sn:b,error:i(c)};postMessage(d)}function i(a){return{message:a.message,stack:a.stack}}function j(){this.crc=-1}function k(){}var b,c,f;if(a.zWorkerInitialized)throw new Error("z-worker.js should be run only once");a.zWorkerInitialized=!0,addEventListener("message",function(a){var c=a.data,d=c.type,e=c.sn,f=b[d];if(f)try{f(c)}catch(g){h(d,e,g)}postMessage({type:"echo",originalType:d,sn:e})}),b={importScripts:d,newTask:e,append:g,flush:g},c={},f=a.performance?a.performance.now.bind(a.performance):Date.now,j.prototype.append=function(a){var d,e,b=0|this.crc,c=this.table;for(d=0,e=0|a.length;e>d;d++)b=b>>>8^c[255&(b^a[d])];this.crc=b},j.prototype.get=function(){return~this.crc},j.prototype.table=function(){var a,b,c,d=[];for(a=0;256>a;a++){for(c=a,b=0;8>b;b++)1&c?c=3988292384^c>>>1:c>>>=1;d[a]=c}return d}(),a.NOOP=k,k.prototype.append=function(a){return a},k.prototype.flush=function(){}}(this);'},{}],72:[function(e,t,r){var n,i=e("./z-worker"),o=URL.createObjectURL(new Blob([i],{type:"text/javascript"})),a="File format is not recognized.",s="CRC failed.",u="File contains encrypted entry.",l="File is using Zip64 (4gb+ file size).",f="Error while reading zip file.",c="Error while writing zip file.",h="Error while writing file data.",d="Error while reading file data.",p="File already exists.",g=524288,y="text/plain";try{n=0===new Blob([new DataView(new ArrayBuffer(0))]).size}catch(e){n=void 0}var b={};function m(){this.crc=-1}function v(){}function w(e,t){var r,n;return r=new ArrayBuffer(e),n=new Uint8Array(r),t&&n.set(t,0),{buffer:r,array:n,view:new DataView(r)}}function _(){}function E(e){var t,r=this;r.size=0,r.init=function(n,i){var o=new Blob([e],{type:y});(t=new T(o)).init(function(){r.size=t.size,n()},i)},r.readUint8Array=function(e,r,n,i){t.readUint8Array(e,r,n,i)}}function x(e){var t,r=this;r.size=0,r.init=function(n){for(var i=e.length;"="==e.charAt(i-1);)i--;t=e.indexOf(",")+1,r.size=Math.floor(.75*(i-t)),n()},r.readUint8Array=function(r,n,i){var o,a=w(n),s=4*Math.floor(r/3),u=4*Math.ceil((r+n)/3),l=atob(e.substring(s+t,u+t)),f=r-3*Math.floor(s/4);for(o=f;o<f+n;o++)a.array[o-f]=l.charCodeAt(o);i(a.array)}}function T(e){var t=this;t.size=0,t.init=function(r){t.size=e.size,r()},t.readUint8Array=function(t,r,n,i){var o=new FileReader;o.onload=function(e){n(new Uint8Array(e.target.result))},o.onerror=i;try{o.readAsArrayBuffer(function(e,t,r){if(t<0||r<0||t+r>e.size)throw new RangeError("offset:"+t+", length:"+r+", size:"+e.size);return e.slice?e.slice(t,t+r):e.webkitSlice?e.webkitSlice(t,t+r):e.mozSlice?e.mozSlice(t,t+r):e.msSlice?e.msSlice(t,t+r):void 0}(e,t,r))}catch(e){i(e)}}}function k(){}function N(e){var t;this.init=function(e){t=new Blob([],{type:y}),e()},this.writeUint8Array=function(e,r){t=new Blob([t,n?e:e.buffer],{type:y}),r()},this.getData=function(r,n){var i=new FileReader;i.onload=function(e){r(e.target.result)},i.onerror=n,i.readAsText(t,e)}}function S(e){var t="",r="";this.init=function(r){t+="data:"+(e||"")+";base64,",r()},this.writeUint8Array=function(e,n){var i,o=r.length,a=r;for(r="",i=0;i<3*Math.floor((o+e.length)/3)-o;i++)a+=String.fromCharCode(e[i]);for(;i<e.length;i++)r+=String.fromCharCode(e[i]);a.length>2?t+=btoa(a):r=a,n()},this.getData=function(e){e(t+btoa(r))}}function I(e){var t;this.init=function(r){t=new Blob([],{type:e}),r()},this.writeUint8Array=function(r,i){t=new Blob([t,n?r:r.buffer],{type:e}),i()},this.getData=function(e){e(t)}}function A(e,t,r,n,i,o,a,s,u,l){var f,c,h,d=0,p=t.sn;function y(){e.removeEventListener("message",b,!1),s(c,h)}function b(t){var r=t.data,i=r.data,s=r.error;if(s)return s.toString=function(){return"Error: "+this.message},void u(s);if(r.sn===p)switch("number"==typeof r.codecTime&&(e.codecTime+=r.codecTime),"number"==typeof r.crcTime&&(e.crcTime+=r.crcTime),r.type){case"append":i?(c+=i.length,n.writeUint8Array(i,function(){m()},l)):m();break;case"flush":h=r.crc,i?(c+=i.length,n.writeUint8Array(i,function(){y()},l)):y();break;case"progress":a&&a(f+r.loaded,o);break;case"importScripts":case"newTask":case"echo":break;default:console.warn("zip.js:launchWorkerProcess: unknown message: ",r)}}function m(){(f=d*g)<=o?r.readUint8Array(i+f,Math.min(g,o-f),function(r){a&&a(f,o);var n=0===f?t:{sn:p};n.type="append",n.data=r;try{e.postMessage(n,[r.buffer])}catch(t){e.postMessage(n)}d++},u):e.postMessage({sn:p,type:"flush"})}c=0,e.addEventListener("message",b,!1),m()}function D(e,t,r,n,i,o,a,s,u,l){var f,c=0,h=0,d="input"===o,p="output"===o,y=new m;!function o(){var b;if((f=c*g)<i)t.readUint8Array(n+f,Math.min(g,i-f),function(t){var n;try{n=e.append(t,function(e){a&&a(f+e,i)})}catch(e){return void u(e)}n?(h+=n.length,r.writeUint8Array(n,function(){c++,setTimeout(o,1)},l),p&&y.append(n)):(c++,setTimeout(o,1)),d&&y.append(t),a&&a(f,i)},u);else{try{b=e.flush()}catch(e){return void u(e)}b?(p&&y.append(b),h+=b.length,r.writeUint8Array(b,function(){s(h,y.get())},l)):s(h,y.get())}}()}function L(e,t,r,n,i,o,a,s,u,l,f){b.useWebWorkers&&a?A(e,{sn:t,codecClass:"NOOP",crcType:"input"},r,n,i,o,u,s,l,f):D(new v,r,n,i,o,"input",u,s,l,f)}function O(e){var t,r,n="",i=["Ç","ü","é","â","ä","à","å","ç","ê","ë","è","ï","î","ì","Ä","Å","É","æ","Æ","ô","ö","ò","û","ù","ÿ","Ö","Ü","ø","£","Ø","×","ƒ","á","í","ó","ú","ñ","Ñ","ª","º","¿","®","¬","½","¼","¡","«","»","_","_","_","¦","¦","Á","Â","À","©","¦","¦","+","+","¢","¥","+","+","-","-","+","-","+","ã","Ã","+","+","-","-","¦","-","+","¤","ð","Ð","Ê","Ë","È","i","Í","Î","Ï","+","+","_","_","¦","Ì","_","Ó","ß","Ô","Ò","õ","Õ","µ","þ","Þ","Ú","Û","Ù","ý","Ý","¯","´","­","±","_","¾","¶","§","÷","¸","°","¨","·","¹","³","²","_"," "];for(t=0;t<e.length;t++)n+=(r=255&e.charCodeAt(t))>127?i[r-128]:String.fromCharCode(r);return n}function M(e){return decodeURIComponent(escape(e))}function U(e){var t,r="";for(t=0;t<e.length;t++)r+=String.fromCharCode(e[t]);return r}function C(e,t,r,n,i){e.version=t.view.getUint16(r,!0),e.bitFlag=t.view.getUint16(r+2,!0),e.compressionMethod=t.view.getUint16(r+4,!0),e.lastModDateRaw=t.view.getUint32(r+6,!0),e.lastModDate=function(e){var t=(4294901760&e)>>16,r=65535&e;try{return new Date(1980+((65024&t)>>9),((480&t)>>5)-1,31&t,(63488&r)>>11,(2016&r)>>5,2*(31&r),0)}catch(e){}}(e.lastModDateRaw),1!=(1&e.bitFlag)?((n||8!=(8&e.bitFlag))&&(e.crc32=t.view.getUint32(r+10,!0),e.compressedSize=t.view.getUint32(r+14,!0),e.uncompressedSize=t.view.getUint32(r+18,!0)),4294967295!==e.compressedSize&&4294967295!==e.uncompressedSize?(e.filenameLength=t.view.getUint16(r+22,!0),e.extraFieldLength=t.view.getUint16(r+24,!0)):i(l)):i(u)}function B(e,t,r){var n=0;function i(){}i.prototype.getData=function(t,i,o,u){var l=this;function f(e,n){u&&!function(e){var t=w(4);return t.view.setUint32(0,e),l.crc32==t.view.getUint32(0)}(n)?r(s):t.getData(function(e){i(e)})}function c(e){r(e||d)}function p(e){r(e||h)}e.readUint8Array(l.offset,30,function(i){var s,h=w(i.length,i);1347093252==h.view.getUint32(0)?(C(l,h,4,!1,r),s=l.offset+30+l.filenameLength+l.extraFieldLength,t.init(function(){0===l.compressionMethod?L(l._worker,n++,e,t,s,l.compressedSize,u,f,o,c,p):function(e,t,r,n,i,o,a,s,u,l,f){var c=a?"output":"none";b.useWebWorkers?A(e,{sn:t,codecClass:"Inflater",crcType:c},r,n,i,o,u,s,l,f):D(new b.Inflater,r,n,i,o,c,u,s,l,f)}(l._worker,n++,e,t,s,l.compressedSize,u,f,o,c,p)},p)):r(a)},c)};var o={getEntries:function(t){var n=this._worker;!function(t){var n=22;if(e.size<n)r(a);else{var i=n+65536;o(n,function(){o(Math.min(i,e.size),function(){r(a)})})}function o(i,o){e.readUint8Array(e.size-i,i,function(e){for(var r=e.length-n;r>=0;r--)if(80===e[r]&&75===e[r+1]&&5===e[r+2]&&6===e[r+3])return void t(new DataView(e.buffer,r,n));o()},function(){r(f)})}}(function(o){var s,u;s=o.getUint32(16,!0),u=o.getUint16(8,!0),s<0||s>=e.size?r(a):e.readUint8Array(s,e.size-s,function(e){var o,s,l,f,c=0,h=[],d=w(e.length,e);for(o=0;o<u;o++){if((s=new i)._worker=n,1347092738!=d.view.getUint32(c))return void r(a);C(s,d,c+6,!0,r),s.commentLength=d.view.getUint16(c+32,!0),s.directory=16==(16&d.view.getUint8(c+38)),s.offset=d.view.getUint32(c+42,!0),l=U(d.array.subarray(c+46,c+46+s.filenameLength)),s.filename=2048==(2048&s.bitFlag)?M(l):O(l),s.directory||"/"!=s.filename.charAt(s.filename.length-1)||(s.directory=!0),f=U(d.array.subarray(c+46+s.filenameLength+s.extraFieldLength,c+46+s.filenameLength+s.extraFieldLength+s.commentLength)),s.comment=2048==(2048&s.bitFlag)?M(f):O(f),h.push(s),c+=46+s.filenameLength+s.extraFieldLength+s.commentLength}t(h)},function(){r(f)})})},close:function(e){this._worker&&(this._worker.terminate(),this._worker=null),e&&e()},_worker:null};b.useWebWorkers?F("inflater",function(e){o._worker=e,t(o)},function(e){r(e)}):t(o)}function R(e){return unescape(encodeURIComponent(e))}function P(e){var t,r=[];for(t=0;t<e.length;t++)r.push(e.charCodeAt(t));return r}function j(e,t,r,n){var i={},o=[],a=0,s=0;function u(e){r(e||c)}function l(e){r(e||d)}var f={add:function(t,f,c,h,d){var g,y,m,v=this._worker;function _(t,r){var n=w(16);a+=t||0,n.view.setUint32(0,1347094280),void 0!==r&&(g.view.setUint32(10,r,!0),n.view.setUint32(4,r,!0)),f&&(n.view.setUint32(8,t,!0),g.view.setUint32(14,t,!0),n.view.setUint32(12,f.size,!0),g.view.setUint32(18,f.size,!0)),e.writeUint8Array(n.array,function(){a+=16,c()},u)}function E(){var c,E;(d=d||{},t=t.trim(),d.directory&&"/"!=t.charAt(t.length-1)&&(t+="/"),i.hasOwnProperty(t))?r(p):(y=P(R(t)),o.push(t),c=function(){f?n||0===d.level?L(v,s++,f,e,0,f.size,!0,_,h,l,u):function(e,t,r,n,i,o,a,s,u){b.useWebWorkers?A(e,{sn:t,options:{level:i},codecClass:"Deflater",crcType:"input"},r,n,0,r.size,a,o,s,u):D(new b.Deflater,r,n,0,r.size,"input",a,o,s,u)}(v,s++,f,e,d.level,_,h,l,u):_()},m=d.lastModDate||new Date,g=w(26),i[t]={headerArray:g.array,directory:d.directory,filename:y,offset:a,comment:P(R(d.comment||""))},g.view.setUint32(0,335546376),d.version&&g.view.setUint8(0,d.version),n||0===d.level||d.directory||g.view.setUint16(4,2048),g.view.setUint16(6,(m.getHours()<<6|m.getMinutes())<<5|m.getSeconds()/2,!0),g.view.setUint16(8,(m.getFullYear()-1980<<4|m.getMonth()+1)<<5|m.getDate(),!0),g.view.setUint16(22,y.length,!0),(E=w(30+y.length)).view.setUint32(0,1347093252),E.array.set(g.array,4),E.array.set(y,30),a+=E.array.length,e.writeUint8Array(E.array,c,u))}f?f.init(E,l):E()},close:function(t){this._worker&&(this._worker.terminate(),this._worker=null);var r,n,s,l=0,f=0;for(n=0;n<o.length;n++)l+=46+(s=i[o[n]]).filename.length+s.comment.length;for(r=w(l+22),n=0;n<o.length;n++)s=i[o[n]],r.view.setUint32(f,1347092738),r.view.setUint16(f+4,5120),r.array.set(s.headerArray,f+6),r.view.setUint16(f+32,s.comment.length,!0),s.directory&&r.view.setUint8(f+38,16),r.view.setUint32(f+42,s.offset,!0),r.array.set(s.filename,f+46),r.array.set(s.comment,f+46+s.filename.length),f+=46+s.filename.length+s.comment.length;r.view.setUint32(f,1347093766),r.view.setUint16(f+8,o.length,!0),r.view.setUint16(f+10,o.length,!0),r.view.setUint32(f+12,l,!0),r.view.setUint32(f+16,a,!0),e.writeUint8Array(r.array,function(){e.getData(t)},u)},_worker:null};b.useWebWorkers?F("deflater",function(e){f._worker=e,t(f)},function(e){r(e)}):t(f)}function F(e,t,r){if(null===b.workerScripts||null===b.workerScriptsPath){var n=new Worker(o);n.codecTime=n.crcTime=0,n.postMessage({type:"importScripts",scripts:["inflate.js"]}),n.addEventListener("message",function e(o){var a=o.data;if(a.error)return n.terminate(),void r(a.error);"importScripts"===a.type&&(n.removeEventListener("message",e),n.removeEventListener("error",i),t(n))}),n.addEventListener("error",i)}else r(new Error("Either zip.workerScripts or zip.workerScriptsPath may be set, not both."));function i(e){n.terminate(),r(e)}}function z(e){console.error(e)}m.prototype.append=function(e){for(var t=0|this.crc,r=this.table,n=0,i=0|e.length;n<i;n++)t=t>>>8^r[255&(t^e[n])];this.crc=t},m.prototype.get=function(){return~this.crc},m.prototype.table=function(){var e,t,r,n=[];for(e=0;e<256;e++){for(r=e,t=0;t<8;t++)1&r?r=r>>>1^3988292384:r>>>=1;n[e]=r}return n}(),v.prototype.append=function(e,t){return e},v.prototype.flush=function(){},E.prototype=new _,E.prototype.constructor=E,x.prototype=new _,x.prototype.constructor=x,T.prototype=new _,T.prototype.constructor=T,k.prototype.getData=function(e){e(this.data)},N.prototype=new k,N.prototype.constructor=N,S.prototype=new k,S.prototype.constructor=S,I.prototype=new k,I.prototype.constructor=I;var q={Reader:_,Writer:k,BlobReader:T,Data64URIReader:x,TextReader:E,BlobWriter:I,Data64URIWriter:S,TextWriter:N,createReader:function(e,t,r){r=r||z,e.init(function(){B(e,t,r)},r)},createWriter:function(e,t,r,n){r=r||z,n=!!n,e.init(function(){j(e,t,r,n)},r)},useWebWorkers:!0,workerScriptsPath:null,workerScripts:null};for(var X in q)b[X]=q[X];t.exports=b},{"./z-worker":71}],73:[function(e,t,r){function n(e){return e?Array.prototype.slice.call(e):[]}function i(e,t){return!!t&&(t=t.toLowerCase(),Object.prototype.toString.call(e).toLowerCase()==="[object "+t+"]")}function o(e){return"function"==typeof e}function a(e){return"string"==typeof e}function s(e){return i(e,"regexp")}function u(e,t){return 0===e.indexOf(t)}t.exports={toArray:n,extend:function(e){var t=n(arguments);if(1==t.length)return e;t.shift();for(var r=0,i=t.length;r<i;r++)for(var o in t[r])t[r].hasOwnProperty(o)&&(e[o]=t[r][o]);return e},startWith:u,isResouces:function(e){return u(e,"resourceId:")},transKeyToMatchResourceMap:function(e){return"@"+e.replace("resourceId:0x","").toUpperCase()},castLogger:function(e,t){console.log(e+" cost: "+(Date.now()-t)+"ms")},isTypeOf:i,isArray:function(e){return i(e,"array")},isFunction:o,isString:a,isDefined:function(e){return!(void 0===e)},isObject:function(e){return"object"==typeof e},isReg:s,isThisWhatYouNeed:function(e,t){return o(e)?e(t):a(e)?t.toLowerCase().indexOf(e.toLowerCase())>-1:!!s(e)&&e.test(t.toLowerCase())}}},{}],74:[function(e,t,r){var n=e("./lib/browser/zip"),i=e("./lib/browser/blob-to-buffer"),o=e("./lib/utils");function a(e){if(!(e instanceof Blob))throw new Error("Invalid input, expect the first param to be a File/Blob.");if(!(this instanceof a))return new a(e);this.file=e}a.prototype.destroy=function(){this.file=null},a.prototype.getBuffer=function(e,t,r){if(!o.isArray(e)||!o.isFunction(r))return r(new Error("getBuffer: invalid param, expect first param to be an Array and the second param to be a callback function"));o.isFunction(t)&&(r=t,t={}),e=e.map(function(e){return"string"==typeof e&&(e=e.split("\0").join("")),e});var n=t&&t.multiple||!1;this.getEntries(function(s,u){if(s)return r(s);var l={};u.forEach(function(t){return e.some(function(e){if(o.isThisWhatYouNeed(e,t.filename)){if(n){var r={fileName:e,buffer:t};l[e]?l[e].push(r):l[e]=[r]}else l[e]=t;return!0}})}),function(e,t,r){var n={},o=[],s=0;for(var u in e)o.push({name:u,entry:e[u]});o.length||r(null,{},o.length);function l(e,t){s++,n[e]=t}o.forEach(function(e){var u,f;u=e.name,f=e.entry,a.getEntryData(f,function(e,a){if(e)return r(e);"blob"===t.type?(l(u,a),s>=o.length&&r(null,n,o.length)):i(a,function(e,t){if(e)return console.error(e),r(e);l(u,t),s>=o.length&&r(null,n,o.length)})})})}(l,t,function(e,t){r(e,t,u.length)})})},a.prototype.getEntries=function(e){n.createReader(new n.BlobReader(this.file),function(t){t.getEntries(function(t){e(null,t,t.length)})},e)},a.getEntryData=function(e,t){var r=new n.BlobWriter;e.getData(r,function(r){t(null,r,e.length)})},t.exports=a},{"./lib/browser/blob-to-buffer":70,"./lib/browser/zip":72,"./lib/utils":73}],75:[function(e,t,r){var n,i;n=this,i=function(){"use strict";function e(e,t,r){this.low=0|e,this.high=0|t,this.unsigned=!!r}function t(e){return!0===(e&&e.__isLong__)}e.prototype.__isLong__,Object.defineProperty(e.prototype,"__isLong__",{value:!0,enumerable:!1,configurable:!1}),e.isLong=t;var r={},n={};function i(e,t){var i,o,s;return t?(s=0<=(e>>>=0)&&e<256)&&(o=n[e])?o:(i=a(e,(0|e)<0?-1:0,!0),s&&(n[e]=i),i):(s=-128<=(e|=0)&&e<128)&&(o=r[e])?o:(i=a(e,e<0?-1:0,!1),s&&(r[e]=i),i)}function o(e,t){if(isNaN(e)||!isFinite(e))return t?g:p;if(t){if(e<0)return g;if(e>=c)return w}else{if(e<=-h)return _;if(e+1>=h)return v}return e<0?o(-e,t).neg():a(e%f|0,e/f|0,t)}function a(t,r,n){return new e(t,r,n)}e.fromInt=i,e.fromNumber=o,e.fromBits=a;var s=Math.pow;function u(e,t,r){if(0===e.length)throw Error("empty string");if("NaN"===e||"Infinity"===e||"+Infinity"===e||"-Infinity"===e)return p;if("number"==typeof t?(r=t,t=!1):t=!!t,(r=r||10)<2||36<r)throw RangeError("radix");var n;if((n=e.indexOf("-"))>0)throw Error("interior hyphen");if(0===n)return u(e.substring(1),t,r).neg();for(var i=o(s(r,8)),a=p,l=0;l<e.length;l+=8){var f=Math.min(8,e.length-l),c=parseInt(e.substring(l,l+f),r);if(f<8){var h=o(s(r,f));a=a.mul(h).add(o(c))}else a=(a=a.mul(i)).add(o(c))}return a.unsigned=t,a}function l(t){return t instanceof e?t:"number"==typeof t?o(t):"string"==typeof t?u(t):a(t.low,t.high,t.unsigned)}e.fromString=u,e.fromValue=l;var f=4294967296,c=f*f,h=c/2,d=i(1<<24),p=i(0);e.ZERO=p;var g=i(0,!0);e.UZERO=g;var y=i(1);e.ONE=y;var b=i(1,!0);e.UONE=b;var m=i(-1);e.NEG_ONE=m;var v=a(-1,2147483647,!1);e.MAX_VALUE=v;var w=a(-1,-1,!0);e.MAX_UNSIGNED_VALUE=w;var _=a(0,-2147483648,!1);e.MIN_VALUE=_;var E=e.prototype;return E.toInt=function(){return this.unsigned?this.low>>>0:this.low},E.toNumber=function(){return this.unsigned?(this.high>>>0)*f+(this.low>>>0):this.high*f+(this.low>>>0)},E.toString=function(e){if((e=e||10)<2||36<e)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative()){if(this.eq(_)){var t=o(e),r=this.div(t),n=r.mul(t).sub(this);return r.toString(e)+n.toInt().toString(e)}return"-"+this.neg().toString(e)}for(var i=o(s(e,6),this.unsigned),a=this,u="";;){var l=a.div(i),f=(a.sub(l.mul(i)).toInt()>>>0).toString(e);if((a=l).isZero())return f+u;for(;f.length<6;)f="0"+f;u=""+f+u}},E.getHighBits=function(){return this.high},E.getHighBitsUnsigned=function(){return this.high>>>0},E.getLowBits=function(){return this.low},E.getLowBitsUnsigned=function(){return this.low>>>0},E.getNumBitsAbs=function(){if(this.isNegative())return this.eq(_)?64:this.neg().getNumBitsAbs();for(var e=0!=this.high?this.high:this.low,t=31;t>0&&0==(e&1<<t);t--);return 0!=this.high?t+33:t+1},E.isZero=function(){return 0===this.high&&0===this.low},E.isNegative=function(){return!this.unsigned&&this.high<0},E.isPositive=function(){return this.unsigned||this.high>=0},E.isOdd=function(){return 1==(1&this.low)},E.isEven=function(){return 0==(1&this.low)},E.equals=function(e){return t(e)||(e=l(e)),(this.unsigned===e.unsigned||this.high>>>31!=1||e.high>>>31!=1)&&(this.high===e.high&&this.low===e.low)},E.eq=E.equals,E.notEquals=function(e){return!this.eq(e)},E.neq=E.notEquals,E.lessThan=function(e){return this.comp(e)<0},E.lt=E.lessThan,E.lessThanOrEqual=function(e){return this.comp(e)<=0},E.lte=E.lessThanOrEqual,E.greaterThan=function(e){return this.comp(e)>0},E.gt=E.greaterThan,E.greaterThanOrEqual=function(e){return this.comp(e)>=0},E.gte=E.greaterThanOrEqual,E.compare=function(e){if(t(e)||(e=l(e)),this.eq(e))return 0;var r=this.isNegative(),n=e.isNegative();return r&&!n?-1:!r&&n?1:this.unsigned?e.high>>>0>this.high>>>0||e.high===this.high&&e.low>>>0>this.low>>>0?-1:1:this.sub(e).isNegative()?-1:1},E.comp=E.compare,E.negate=function(){return!this.unsigned&&this.eq(_)?_:this.not().add(y)},E.neg=E.negate,E.add=function(e){t(e)||(e=l(e));var r=this.high>>>16,n=65535&this.high,i=this.low>>>16,o=65535&this.low,s=e.high>>>16,u=65535&e.high,f=e.low>>>16,c=0,h=0,d=0,p=0;return d+=(p+=o+(65535&e.low))>>>16,h+=(d+=i+f)>>>16,c+=(h+=n+u)>>>16,c+=r+s,a((d&=65535)<<16|(p&=65535),(c&=65535)<<16|(h&=65535),this.unsigned)},E.subtract=function(e){return t(e)||(e=l(e)),this.add(e.neg())},E.sub=E.subtract,E.multiply=function(e){if(this.isZero())return p;if(t(e)||(e=l(e)),e.isZero())return p;if(this.eq(_))return e.isOdd()?_:p;if(e.eq(_))return this.isOdd()?_:p;if(this.isNegative())return e.isNegative()?this.neg().mul(e.neg()):this.neg().mul(e).neg();if(e.isNegative())return this.mul(e.neg()).neg();if(this.lt(d)&&e.lt(d))return o(this.toNumber()*e.toNumber(),this.unsigned);var r=this.high>>>16,n=65535&this.high,i=this.low>>>16,s=65535&this.low,u=e.high>>>16,f=65535&e.high,c=e.low>>>16,h=65535&e.low,g=0,y=0,b=0,m=0;return b+=(m+=s*h)>>>16,y+=(b+=i*h)>>>16,b&=65535,y+=(b+=s*c)>>>16,g+=(y+=n*h)>>>16,y&=65535,g+=(y+=i*c)>>>16,y&=65535,g+=(y+=s*f)>>>16,g+=r*h+n*c+i*f+s*u,a((b&=65535)<<16|(m&=65535),(g&=65535)<<16|(y&=65535),this.unsigned)},E.mul=E.multiply,E.divide=function(e){if(t(e)||(e=l(e)),e.isZero())throw Error("division by zero");if(this.isZero())return this.unsigned?g:p;var r,n,i;if(this.unsigned){if(e.unsigned||(e=e.toUnsigned()),e.gt(this))return g;if(e.gt(this.shru(1)))return b;i=g}else{if(this.eq(_))return e.eq(y)||e.eq(m)?_:e.eq(_)?y:(r=this.shr(1).div(e).shl(1)).eq(p)?e.isNegative()?y:m:(n=this.sub(e.mul(r)),i=r.add(n.div(e)));else if(e.eq(_))return this.unsigned?g:p;if(this.isNegative())return e.isNegative()?this.neg().div(e.neg()):this.neg().div(e).neg();if(e.isNegative())return this.div(e.neg()).neg();i=p}for(n=this;n.gte(e);){r=Math.max(1,Math.floor(n.toNumber()/e.toNumber()));for(var a=Math.ceil(Math.log(r)/Math.LN2),u=a<=48?1:s(2,a-48),f=o(r),c=f.mul(e);c.isNegative()||c.gt(n);)c=(f=o(r-=u,this.unsigned)).mul(e);f.isZero()&&(f=y),i=i.add(f),n=n.sub(c)}return i},E.div=E.divide,E.modulo=function(e){return t(e)||(e=l(e)),this.sub(this.div(e).mul(e))},E.mod=E.modulo,E.not=function(){return a(~this.low,~this.high,this.unsigned)},E.and=function(e){return t(e)||(e=l(e)),a(this.low&e.low,this.high&e.high,this.unsigned)},E.or=function(e){return t(e)||(e=l(e)),a(this.low|e.low,this.high|e.high,this.unsigned)},E.xor=function(e){return t(e)||(e=l(e)),a(this.low^e.low,this.high^e.high,this.unsigned)},E.shiftLeft=function(e){return t(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?a(this.low<<e,this.high<<e|this.low>>>32-e,this.unsigned):a(0,this.low<<e-32,this.unsigned)},E.shl=E.shiftLeft,E.shiftRight=function(e){return t(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?a(this.low>>>e|this.high<<32-e,this.high>>e,this.unsigned):a(this.high>>e-32,this.high>=0?0:-1,this.unsigned)},E.shr=E.shiftRight,E.shiftRightUnsigned=function(e){if(t(e)&&(e=e.toInt()),0===(e&=63))return this;var r=this.high;return e<32?a(this.low>>>e|r<<32-e,r>>>e,this.unsigned):a(32===e?r:r>>>e-32,0,this.unsigned)},E.shru=E.shiftRightUnsigned,E.toSigned=function(){return this.unsigned?a(this.low,this.high,!1):this},E.toUnsigned=function(){return this.unsigned?this:a(this.low,this.high,!0)},E.toBytes=function(e){return e?this.toBytesLE():this.toBytesBE()},E.toBytesLE=function(){var e=this.high,t=this.low;return[255&t,t>>>8&255,t>>>16&255,t>>>24&255,255&e,e>>>8&255,e>>>16&255,e>>>24&255]},E.toBytesBE=function(){var e=this.high,t=this.low;return[e>>>24&255,e>>>16&255,e>>>8&255,255&e,t>>>24&255,t>>>16&255,t>>>8&255,255&t]},e},"function"==typeof e&&"object"==typeof t&&t&&t.exports?t.exports=i():(n.dcodeIO=n.dcodeIO||{}).Long=i()},{}],76:[function(e,t,r){"use strict";var n;if(!Object.keys){var i=Object.prototype.hasOwnProperty,o=Object.prototype.toString,a=e("./isArguments"),s=Object.prototype.propertyIsEnumerable,u=!s.call({toString:null},"toString"),l=s.call(function(){},"prototype"),f=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],c=function(e){var t=e.constructor;return t&&t.prototype===e},h={$applicationCache:!0,$console:!0,$external:!0,$frame:!0,$frameElement:!0,$frames:!0,$innerHeight:!0,$innerWidth:!0,$onmozfullscreenchange:!0,$onmozfullscreenerror:!0,$outerHeight:!0,$outerWidth:!0,$pageXOffset:!0,$pageYOffset:!0,$parent:!0,$scrollLeft:!0,$scrollTop:!0,$scrollX:!0,$scrollY:!0,$self:!0,$webkitIndexedDB:!0,$webkitStorageInfo:!0,$window:!0},d=function(){if("undefined"==typeof window)return!1;for(var e in window)try{if(!h["$"+e]&&i.call(window,e)&&null!==window[e]&&"object"==typeof window[e])try{c(window[e])}catch(e){return!0}}catch(e){return!0}return!1}();n=function(e){var t=null!==e&&"object"==typeof e,r="[object Function]"===o.call(e),n=a(e),s=t&&"[object String]"===o.call(e),h=[];if(!t&&!r&&!n)throw new TypeError("Object.keys called on a non-object");var p=l&&r;if(s&&e.length>0&&!i.call(e,0))for(var g=0;g<e.length;++g)h.push(String(g));if(n&&e.length>0)for(var y=0;y<e.length;++y)h.push(String(y));else for(var b in e)p&&"prototype"===b||!i.call(e,b)||h.push(String(b));if(u)for(var m=function(e){if("undefined"==typeof window||!d)return c(e);try{return c(e)}catch(e){return!1}}(e),v=0;v<f.length;++v)m&&"constructor"===f[v]||!i.call(e,f[v])||h.push(f[v]);return h}}t.exports=n},{"./isArguments":78}],77:[function(e,t,r){"use strict";var n=Array.prototype.slice,i=e("./isArguments"),o=Object.keys,a=o?function(e){return o(e)}:e("./implementation"),s=Object.keys;a.shim=function(){Object.keys?function(){var e=Object.keys(arguments);return e&&e.length===arguments.length}(1,2)||(Object.keys=function(e){return i(e)?s(n.call(e)):s(e)}):Object.keys=a;return Object.keys||a},t.exports=a},{"./implementation":76,"./isArguments":78}],78:[function(e,t,r){"use strict";var n=Object.prototype.toString;t.exports=function(e){var t=n.call(e),r="[object Arguments]"===t;return r||(r="[object Array]"!==t&&null!==e&&"object"==typeof e&&"number"==typeof e.length&&e.length>=0&&"[object Function]"===n.call(e.callee)),r}},{}],79:[function(e,t,r){"use strict";var n=e("object-keys"),i=e("has-symbols/shams")(),o=e("call-bind/callBound"),a=Object,s=o("Array.prototype.push"),u=o("Object.prototype.propertyIsEnumerable"),l=i?Object.getOwnPropertySymbols:null;t.exports=function(e,t){if(null==e)throw new TypeError("target must be an object");var r=a(e);if(1===arguments.length)return r;for(var o=1;o<arguments.length;++o){var f=a(arguments[o]),c=n(f),h=i&&(Object.getOwnPropertySymbols||l);if(h)for(var d=h(f),p=0;p<d.length;++p){var g=d[p];u(f,g)&&s(c,g)}for(var y=0;y<c.length;++y){var b=c[y];if(u(f,b)){var m=f[b];r[b]=m}}}return r}},{"call-bind/callBound":26,"has-symbols/shams":64,"object-keys":77}],80:[function(e,t,r){"use strict";var n=e("./implementation");t.exports=function(){return Object.assign?function(){if(!Object.assign)return!1;for(var e="abcdefghijklmnopqrst",t=e.split(""),r={},n=0;n<t.length;++n)r[t[n]]=t[n];var i=Object.assign({},r),o="";for(var a in i)o+=a;return e!==o}()?n:function(){if(!Object.assign||!Object.preventExtensions)return!1;var e=Object.preventExtensions({1:2});try{Object.assign(e,"xy")}catch(t){return"y"===e[1]}return!1}()?n:Object.assign:n}},{"./implementation":79}],81:[function(e,t,r){"use strict";var n="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;function i(e,t){return Object.prototype.hasOwnProperty.call(e,t)}r.assign=function(e){for(var t=Array.prototype.slice.call(arguments,1);t.length;){var r=t.shift();if(r){if("object"!=typeof r)throw new TypeError(r+"must be non-object");for(var n in r)i(r,n)&&(e[n]=r[n])}}return e},r.shrinkBuf=function(e,t){return e.length===t?e:e.subarray?e.subarray(0,t):(e.length=t,e)};var o={arraySet:function(e,t,r,n,i){if(t.subarray&&e.subarray)e.set(t.subarray(r,r+n),i);else for(var o=0;o<n;o++)e[i+o]=t[r+o]},flattenChunks:function(e){var t,r,n,i,o,a;for(n=0,t=0,r=e.length;t<r;t++)n+=e[t].length;for(a=new Uint8Array(n),i=0,t=0,r=e.length;t<r;t++)o=e[t],a.set(o,i),i+=o.length;return a}},a={arraySet:function(e,t,r,n,i){for(var o=0;o<n;o++)e[i+o]=t[r+o]},flattenChunks:function(e){return[].concat.apply([],e)}};r.setTyped=function(e){e?(r.Buf8=Uint8Array,r.Buf16=Uint16Array,r.Buf32=Int32Array,r.assign(r,o)):(r.Buf8=Array,r.Buf16=Array,r.Buf32=Array,r.assign(r,a))},r.setTyped(n)},{}],82:[function(e,t,r){"use strict";t.exports=function(e,t,r,n){for(var i=65535&e|0,o=e>>>16&65535|0,a=0;0!==r;){r-=a=r>2e3?2e3:r;do{o=o+(i=i+t[n++]|0)|0}while(--a);i%=65521,o%=65521}return i|o<<16|0}},{}],83:[function(e,t,r){"use strict";t.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],84:[function(e,t,r){"use strict";var n=function(){for(var e,t=[],r=0;r<256;r++){e=r;for(var n=0;n<8;n++)e=1&e?3988292384^e>>>1:e>>>1;t[r]=e}return t}();t.exports=function(e,t,r,i){var o=n,a=i+r;e^=-1;for(var s=i;s<a;s++)e=e>>>8^o[255&(e^t[s])];return-1^e}},{}],85:[function(e,t,r){"use strict";var n,i=e("../utils/common"),o=e("./trees"),a=e("./adler32"),s=e("./crc32"),u=e("./messages"),l=0,f=1,c=3,h=4,d=5,p=0,g=1,y=-2,b=-3,m=-5,v=-1,w=1,_=2,E=3,x=4,T=0,k=2,N=8,S=9,I=15,A=8,D=286,L=30,O=19,M=2*D+1,U=15,C=3,B=258,R=B+C+1,P=32,j=42,F=69,z=73,q=91,X=103,Z=113,V=666,H=1,W=2,G=3,K=4,$=3;function Y(e,t){return e.msg=u[t],t}function J(e){return(e<<1)-(e>4?9:0)}function Q(e){for(var t=e.length;--t>=0;)e[t]=0}function ee(e){var t=e.state,r=t.pending;r>e.avail_out&&(r=e.avail_out),0!==r&&(i.arraySet(e.output,t.pending_buf,t.pending_out,r,e.next_out),e.next_out+=r,t.pending_out+=r,e.total_out+=r,e.avail_out-=r,t.pending-=r,0===t.pending&&(t.pending_out=0))}function te(e,t){o._tr_flush_block(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,ee(e.strm)}function re(e,t){e.pending_buf[e.pending++]=t}function ne(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t}function ie(e,t){var r,n,i=e.max_chain_length,o=e.strstart,a=e.prev_length,s=e.nice_match,u=e.strstart>e.w_size-R?e.strstart-(e.w_size-R):0,l=e.window,f=e.w_mask,c=e.prev,h=e.strstart+B,d=l[o+a-1],p=l[o+a];e.prev_length>=e.good_match&&(i>>=2),s>e.lookahead&&(s=e.lookahead);do{if(l[(r=t)+a]===p&&l[r+a-1]===d&&l[r]===l[o]&&l[++r]===l[o+1]){o+=2,r++;do{}while(l[++o]===l[++r]&&l[++o]===l[++r]&&l[++o]===l[++r]&&l[++o]===l[++r]&&l[++o]===l[++r]&&l[++o]===l[++r]&&l[++o]===l[++r]&&l[++o]===l[++r]&&o<h);if(n=B-(h-o),o=h-B,n>a){if(e.match_start=t,a=n,n>=s)break;d=l[o+a-1],p=l[o+a]}}}while((t=c[t&f])>u&&0!=--i);return a<=e.lookahead?a:e.lookahead}function oe(e){var t,r,n,o,u,l,f,c,h,d,p=e.w_size;do{if(o=e.window_size-e.lookahead-e.strstart,e.strstart>=p+(p-R)){i.arraySet(e.window,e.window,p,p,0),e.match_start-=p,e.strstart-=p,e.block_start-=p,t=r=e.hash_size;do{n=e.head[--t],e.head[t]=n>=p?n-p:0}while(--r);t=r=p;do{n=e.prev[--t],e.prev[t]=n>=p?n-p:0}while(--r);o+=p}if(0===e.strm.avail_in)break;if(l=e.strm,f=e.window,c=e.strstart+e.lookahead,h=o,d=void 0,(d=l.avail_in)>h&&(d=h),r=0===d?0:(l.avail_in-=d,i.arraySet(f,l.input,l.next_in,d,c),1===l.state.wrap?l.adler=a(l.adler,f,d,c):2===l.state.wrap&&(l.adler=s(l.adler,f,d,c)),l.next_in+=d,l.total_in+=d,d),e.lookahead+=r,e.lookahead+e.insert>=C)for(u=e.strstart-e.insert,e.ins_h=e.window[u],e.ins_h=(e.ins_h<<e.hash_shift^e.window[u+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[u+C-1])&e.hash_mask,e.prev[u&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=u,u++,e.insert--,!(e.lookahead+e.insert<C)););}while(e.lookahead<R&&0!==e.strm.avail_in)}function ae(e,t){for(var r,n;;){if(e.lookahead<R){if(oe(e),e.lookahead<R&&t===l)return H;if(0===e.lookahead)break}if(r=0,e.lookahead>=C&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+C-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==r&&e.strstart-r<=e.w_size-R&&(e.match_length=ie(e,r)),e.match_length>=C)if(n=o._tr_tally(e,e.strstart-e.match_start,e.match_length-C),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=C){e.match_length--;do{e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+C-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart}while(0!=--e.match_length);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else n=o._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(n&&(te(e,!1),0===e.strm.avail_out))return H}return e.insert=e.strstart<C-1?e.strstart:C-1,t===h?(te(e,!0),0===e.strm.avail_out?G:K):e.last_lit&&(te(e,!1),0===e.strm.avail_out)?H:W}function se(e,t){for(var r,n,i;;){if(e.lookahead<R){if(oe(e),e.lookahead<R&&t===l)return H;if(0===e.lookahead)break}if(r=0,e.lookahead>=C&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+C-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=C-1,0!==r&&e.prev_length<e.max_lazy_match&&e.strstart-r<=e.w_size-R&&(e.match_length=ie(e,r),e.match_length<=5&&(e.strategy===w||e.match_length===C&&e.strstart-e.match_start>4096)&&(e.match_length=C-1)),e.prev_length>=C&&e.match_length<=e.prev_length){i=e.strstart+e.lookahead-C,n=o._tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-C),e.lookahead-=e.prev_length-1,e.prev_length-=2;do{++e.strstart<=i&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+C-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart)}while(0!=--e.prev_length);if(e.match_available=0,e.match_length=C-1,e.strstart++,n&&(te(e,!1),0===e.strm.avail_out))return H}else if(e.match_available){if((n=o._tr_tally(e,0,e.window[e.strstart-1]))&&te(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return H}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(n=o._tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<C-1?e.strstart:C-1,t===h?(te(e,!0),0===e.strm.avail_out?G:K):e.last_lit&&(te(e,!1),0===e.strm.avail_out)?H:W}function ue(e,t,r,n,i){this.good_length=e,this.max_lazy=t,this.nice_length=r,this.max_chain=n,this.func=i}function le(e){var t;return e&&e.state?(e.total_in=e.total_out=0,e.data_type=k,(t=e.state).pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?j:Z,e.adler=2===t.wrap?0:1,t.last_flush=l,o._tr_init(t),p):Y(e,y)}function fe(e){var t,r=le(e);return r===p&&((t=e.state).window_size=2*t.w_size,Q(t.head),t.max_lazy_match=n[t.level].max_lazy,t.good_match=n[t.level].good_length,t.nice_match=n[t.level].nice_length,t.max_chain_length=n[t.level].max_chain,t.strstart=0,t.block_start=0,t.lookahead=0,t.insert=0,t.match_length=t.prev_length=C-1,t.match_available=0,t.ins_h=0),r}function ce(e,t,r,n,o,a){if(!e)return y;var s=1;if(t===v&&(t=6),n<0?(s=0,n=-n):n>15&&(s=2,n-=16),o<1||o>S||r!==N||n<8||n>15||t<0||t>9||a<0||a>x)return Y(e,y);8===n&&(n=9);var u=new function(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=N,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new i.Buf16(2*M),this.dyn_dtree=new i.Buf16(2*(2*L+1)),this.bl_tree=new i.Buf16(2*(2*O+1)),Q(this.dyn_ltree),Q(this.dyn_dtree),Q(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new i.Buf16(U+1),this.heap=new i.Buf16(2*D+1),Q(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new i.Buf16(2*D+1),Q(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0};return e.state=u,u.strm=e,u.wrap=s,u.gzhead=null,u.w_bits=n,u.w_size=1<<u.w_bits,u.w_mask=u.w_size-1,u.hash_bits=o+7,u.hash_size=1<<u.hash_bits,u.hash_mask=u.hash_size-1,u.hash_shift=~~((u.hash_bits+C-1)/C),u.window=new i.Buf8(2*u.w_size),u.head=new i.Buf16(u.hash_size),u.prev=new i.Buf16(u.w_size),u.lit_bufsize=1<<o+6,u.pending_buf_size=4*u.lit_bufsize,u.pending_buf=new i.Buf8(u.pending_buf_size),u.d_buf=1*u.lit_bufsize,u.l_buf=3*u.lit_bufsize,u.level=t,u.strategy=a,u.method=r,fe(e)}n=[new ue(0,0,0,0,function(e,t){var r=65535;for(r>e.pending_buf_size-5&&(r=e.pending_buf_size-5);;){if(e.lookahead<=1){if(oe(e),0===e.lookahead&&t===l)return H;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;var n=e.block_start+r;if((0===e.strstart||e.strstart>=n)&&(e.lookahead=e.strstart-n,e.strstart=n,te(e,!1),0===e.strm.avail_out))return H;if(e.strstart-e.block_start>=e.w_size-R&&(te(e,!1),0===e.strm.avail_out))return H}return e.insert=0,t===h?(te(e,!0),0===e.strm.avail_out?G:K):(e.strstart>e.block_start&&(te(e,!1),e.strm.avail_out),H)}),new ue(4,4,8,4,ae),new ue(4,5,16,8,ae),new ue(4,6,32,32,ae),new ue(4,4,16,16,se),new ue(8,16,32,32,se),new ue(8,16,128,128,se),new ue(8,32,128,256,se),new ue(32,128,258,1024,se),new ue(32,258,258,4096,se)],r.deflateInit=function(e,t){return ce(e,t,N,I,A,T)},r.deflateInit2=ce,r.deflateReset=fe,r.deflateResetKeep=le,r.deflateSetHeader=function(e,t){return e&&e.state?2!==e.state.wrap?y:(e.state.gzhead=t,p):y},r.deflate=function(e,t){var r,i,a,u;if(!e||!e.state||t>d||t<0)return e?Y(e,y):y;if(i=e.state,!e.output||!e.input&&0!==e.avail_in||i.status===V&&t!==h)return Y(e,0===e.avail_out?m:y);if(i.strm=e,r=i.last_flush,i.last_flush=t,i.status===j)if(2===i.wrap)e.adler=0,re(i,31),re(i,139),re(i,8),i.gzhead?(re(i,(i.gzhead.text?1:0)+(i.gzhead.hcrc?2:0)+(i.gzhead.extra?4:0)+(i.gzhead.name?8:0)+(i.gzhead.comment?16:0)),re(i,255&i.gzhead.time),re(i,i.gzhead.time>>8&255),re(i,i.gzhead.time>>16&255),re(i,i.gzhead.time>>24&255),re(i,9===i.level?2:i.strategy>=_||i.level<2?4:0),re(i,255&i.gzhead.os),i.gzhead.extra&&i.gzhead.extra.length&&(re(i,255&i.gzhead.extra.length),re(i,i.gzhead.extra.length>>8&255)),i.gzhead.hcrc&&(e.adler=s(e.adler,i.pending_buf,i.pending,0)),i.gzindex=0,i.status=F):(re(i,0),re(i,0),re(i,0),re(i,0),re(i,0),re(i,9===i.level?2:i.strategy>=_||i.level<2?4:0),re(i,$),i.status=Z);else{var b=N+(i.w_bits-8<<4)<<8;b|=(i.strategy>=_||i.level<2?0:i.level<6?1:6===i.level?2:3)<<6,0!==i.strstart&&(b|=P),b+=31-b%31,i.status=Z,ne(i,b),0!==i.strstart&&(ne(i,e.adler>>>16),ne(i,65535&e.adler)),e.adler=1}if(i.status===F)if(i.gzhead.extra){for(a=i.pending;i.gzindex<(65535&i.gzhead.extra.length)&&(i.pending!==i.pending_buf_size||(i.gzhead.hcrc&&i.pending>a&&(e.adler=s(e.adler,i.pending_buf,i.pending-a,a)),ee(e),a=i.pending,i.pending!==i.pending_buf_size));)re(i,255&i.gzhead.extra[i.gzindex]),i.gzindex++;i.gzhead.hcrc&&i.pending>a&&(e.adler=s(e.adler,i.pending_buf,i.pending-a,a)),i.gzindex===i.gzhead.extra.length&&(i.gzindex=0,i.status=z)}else i.status=z;if(i.status===z)if(i.gzhead.name){a=i.pending;do{if(i.pending===i.pending_buf_size&&(i.gzhead.hcrc&&i.pending>a&&(e.adler=s(e.adler,i.pending_buf,i.pending-a,a)),ee(e),a=i.pending,i.pending===i.pending_buf_size)){u=1;break}u=i.gzindex<i.gzhead.name.length?255&i.gzhead.name.charCodeAt(i.gzindex++):0,re(i,u)}while(0!==u);i.gzhead.hcrc&&i.pending>a&&(e.adler=s(e.adler,i.pending_buf,i.pending-a,a)),0===u&&(i.gzindex=0,i.status=q)}else i.status=q;if(i.status===q)if(i.gzhead.comment){a=i.pending;do{if(i.pending===i.pending_buf_size&&(i.gzhead.hcrc&&i.pending>a&&(e.adler=s(e.adler,i.pending_buf,i.pending-a,a)),ee(e),a=i.pending,i.pending===i.pending_buf_size)){u=1;break}u=i.gzindex<i.gzhead.comment.length?255&i.gzhead.comment.charCodeAt(i.gzindex++):0,re(i,u)}while(0!==u);i.gzhead.hcrc&&i.pending>a&&(e.adler=s(e.adler,i.pending_buf,i.pending-a,a)),0===u&&(i.status=X)}else i.status=X;if(i.status===X&&(i.gzhead.hcrc?(i.pending+2>i.pending_buf_size&&ee(e),i.pending+2<=i.pending_buf_size&&(re(i,255&e.adler),re(i,e.adler>>8&255),e.adler=0,i.status=Z)):i.status=Z),0!==i.pending){if(ee(e),0===e.avail_out)return i.last_flush=-1,p}else if(0===e.avail_in&&J(t)<=J(r)&&t!==h)return Y(e,m);if(i.status===V&&0!==e.avail_in)return Y(e,m);if(0!==e.avail_in||0!==i.lookahead||t!==l&&i.status!==V){var v=i.strategy===_?function(e,t){for(var r;;){if(0===e.lookahead&&(oe(e),0===e.lookahead)){if(t===l)return H;break}if(e.match_length=0,r=o._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,r&&(te(e,!1),0===e.strm.avail_out))return H}return e.insert=0,t===h?(te(e,!0),0===e.strm.avail_out?G:K):e.last_lit&&(te(e,!1),0===e.strm.avail_out)?H:W}(i,t):i.strategy===E?function(e,t){for(var r,n,i,a,s=e.window;;){if(e.lookahead<=B){if(oe(e),e.lookahead<=B&&t===l)return H;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=C&&e.strstart>0&&(n=s[i=e.strstart-1])===s[++i]&&n===s[++i]&&n===s[++i]){a=e.strstart+B;do{}while(n===s[++i]&&n===s[++i]&&n===s[++i]&&n===s[++i]&&n===s[++i]&&n===s[++i]&&n===s[++i]&&n===s[++i]&&i<a);e.match_length=B-(a-i),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=C?(r=o._tr_tally(e,1,e.match_length-C),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(r=o._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),r&&(te(e,!1),0===e.strm.avail_out))return H}return e.insert=0,t===h?(te(e,!0),0===e.strm.avail_out?G:K):e.last_lit&&(te(e,!1),0===e.strm.avail_out)?H:W}(i,t):n[i.level].func(i,t);if(v!==G&&v!==K||(i.status=V),v===H||v===G)return 0===e.avail_out&&(i.last_flush=-1),p;if(v===W&&(t===f?o._tr_align(i):t!==d&&(o._tr_stored_block(i,0,0,!1),t===c&&(Q(i.head),0===i.lookahead&&(i.strstart=0,i.block_start=0,i.insert=0))),ee(e),0===e.avail_out))return i.last_flush=-1,p}return t!==h?p:i.wrap<=0?g:(2===i.wrap?(re(i,255&e.adler),re(i,e.adler>>8&255),re(i,e.adler>>16&255),re(i,e.adler>>24&255),re(i,255&e.total_in),re(i,e.total_in>>8&255),re(i,e.total_in>>16&255),re(i,e.total_in>>24&255)):(ne(i,e.adler>>>16),ne(i,65535&e.adler)),ee(e),i.wrap>0&&(i.wrap=-i.wrap),0!==i.pending?p:g)},r.deflateEnd=function(e){var t;return e&&e.state?(t=e.state.status)!==j&&t!==F&&t!==z&&t!==q&&t!==X&&t!==Z&&t!==V?Y(e,y):(e.state=null,t===Z?Y(e,b):p):y},r.deflateSetDictionary=function(e,t){var r,n,o,s,u,l,f,c,h=t.length;if(!e||!e.state)return y;if(2===(s=(r=e.state).wrap)||1===s&&r.status!==j||r.lookahead)return y;for(1===s&&(e.adler=a(e.adler,t,h,0)),r.wrap=0,h>=r.w_size&&(0===s&&(Q(r.head),r.strstart=0,r.block_start=0,r.insert=0),c=new i.Buf8(r.w_size),i.arraySet(c,t,h-r.w_size,r.w_size,0),t=c,h=r.w_size),u=e.avail_in,l=e.next_in,f=e.input,e.avail_in=h,e.next_in=0,e.input=t,oe(r);r.lookahead>=C;){n=r.strstart,o=r.lookahead-(C-1);do{r.ins_h=(r.ins_h<<r.hash_shift^r.window[n+C-1])&r.hash_mask,r.prev[n&r.w_mask]=r.head[r.ins_h],r.head[r.ins_h]=n,n++}while(--o);r.strstart=n,r.lookahead=C-1,oe(r)}return r.strstart+=r.lookahead,r.block_start=r.strstart,r.insert=r.lookahead,r.lookahead=0,r.match_length=r.prev_length=C-1,r.match_available=0,e.next_in=l,e.input=f,e.avail_in=u,r.wrap=s,p},r.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":81,"./adler32":82,"./crc32":84,"./messages":89,"./trees":90}],86:[function(e,t,r){"use strict";t.exports=function(e,t){var r,n,i,o,a,s,u,l,f,c,h,d,p,g,y,b,m,v,w,_,E,x,T,k,N;r=e.state,n=e.next_in,k=e.input,i=n+(e.avail_in-5),o=e.next_out,N=e.output,a=o-(t-e.avail_out),s=o+(e.avail_out-257),u=r.dmax,l=r.wsize,f=r.whave,c=r.wnext,h=r.window,d=r.hold,p=r.bits,g=r.lencode,y=r.distcode,b=(1<<r.lenbits)-1,m=(1<<r.distbits)-1;e:do{p<15&&(d+=k[n++]<<p,p+=8,d+=k[n++]<<p,p+=8),v=g[d&b];t:for(;;){if(d>>>=w=v>>>24,p-=w,0===(w=v>>>16&255))N[o++]=65535&v;else{if(!(16&w)){if(0==(64&w)){v=g[(65535&v)+(d&(1<<w)-1)];continue t}if(32&w){r.mode=12;break e}e.msg="invalid literal/length code",r.mode=30;break e}_=65535&v,(w&=15)&&(p<w&&(d+=k[n++]<<p,p+=8),_+=d&(1<<w)-1,d>>>=w,p-=w),p<15&&(d+=k[n++]<<p,p+=8,d+=k[n++]<<p,p+=8),v=y[d&m];r:for(;;){if(d>>>=w=v>>>24,p-=w,!(16&(w=v>>>16&255))){if(0==(64&w)){v=y[(65535&v)+(d&(1<<w)-1)];continue r}e.msg="invalid distance code",r.mode=30;break e}if(E=65535&v,p<(w&=15)&&(d+=k[n++]<<p,(p+=8)<w&&(d+=k[n++]<<p,p+=8)),(E+=d&(1<<w)-1)>u){e.msg="invalid distance too far back",r.mode=30;break e}if(d>>>=w,p-=w,E>(w=o-a)){if((w=E-w)>f&&r.sane){e.msg="invalid distance too far back",r.mode=30;break e}if(x=0,T=h,0===c){if(x+=l-w,w<_){_-=w;do{N[o++]=h[x++]}while(--w);x=o-E,T=N}}else if(c<w){if(x+=l+c-w,(w-=c)<_){_-=w;do{N[o++]=h[x++]}while(--w);if(x=0,c<_){_-=w=c;do{N[o++]=h[x++]}while(--w);x=o-E,T=N}}}else if(x+=c-w,w<_){_-=w;do{N[o++]=h[x++]}while(--w);x=o-E,T=N}for(;_>2;)N[o++]=T[x++],N[o++]=T[x++],N[o++]=T[x++],_-=3;_&&(N[o++]=T[x++],_>1&&(N[o++]=T[x++]))}else{x=o-E;do{N[o++]=N[x++],N[o++]=N[x++],N[o++]=N[x++],_-=3}while(_>2);_&&(N[o++]=N[x++],_>1&&(N[o++]=N[x++]))}break}}break}}while(n<i&&o<s);n-=_=p>>3,d&=(1<<(p-=_<<3))-1,e.next_in=n,e.next_out=o,e.avail_in=n<i?i-n+5:5-(n-i),e.avail_out=o<s?s-o+257:257-(o-s),r.hold=d,r.bits=p}},{}],87:[function(e,t,r){"use strict";var n=e("../utils/common"),i=e("./adler32"),o=e("./crc32"),a=e("./inffast"),s=e("./inftrees"),u=0,l=1,f=2,c=4,h=5,d=6,p=0,g=1,y=2,b=-2,m=-3,v=-4,w=-5,_=8,E=1,x=2,T=3,k=4,N=5,S=6,I=7,A=8,D=9,L=10,O=11,M=12,U=13,C=14,B=15,R=16,P=17,j=18,F=19,z=20,q=21,X=22,Z=23,V=24,H=25,W=26,G=27,K=28,$=29,Y=30,J=31,Q=32,ee=852,te=592,re=15;function ne(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)}function ie(e){var t;return e&&e.state?(t=e.state,e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=E,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new n.Buf32(ee),t.distcode=t.distdyn=new n.Buf32(te),t.sane=1,t.back=-1,p):b}function oe(e){var t;return e&&e.state?((t=e.state).wsize=0,t.whave=0,t.wnext=0,ie(e)):b}function ae(e,t){var r,n;return e&&e.state?(n=e.state,t<0?(r=0,t=-t):(r=1+(t>>4),t<48&&(t&=15)),t&&(t<8||t>15)?b:(null!==n.window&&n.wbits!==t&&(n.window=null),n.wrap=r,n.wbits=t,oe(e))):b}function se(e,t){var r,i;return e?(i=new function(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new n.Buf16(320),this.work=new n.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0},e.state=i,i.window=null,(r=ae(e,t))!==p&&(e.state=null),r):b}var ue,le,fe=!0;function ce(e){if(fe){var t;for(ue=new n.Buf32(512),le=new n.Buf32(32),t=0;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(s(l,e.lens,0,288,ue,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;s(f,e.lens,0,32,le,0,e.work,{bits:5}),fe=!1}e.lencode=ue,e.lenbits=9,e.distcode=le,e.distbits=5}function he(e,t,r,i){var o,a=e.state;return null===a.window&&(a.wsize=1<<a.wbits,a.wnext=0,a.whave=0,a.window=new n.Buf8(a.wsize)),i>=a.wsize?(n.arraySet(a.window,t,r-a.wsize,a.wsize,0),a.wnext=0,a.whave=a.wsize):((o=a.wsize-a.wnext)>i&&(o=i),n.arraySet(a.window,t,r-i,o,a.wnext),(i-=o)?(n.arraySet(a.window,t,r-i,i,0),a.wnext=i,a.whave=a.wsize):(a.wnext+=o,a.wnext===a.wsize&&(a.wnext=0),a.whave<a.wsize&&(a.whave+=o))),0}r.inflateReset=oe,r.inflateReset2=ae,r.inflateResetKeep=ie,r.inflateInit=function(e){return se(e,re)},r.inflateInit2=se,r.inflate=function(e,t){var r,ee,te,re,ie,oe,ae,se,ue,le,fe,de,pe,ge,ye,be,me,ve,we,_e,Ee,xe,Te,ke,Ne=0,Se=new n.Buf8(4),Ie=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return b;(r=e.state).mode===M&&(r.mode=U),ie=e.next_out,te=e.output,ae=e.avail_out,re=e.next_in,ee=e.input,oe=e.avail_in,se=r.hold,ue=r.bits,le=oe,fe=ae,xe=p;e:for(;;)switch(r.mode){case E:if(0===r.wrap){r.mode=U;break}for(;ue<16;){if(0===oe)break e;oe--,se+=ee[re++]<<ue,ue+=8}if(2&r.wrap&&35615===se){r.check=0,Se[0]=255&se,Se[1]=se>>>8&255,r.check=o(r.check,Se,2,0),se=0,ue=0,r.mode=x;break}if(r.flags=0,r.head&&(r.head.done=!1),!(1&r.wrap)||(((255&se)<<8)+(se>>8))%31){e.msg="incorrect header check",r.mode=Y;break}if((15&se)!==_){e.msg="unknown compression method",r.mode=Y;break}if(ue-=4,Ee=8+(15&(se>>>=4)),0===r.wbits)r.wbits=Ee;else if(Ee>r.wbits){e.msg="invalid window size",r.mode=Y;break}r.dmax=1<<Ee,e.adler=r.check=1,r.mode=512&se?L:M,se=0,ue=0;break;case x:for(;ue<16;){if(0===oe)break e;oe--,se+=ee[re++]<<ue,ue+=8}if(r.flags=se,(255&r.flags)!==_){e.msg="unknown compression method",r.mode=Y;break}if(57344&r.flags){e.msg="unknown header flags set",r.mode=Y;break}r.head&&(r.head.text=se>>8&1),512&r.flags&&(Se[0]=255&se,Se[1]=se>>>8&255,r.check=o(r.check,Se,2,0)),se=0,ue=0,r.mode=T;case T:for(;ue<32;){if(0===oe)break e;oe--,se+=ee[re++]<<ue,ue+=8}r.head&&(r.head.time=se),512&r.flags&&(Se[0]=255&se,Se[1]=se>>>8&255,Se[2]=se>>>16&255,Se[3]=se>>>24&255,r.check=o(r.check,Se,4,0)),se=0,ue=0,r.mode=k;case k:for(;ue<16;){if(0===oe)break e;oe--,se+=ee[re++]<<ue,ue+=8}r.head&&(r.head.xflags=255&se,r.head.os=se>>8),512&r.flags&&(Se[0]=255&se,Se[1]=se>>>8&255,r.check=o(r.check,Se,2,0)),se=0,ue=0,r.mode=N;case N:if(1024&r.flags){for(;ue<16;){if(0===oe)break e;oe--,se+=ee[re++]<<ue,ue+=8}r.length=se,r.head&&(r.head.extra_len=se),512&r.flags&&(Se[0]=255&se,Se[1]=se>>>8&255,r.check=o(r.check,Se,2,0)),se=0,ue=0}else r.head&&(r.head.extra=null);r.mode=S;case S:if(1024&r.flags&&((de=r.length)>oe&&(de=oe),de&&(r.head&&(Ee=r.head.extra_len-r.length,r.head.extra||(r.head.extra=new Array(r.head.extra_len)),n.arraySet(r.head.extra,ee,re,de,Ee)),512&r.flags&&(r.check=o(r.check,ee,de,re)),oe-=de,re+=de,r.length-=de),r.length))break e;r.length=0,r.mode=I;case I:if(2048&r.flags){if(0===oe)break e;de=0;do{Ee=ee[re+de++],r.head&&Ee&&r.length<65536&&(r.head.name+=String.fromCharCode(Ee))}while(Ee&&de<oe);if(512&r.flags&&(r.check=o(r.check,ee,de,re)),oe-=de,re+=de,Ee)break e}else r.head&&(r.head.name=null);r.length=0,r.mode=A;case A:if(4096&r.flags){if(0===oe)break e;de=0;do{Ee=ee[re+de++],r.head&&Ee&&r.length<65536&&(r.head.comment+=String.fromCharCode(Ee))}while(Ee&&de<oe);if(512&r.flags&&(r.check=o(r.check,ee,de,re)),oe-=de,re+=de,Ee)break e}else r.head&&(r.head.comment=null);r.mode=D;case D:if(512&r.flags){for(;ue<16;){if(0===oe)break e;oe--,se+=ee[re++]<<ue,ue+=8}if(se!==(65535&r.check)){e.msg="header crc mismatch",r.mode=Y;break}se=0,ue=0}r.head&&(r.head.hcrc=r.flags>>9&1,r.head.done=!0),e.adler=r.check=0,r.mode=M;break;case L:for(;ue<32;){if(0===oe)break e;oe--,se+=ee[re++]<<ue,ue+=8}e.adler=r.check=ne(se),se=0,ue=0,r.mode=O;case O:if(0===r.havedict)return e.next_out=ie,e.avail_out=ae,e.next_in=re,e.avail_in=oe,r.hold=se,r.bits=ue,y;e.adler=r.check=1,r.mode=M;case M:if(t===h||t===d)break e;case U:if(r.last){se>>>=7&ue,ue-=7&ue,r.mode=G;break}for(;ue<3;){if(0===oe)break e;oe--,se+=ee[re++]<<ue,ue+=8}switch(r.last=1&se,ue-=1,3&(se>>>=1)){case 0:r.mode=C;break;case 1:if(ce(r),r.mode=z,t===d){se>>>=2,ue-=2;break e}break;case 2:r.mode=P;break;case 3:e.msg="invalid block type",r.mode=Y}se>>>=2,ue-=2;break;case C:for(se>>>=7&ue,ue-=7&ue;ue<32;){if(0===oe)break e;oe--,se+=ee[re++]<<ue,ue+=8}if((65535&se)!=(se>>>16^65535)){e.msg="invalid stored block lengths",r.mode=Y;break}if(r.length=65535&se,se=0,ue=0,r.mode=B,t===d)break e;case B:r.mode=R;case R:if(de=r.length){if(de>oe&&(de=oe),de>ae&&(de=ae),0===de)break e;n.arraySet(te,ee,re,de,ie),oe-=de,re+=de,ae-=de,ie+=de,r.length-=de;break}r.mode=M;break;case P:for(;ue<14;){if(0===oe)break e;oe--,se+=ee[re++]<<ue,ue+=8}if(r.nlen=257+(31&se),se>>>=5,ue-=5,r.ndist=1+(31&se),se>>>=5,ue-=5,r.ncode=4+(15&se),se>>>=4,ue-=4,r.nlen>286||r.ndist>30){e.msg="too many length or distance symbols",r.mode=Y;break}r.have=0,r.mode=j;case j:for(;r.have<r.ncode;){for(;ue<3;){if(0===oe)break e;oe--,se+=ee[re++]<<ue,ue+=8}r.lens[Ie[r.have++]]=7&se,se>>>=3,ue-=3}for(;r.have<19;)r.lens[Ie[r.have++]]=0;if(r.lencode=r.lendyn,r.lenbits=7,Te={bits:r.lenbits},xe=s(u,r.lens,0,19,r.lencode,0,r.work,Te),r.lenbits=Te.bits,xe){e.msg="invalid code lengths set",r.mode=Y;break}r.have=0,r.mode=F;case F:for(;r.have<r.nlen+r.ndist;){for(;be=(Ne=r.lencode[se&(1<<r.lenbits)-1])>>>16&255,me=65535&Ne,!((ye=Ne>>>24)<=ue);){if(0===oe)break e;oe--,se+=ee[re++]<<ue,ue+=8}if(me<16)se>>>=ye,ue-=ye,r.lens[r.have++]=me;else{if(16===me){for(ke=ye+2;ue<ke;){if(0===oe)break e;oe--,se+=ee[re++]<<ue,ue+=8}if(se>>>=ye,ue-=ye,0===r.have){e.msg="invalid bit length repeat",r.mode=Y;break}Ee=r.lens[r.have-1],de=3+(3&se),se>>>=2,ue-=2}else if(17===me){for(ke=ye+3;ue<ke;){if(0===oe)break e;oe--,se+=ee[re++]<<ue,ue+=8}ue-=ye,Ee=0,de=3+(7&(se>>>=ye)),se>>>=3,ue-=3}else{for(ke=ye+7;ue<ke;){if(0===oe)break e;oe--,se+=ee[re++]<<ue,ue+=8}ue-=ye,Ee=0,de=11+(127&(se>>>=ye)),se>>>=7,ue-=7}if(r.have+de>r.nlen+r.ndist){e.msg="invalid bit length repeat",r.mode=Y;break}for(;de--;)r.lens[r.have++]=Ee}}if(r.mode===Y)break;if(0===r.lens[256]){e.msg="invalid code -- missing end-of-block",r.mode=Y;break}if(r.lenbits=9,Te={bits:r.lenbits},xe=s(l,r.lens,0,r.nlen,r.lencode,0,r.work,Te),r.lenbits=Te.bits,xe){e.msg="invalid literal/lengths set",r.mode=Y;break}if(r.distbits=6,r.distcode=r.distdyn,Te={bits:r.distbits},xe=s(f,r.lens,r.nlen,r.ndist,r.distcode,0,r.work,Te),r.distbits=Te.bits,xe){e.msg="invalid distances set",r.mode=Y;break}if(r.mode=z,t===d)break e;case z:r.mode=q;case q:if(oe>=6&&ae>=258){e.next_out=ie,e.avail_out=ae,e.next_in=re,e.avail_in=oe,r.hold=se,r.bits=ue,a(e,fe),ie=e.next_out,te=e.output,ae=e.avail_out,re=e.next_in,ee=e.input,oe=e.avail_in,se=r.hold,ue=r.bits,r.mode===M&&(r.back=-1);break}for(r.back=0;be=(Ne=r.lencode[se&(1<<r.lenbits)-1])>>>16&255,me=65535&Ne,!((ye=Ne>>>24)<=ue);){if(0===oe)break e;oe--,se+=ee[re++]<<ue,ue+=8}if(be&&0==(240&be)){for(ve=ye,we=be,_e=me;be=(Ne=r.lencode[_e+((se&(1<<ve+we)-1)>>ve)])>>>16&255,me=65535&Ne,!(ve+(ye=Ne>>>24)<=ue);){if(0===oe)break e;oe--,se+=ee[re++]<<ue,ue+=8}se>>>=ve,ue-=ve,r.back+=ve}if(se>>>=ye,ue-=ye,r.back+=ye,r.length=me,0===be){r.mode=W;break}if(32&be){r.back=-1,r.mode=M;break}if(64&be){e.msg="invalid literal/length code",r.mode=Y;break}r.extra=15&be,r.mode=X;case X:if(r.extra){for(ke=r.extra;ue<ke;){if(0===oe)break e;oe--,se+=ee[re++]<<ue,ue+=8}r.length+=se&(1<<r.extra)-1,se>>>=r.extra,ue-=r.extra,r.back+=r.extra}r.was=r.length,r.mode=Z;case Z:for(;be=(Ne=r.distcode[se&(1<<r.distbits)-1])>>>16&255,me=65535&Ne,!((ye=Ne>>>24)<=ue);){if(0===oe)break e;oe--,se+=ee[re++]<<ue,ue+=8}if(0==(240&be)){for(ve=ye,we=be,_e=me;be=(Ne=r.distcode[_e+((se&(1<<ve+we)-1)>>ve)])>>>16&255,me=65535&Ne,!(ve+(ye=Ne>>>24)<=ue);){if(0===oe)break e;oe--,se+=ee[re++]<<ue,ue+=8}se>>>=ve,ue-=ve,r.back+=ve}if(se>>>=ye,ue-=ye,r.back+=ye,64&be){e.msg="invalid distance code",r.mode=Y;break}r.offset=me,r.extra=15&be,r.mode=V;case V:if(r.extra){for(ke=r.extra;ue<ke;){if(0===oe)break e;oe--,se+=ee[re++]<<ue,ue+=8}r.offset+=se&(1<<r.extra)-1,se>>>=r.extra,ue-=r.extra,r.back+=r.extra}if(r.offset>r.dmax){e.msg="invalid distance too far back",r.mode=Y;break}r.mode=H;case H:if(0===ae)break e;if(de=fe-ae,r.offset>de){if((de=r.offset-de)>r.whave&&r.sane){e.msg="invalid distance too far back",r.mode=Y;break}de>r.wnext?(de-=r.wnext,pe=r.wsize-de):pe=r.wnext-de,de>r.length&&(de=r.length),ge=r.window}else ge=te,pe=ie-r.offset,de=r.length;de>ae&&(de=ae),ae-=de,r.length-=de;do{te[ie++]=ge[pe++]}while(--de);0===r.length&&(r.mode=q);break;case W:if(0===ae)break e;te[ie++]=r.length,ae--,r.mode=q;break;case G:if(r.wrap){for(;ue<32;){if(0===oe)break e;oe--,se|=ee[re++]<<ue,ue+=8}if(fe-=ae,e.total_out+=fe,r.total+=fe,fe&&(e.adler=r.check=r.flags?o(r.check,te,fe,ie-fe):i(r.check,te,fe,ie-fe)),fe=ae,(r.flags?se:ne(se))!==r.check){e.msg="incorrect data check",r.mode=Y;break}se=0,ue=0}r.mode=K;case K:if(r.wrap&&r.flags){for(;ue<32;){if(0===oe)break e;oe--,se+=ee[re++]<<ue,ue+=8}if(se!==(4294967295&r.total)){e.msg="incorrect length check",r.mode=Y;break}se=0,ue=0}r.mode=$;case $:xe=g;break e;case Y:xe=m;break e;case J:return v;case Q:default:return b}return e.next_out=ie,e.avail_out=ae,e.next_in=re,e.avail_in=oe,r.hold=se,r.bits=ue,(r.wsize||fe!==e.avail_out&&r.mode<Y&&(r.mode<G||t!==c))&&he(e,e.output,e.next_out,fe-e.avail_out)?(r.mode=J,v):(le-=e.avail_in,fe-=e.avail_out,e.total_in+=le,e.total_out+=fe,r.total+=fe,r.wrap&&fe&&(e.adler=r.check=r.flags?o(r.check,te,fe,e.next_out-fe):i(r.check,te,fe,e.next_out-fe)),e.data_type=r.bits+(r.last?64:0)+(r.mode===M?128:0)+(r.mode===z||r.mode===B?256:0),(0===le&&0===fe||t===c)&&xe===p&&(xe=w),xe)},r.inflateEnd=function(e){if(!e||!e.state)return b;var t=e.state;return t.window&&(t.window=null),e.state=null,p},r.inflateGetHeader=function(e,t){var r;return e&&e.state?0==(2&(r=e.state).wrap)?b:(r.head=t,t.done=!1,p):b},r.inflateSetDictionary=function(e,t){var r,n=t.length;return e&&e.state?0!==(r=e.state).wrap&&r.mode!==O?b:r.mode===O&&i(1,t,n,0)!==r.check?m:he(e,t,n,n)?(r.mode=J,v):(r.havedict=1,p):b},r.inflateInfo="pako inflate (from Nodeca project)"},{"../utils/common":81,"./adler32":82,"./crc32":84,"./inffast":86,"./inftrees":88}],88:[function(e,t,r){"use strict";var n=e("../utils/common"),i=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],o=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],a=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],s=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];t.exports=function(e,t,r,u,l,f,c,h){var d,p,g,y,b,m,v,w,_,E=h.bits,x=0,T=0,k=0,N=0,S=0,I=0,A=0,D=0,L=0,O=0,M=null,U=0,C=new n.Buf16(16),B=new n.Buf16(16),R=null,P=0;for(x=0;x<=15;x++)C[x]=0;for(T=0;T<u;T++)C[t[r+T]]++;for(S=E,N=15;N>=1&&0===C[N];N--);if(S>N&&(S=N),0===N)return l[f++]=20971520,l[f++]=20971520,h.bits=1,0;for(k=1;k<N&&0===C[k];k++);for(S<k&&(S=k),D=1,x=1;x<=15;x++)if(D<<=1,(D-=C[x])<0)return-1;if(D>0&&(0===e||1!==N))return-1;for(B[1]=0,x=1;x<15;x++)B[x+1]=B[x]+C[x];for(T=0;T<u;T++)0!==t[r+T]&&(c[B[t[r+T]]++]=T);if(0===e?(M=R=c,m=19):1===e?(M=i,U-=257,R=o,P-=257,m=256):(M=a,R=s,m=-1),O=0,T=0,x=k,b=f,I=S,A=0,g=-1,y=(L=1<<S)-1,1===e&&L>852||2===e&&L>592)return 1;for(;;){v=x-A,c[T]<m?(w=0,_=c[T]):c[T]>m?(w=R[P+c[T]],_=M[U+c[T]]):(w=96,_=0),d=1<<x-A,k=p=1<<I;do{l[b+(O>>A)+(p-=d)]=v<<24|w<<16|_|0}while(0!==p);for(d=1<<x-1;O&d;)d>>=1;if(0!==d?(O&=d-1,O+=d):O=0,T++,0==--C[x]){if(x===N)break;x=t[r+c[T]]}if(x>S&&(O&y)!==g){for(0===A&&(A=S),b+=k,D=1<<(I=x-A);I+A<N&&!((D-=C[I+A])<=0);)I++,D<<=1;if(L+=1<<I,1===e&&L>852||2===e&&L>592)return 1;l[g=O&y]=S<<24|I<<16|b-f|0}}return 0!==O&&(l[b+O]=x-A<<24|64<<16|0),h.bits=S,0}},{"../utils/common":81}],89:[function(e,t,r){"use strict";t.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],90:[function(e,t,r){"use strict";var n=e("../utils/common"),i=4,o=0,a=1,s=2;function u(e){for(var t=e.length;--t>=0;)e[t]=0}var l=0,f=1,c=2,h=29,d=256,p=d+1+h,g=30,y=19,b=2*p+1,m=15,v=16,w=7,_=256,E=16,x=17,T=18,k=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],N=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],S=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],I=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],A=new Array(2*(p+2));u(A);var D=new Array(2*g);u(D);var L=new Array(512);u(L);var O=new Array(256);u(O);var M=new Array(h);u(M);var U,C,B,R=new Array(g);function P(e,t,r,n,i){this.static_tree=e,this.extra_bits=t,this.extra_base=r,this.elems=n,this.max_length=i,this.has_stree=e&&e.length}function j(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}function F(e){return e<256?L[e]:L[256+(e>>>7)]}function z(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255}function q(e,t,r){e.bi_valid>v-r?(e.bi_buf|=t<<e.bi_valid&65535,z(e,e.bi_buf),e.bi_buf=t>>v-e.bi_valid,e.bi_valid+=r-v):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=r)}function X(e,t,r){q(e,r[2*t],r[2*t+1])}function Z(e,t){var r=0;do{r|=1&e,e>>>=1,r<<=1}while(--t>0);return r>>>1}function V(e,t,r){var n,i,o=new Array(m+1),a=0;for(n=1;n<=m;n++)o[n]=a=a+r[n-1]<<1;for(i=0;i<=t;i++){var s=e[2*i+1];0!==s&&(e[2*i]=Z(o[s]++,s))}}function H(e){var t;for(t=0;t<p;t++)e.dyn_ltree[2*t]=0;for(t=0;t<g;t++)e.dyn_dtree[2*t]=0;for(t=0;t<y;t++)e.bl_tree[2*t]=0;e.dyn_ltree[2*_]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0}function W(e){e.bi_valid>8?z(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0}function G(e,t,r,n){var i=2*t,o=2*r;return e[i]<e[o]||e[i]===e[o]&&n[t]<=n[r]}function K(e,t,r){for(var n=e.heap[r],i=r<<1;i<=e.heap_len&&(i<e.heap_len&&G(t,e.heap[i+1],e.heap[i],e.depth)&&i++,!G(t,n,e.heap[i],e.depth));)e.heap[r]=e.heap[i],r=i,i<<=1;e.heap[r]=n}function $(e,t,r){var n,i,o,a,s=0;if(0!==e.last_lit)do{n=e.pending_buf[e.d_buf+2*s]<<8|e.pending_buf[e.d_buf+2*s+1],i=e.pending_buf[e.l_buf+s],s++,0===n?X(e,i,t):(X(e,(o=O[i])+d+1,t),0!==(a=k[o])&&q(e,i-=M[o],a),X(e,o=F(--n),r),0!==(a=N[o])&&q(e,n-=R[o],a))}while(s<e.last_lit);X(e,_,t)}function Y(e,t){var r,n,i,o=t.dyn_tree,a=t.stat_desc.static_tree,s=t.stat_desc.has_stree,u=t.stat_desc.elems,l=-1;for(e.heap_len=0,e.heap_max=b,r=0;r<u;r++)0!==o[2*r]?(e.heap[++e.heap_len]=l=r,e.depth[r]=0):o[2*r+1]=0;for(;e.heap_len<2;)o[2*(i=e.heap[++e.heap_len]=l<2?++l:0)]=1,e.depth[i]=0,e.opt_len--,s&&(e.static_len-=a[2*i+1]);for(t.max_code=l,r=e.heap_len>>1;r>=1;r--)K(e,o,r);i=u;do{r=e.heap[1],e.heap[1]=e.heap[e.heap_len--],K(e,o,1),n=e.heap[1],e.heap[--e.heap_max]=r,e.heap[--e.heap_max]=n,o[2*i]=o[2*r]+o[2*n],e.depth[i]=(e.depth[r]>=e.depth[n]?e.depth[r]:e.depth[n])+1,o[2*r+1]=o[2*n+1]=i,e.heap[1]=i++,K(e,o,1)}while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],function(e,t){var r,n,i,o,a,s,u=t.dyn_tree,l=t.max_code,f=t.stat_desc.static_tree,c=t.stat_desc.has_stree,h=t.stat_desc.extra_bits,d=t.stat_desc.extra_base,p=t.stat_desc.max_length,g=0;for(o=0;o<=m;o++)e.bl_count[o]=0;for(u[2*e.heap[e.heap_max]+1]=0,r=e.heap_max+1;r<b;r++)(o=u[2*u[2*(n=e.heap[r])+1]+1]+1)>p&&(o=p,g++),u[2*n+1]=o,n>l||(e.bl_count[o]++,a=0,n>=d&&(a=h[n-d]),s=u[2*n],e.opt_len+=s*(o+a),c&&(e.static_len+=s*(f[2*n+1]+a)));if(0!==g){do{for(o=p-1;0===e.bl_count[o];)o--;e.bl_count[o]--,e.bl_count[o+1]+=2,e.bl_count[p]--,g-=2}while(g>0);for(o=p;0!==o;o--)for(n=e.bl_count[o];0!==n;)(i=e.heap[--r])>l||(u[2*i+1]!==o&&(e.opt_len+=(o-u[2*i+1])*u[2*i],u[2*i+1]=o),n--)}}(e,t),V(o,l,e.bl_count)}function J(e,t,r){var n,i,o=-1,a=t[1],s=0,u=7,l=4;for(0===a&&(u=138,l=3),t[2*(r+1)+1]=65535,n=0;n<=r;n++)i=a,a=t[2*(n+1)+1],++s<u&&i===a||(s<l?e.bl_tree[2*i]+=s:0!==i?(i!==o&&e.bl_tree[2*i]++,e.bl_tree[2*E]++):s<=10?e.bl_tree[2*x]++:e.bl_tree[2*T]++,s=0,o=i,0===a?(u=138,l=3):i===a?(u=6,l=3):(u=7,l=4))}function Q(e,t,r){var n,i,o=-1,a=t[1],s=0,u=7,l=4;for(0===a&&(u=138,l=3),n=0;n<=r;n++)if(i=a,a=t[2*(n+1)+1],!(++s<u&&i===a)){if(s<l)do{X(e,i,e.bl_tree)}while(0!=--s);else 0!==i?(i!==o&&(X(e,i,e.bl_tree),s--),X(e,E,e.bl_tree),q(e,s-3,2)):s<=10?(X(e,x,e.bl_tree),q(e,s-3,3)):(X(e,T,e.bl_tree),q(e,s-11,7));s=0,o=i,0===a?(u=138,l=3):i===a?(u=6,l=3):(u=7,l=4)}}u(R);var ee=!1;function te(e,t,r,i){q(e,(l<<1)+(i?1:0),3),function(e,t,r,i){W(e),i&&(z(e,r),z(e,~r)),n.arraySet(e.pending_buf,e.window,t,r,e.pending),e.pending+=r}(e,t,r,!0)}r._tr_init=function(e){ee||(function(){var e,t,r,n,i,o=new Array(m+1);for(r=0,n=0;n<h-1;n++)for(M[n]=r,e=0;e<1<<k[n];e++)O[r++]=n;for(O[r-1]=n,i=0,n=0;n<16;n++)for(R[n]=i,e=0;e<1<<N[n];e++)L[i++]=n;for(i>>=7;n<g;n++)for(R[n]=i<<7,e=0;e<1<<N[n]-7;e++)L[256+i++]=n;for(t=0;t<=m;t++)o[t]=0;for(e=0;e<=143;)A[2*e+1]=8,e++,o[8]++;for(;e<=255;)A[2*e+1]=9,e++,o[9]++;for(;e<=279;)A[2*e+1]=7,e++,o[7]++;for(;e<=287;)A[2*e+1]=8,e++,o[8]++;for(V(A,p+1,o),e=0;e<g;e++)D[2*e+1]=5,D[2*e]=Z(e,5);U=new P(A,k,d+1,p,m),C=new P(D,N,0,g,m),B=new P(new Array(0),S,0,y,w)}(),ee=!0),e.l_desc=new j(e.dyn_ltree,U),e.d_desc=new j(e.dyn_dtree,C),e.bl_desc=new j(e.bl_tree,B),e.bi_buf=0,e.bi_valid=0,H(e)},r._tr_stored_block=te,r._tr_flush_block=function(e,t,r,n){var u,l,h=0;e.level>0?(e.strm.data_type===s&&(e.strm.data_type=function(e){var t,r=4093624447;for(t=0;t<=31;t++,r>>>=1)if(1&r&&0!==e.dyn_ltree[2*t])return o;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return a;for(t=32;t<d;t++)if(0!==e.dyn_ltree[2*t])return a;return o}(e)),Y(e,e.l_desc),Y(e,e.d_desc),h=function(e){var t;for(J(e,e.dyn_ltree,e.l_desc.max_code),J(e,e.dyn_dtree,e.d_desc.max_code),Y(e,e.bl_desc),t=y-1;t>=3&&0===e.bl_tree[2*I[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}(e),u=e.opt_len+3+7>>>3,(l=e.static_len+3+7>>>3)<=u&&(u=l)):u=l=r+5,r+4<=u&&-1!==t?te(e,t,r,n):e.strategy===i||l===u?(q(e,(f<<1)+(n?1:0),3),$(e,A,D)):(q(e,(c<<1)+(n?1:0),3),function(e,t,r,n){var i;for(q(e,t-257,5),q(e,r-1,5),q(e,n-4,4),i=0;i<n;i++)q(e,e.bl_tree[2*I[i]+1],3);Q(e,e.dyn_ltree,t-1),Q(e,e.dyn_dtree,r-1)}(e,e.l_desc.max_code+1,e.d_desc.max_code+1,h+1),$(e,e.dyn_ltree,e.dyn_dtree)),H(e),n&&W(e)},r._tr_tally=function(e,t,r){return e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&r,e.last_lit++,0===t?e.dyn_ltree[2*r]++:(e.matches++,t--,e.dyn_ltree[2*(O[r]+d+1)]++,e.dyn_dtree[2*F(t)]++),e.last_lit===e.lit_bufsize-1},r._tr_align=function(e){q(e,f<<1,3),X(e,_,A),function(e){16===e.bi_valid?(z(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):e.bi_valid>=8&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}(e)}},{"../utils/common":81}],91:[function(e,t,r){"use strict";t.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},{}],92:[function(e,t,r){(function(e){(function(){function t(e,t){for(var r=0,n=e.length-1;n>=0;n--){var i=e[n];"."===i?e.splice(n,1):".."===i?(e.splice(n,1),r++):r&&(e.splice(n,1),r--)}if(t)for(;r--;r)e.unshift("..");return e}function n(e,t){if(e.filter)return e.filter(t);for(var r=[],n=0;n<e.length;n++)t(e[n],n,e)&&r.push(e[n]);return r}r.resolve=function(){for(var r="",i=!1,o=arguments.length-1;o>=-1&&!i;o--){var a=o>=0?arguments[o]:e.cwd();if("string"!=typeof a)throw new TypeError("Arguments to path.resolve must be strings");a&&(r=a+"/"+r,i="/"===a.charAt(0))}return r=t(n(r.split("/"),function(e){return!!e}),!i).join("/"),(i?"/":"")+r||"."},r.normalize=function(e){var o=r.isAbsolute(e),a="/"===i(e,-1);return(e=t(n(e.split("/"),function(e){return!!e}),!o).join("/"))||o||(e="."),e&&a&&(e+="/"),(o?"/":"")+e},r.isAbsolute=function(e){return"/"===e.charAt(0)},r.join=function(){var e=Array.prototype.slice.call(arguments,0);return r.normalize(n(e,function(e,t){if("string"!=typeof e)throw new TypeError("Arguments to path.join must be strings");return e}).join("/"))},r.relative=function(e,t){function n(e){for(var t=0;t<e.length&&""===e[t];t++);for(var r=e.length-1;r>=0&&""===e[r];r--);return t>r?[]:e.slice(t,r-t+1)}e=r.resolve(e).substr(1),t=r.resolve(t).substr(1);for(var i=n(e.split("/")),o=n(t.split("/")),a=Math.min(i.length,o.length),s=a,u=0;u<a;u++)if(i[u]!==o[u]){s=u;break}var l=[];for(u=s;u<i.length;u++)l.push("..");return(l=l.concat(o.slice(s))).join("/")},r.sep="/",r.delimiter=":",r.dirname=function(e){if("string"!=typeof e&&(e+=""),0===e.length)return".";for(var t=e.charCodeAt(0),r=47===t,n=-1,i=!0,o=e.length-1;o>=1;--o)if(47===(t=e.charCodeAt(o))){if(!i){n=o;break}}else i=!1;return-1===n?r?"/":".":r&&1===n?"/":e.slice(0,n)},r.basename=function(e,t){var r=function(e){"string"!=typeof e&&(e+="");var t,r=0,n=-1,i=!0;for(t=e.length-1;t>=0;--t)if(47===e.charCodeAt(t)){if(!i){r=t+1;break}}else-1===n&&(i=!1,n=t+1);return-1===n?"":e.slice(r,n)}(e);return t&&r.substr(-1*t.length)===t&&(r=r.substr(0,r.length-t.length)),r},r.extname=function(e){"string"!=typeof e&&(e+="");for(var t=-1,r=0,n=-1,i=!0,o=0,a=e.length-1;a>=0;--a){var s=e.charCodeAt(a);if(47!==s)-1===n&&(i=!1,n=a+1),46===s?-1===t?t=a:1!==o&&(o=1):-1!==t&&(o=-1);else if(!i){r=a+1;break}}return-1===t||-1===n||0===o||1===o&&t===n-1&&t===r+1?"":e.slice(t,n)};var i="b"==="ab".substr(-1)?function(e,t,r){return e.substr(t,r)}:function(e,t,r){return t<0&&(t=e.length+t),e.substr(t,r)}}).call(this)}).call(this,e("_process"))},{_process:97}],93:[function(e,t,r){var n=e("./lib/parse");Object.keys(n).forEach(function(e){r[e]=n[e]});var i=e("./lib/build");Object.keys(i).forEach(function(e){r[e]=i[e]})},{"./lib/build":94,"./lib/parse":95}],94:[function(e,t,r){(function(t){(function(){var n=e("base64-js"),i=e("xmlbuilder");r.build=function(e,r){var o={version:"1.0",encoding:"UTF-8"},s={pubid:"-//Apple//DTD PLIST 1.0//EN",sysid:"http://www.apple.com/DTDs/PropertyList-1.0.dtd"},u=i.create("plist");u.dec(o.version,o.encoding,o.standalone),u.dtd(s.pubid,s.sysid),u.att("version","1.0"),function e(r,i){var o,s,u,l=a(r);if("Undefined"!=l)if(Array.isArray(r))for(i=i.ele("array"),s=0;s<r.length;s++)e(r[s],i);else if(t.isBuffer(r))i.ele("data").raw(r.toString("base64"));else if("Object"==l)for(u in i=i.ele("dict"),r)r.hasOwnProperty(u)&&(i.ele("key").txt(u),e(r[u],i));else"Number"==l?(o=r%1==0?"integer":"real",i.ele(o).txt(r.toString())):"BigInt"==l?i.ele("integer").txt(r):"Date"==l?i.ele("date").txt(function(e){function t(e){return e<10?"0"+e:e}return e.getUTCFullYear()+"-"+t(e.getUTCMonth()+1)+"-"+t(e.getUTCDate())+"T"+t(e.getUTCHours())+":"+t(e.getUTCMinutes())+":"+t(e.getUTCSeconds())+"Z"}(new Date(r))):"Boolean"==l?i.ele(r?"true":"false"):"String"==l?i.ele("string").txt(r):"ArrayBuffer"==l?i.ele("data").raw(n.fromByteArray(r)):r&&r.buffer&&"ArrayBuffer"==a(r.buffer)?i.ele("data").raw(n.fromByteArray(new Uint8Array(r.buffer),i)):"Null"===l&&i.ele("null").txt("")}(e,u),r||(r={});return r.pretty=!1!==r.pretty,u.end(r)};var o=Object.prototype.toString;function a(e){var t=o.call(e).match(/\[object (.*)\]/);return t?t[1]:t}}).call(this)}).call(this,{isBuffer:e("../../is-buffer/index.js")})},{"../../is-buffer/index.js":68,"base64-js":16,xmlbuilder:155}],95:[function(e,t,r){(function(t){(function(){const{DOMParser:n}=e("@xmldom/xmldom");r.parse=function(e){var r=(new n).parseFromString(e);l("plist"===r.documentElement.nodeName,"malformed document. First element should be <plist>");var a=function e(r){var n,a,f,c,h,d;if(!r)return null;if("plist"===r.nodeName){if(c=[],u(r))return c;for(n=0;n<r.childNodes.length;n++)s(r.childNodes[n])||c.push(e(r.childNodes[n]));return c}if("dict"===r.nodeName){if(a={},f=null,d=0,u(r))return a;for(n=0;n<r.childNodes.length;n++)s(r.childNodes[n])||(d%2==0?(l("key"===r.childNodes[n].nodeName,"Missing key while parsing <dict/>."),f=e(r.childNodes[n])):(l("key"!==r.childNodes[n].nodeName,'Unexpected key "'+e(r.childNodes[n])+'" while parsing <dict/>.'),a[f]=e(r.childNodes[n])),d+=1);return d%2==1&&(a[f]=""),a}if("array"===r.nodeName){if(c=[],u(r))return c;for(n=0;n<r.childNodes.length;n++)s(r.childNodes[n])||null!=(h=e(r.childNodes[n]))&&c.push(h);return c}if("#text"!==r.nodeName){if("key"===r.nodeName)return u(r)?"":(l("__proto__"!==r.childNodes[0].nodeValue,"__proto__ keys can lead to prototype pollution. More details on CVE-2022-22912"),r.childNodes[0].nodeValue);if("string"===r.nodeName){if(h="",u(r))return h;for(n=0;n<r.childNodes.length;n++){var p=r.childNodes[n].nodeType;p!==i&&p!==o||(h+=r.childNodes[n].nodeValue)}return h}if("integer"===r.nodeName)return l(!u(r),'Cannot parse "" as integer.'),parseInt(r.childNodes[0].nodeValue,10);if("real"===r.nodeName){for(l(!u(r),'Cannot parse "" as real.'),h="",n=0;n<r.childNodes.length;n++)r.childNodes[n].nodeType===i&&(h+=r.childNodes[n].nodeValue);return parseFloat(h)}if("data"===r.nodeName){if(h="",u(r))return t.from(h,"base64");for(n=0;n<r.childNodes.length;n++)r.childNodes[n].nodeType===i&&(h+=r.childNodes[n].nodeValue.replace(/\s+/g,""));return t.from(h,"base64")}if("date"===r.nodeName)return l(!u(r),'Cannot parse "" as Date.'),new Date(r.childNodes[0].nodeValue);if("null"===r.nodeName)return null;if("true"===r.nodeName)return!0;if("false"===r.nodeName)return!1;throw new Error("Invalid PLIST tag "+r.nodeName)}}(r.documentElement);1==a.length&&(a=a[0]);return a};var i=3,o=4,a=8;function s(e){return e.nodeType===i||e.nodeType===a||e.nodeType===o}function u(e){return!e.childNodes||0===e.childNodes.length}function l(e,t){if(!e)throw new Error(t)}}).call(this)}).call(this,e("buffer").Buffer)},{"@xmldom/xmldom":13,buffer:23}],96:[function(e,t,r){(function(e){(function(){"use strict";void 0===e||!e.version||0===e.version.indexOf("v0.")||0===e.version.indexOf("v1.")&&0!==e.version.indexOf("v1.8.")?t.exports={nextTick:function(t,r,n,i){if("function"!=typeof t)throw new TypeError('"callback" argument must be a function');var o,a,s=arguments.length;switch(s){case 0:case 1:return e.nextTick(t);case 2:return e.nextTick(function(){t.call(null,r)});case 3:return e.nextTick(function(){t.call(null,r,n)});case 4:return e.nextTick(function(){t.call(null,r,n,i)});default:for(o=new Array(s-1),a=0;a<o.length;)o[a++]=arguments[a];return e.nextTick(function(){t.apply(null,o)})}}}:t.exports=e}).call(this)}).call(this,e("_process"))},{_process:97}],97:[function(e,t,r){var n,i,o=t.exports={};function a(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function u(e){if(n===setTimeout)return setTimeout(e,0);if((n===a||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:a}catch(e){n=a}try{i="function"==typeof clearTimeout?clearTimeout:s}catch(e){i=s}}();var l,f=[],c=!1,h=-1;function d(){c&&l&&(c=!1,l.length?f=l.concat(f):h=-1,f.length&&p())}function p(){if(!c){var e=u(d);c=!0;for(var t=f.length;t;){for(l=f,f=[];++h<t;)l&&l[h].run();h=-1,t=f.length}l=null,c=!1,function(e){if(i===clearTimeout)return clearTimeout(e);if((i===s||!i)&&clearTimeout)return i=clearTimeout,clearTimeout(e);try{i(e)}catch(t){try{return i.call(null,e)}catch(t){return i.call(this,e)}}}(e)}}function g(e,t){this.fun=e,this.array=t}function y(){}o.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];f.push(new g(e,t)),1!==f.length||c||u(p)},g.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=y,o.addListener=y,o.once=y,o.off=y,o.removeListener=y,o.removeAllListeners=y,o.emit=y,o.prependListener=y,o.prependOnceListener=y,o.listeners=function(e){return[]},o.binding=function(e){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},{}],98:[function(e,t,r){t.exports=e("./lib/_stream_duplex.js")},{"./lib/_stream_duplex.js":99}],99:[function(e,t,r){"use strict";var n=e("process-nextick-args"),i=Object.keys||function(e){var t=[];for(var r in e)t.push(r);return t};t.exports=c;var o=Object.create(e("core-util-is"));o.inherits=e("inherits");var a=e("./_stream_readable"),s=e("./_stream_writable");o.inherits(c,a);for(var u=i(s.prototype),l=0;l<u.length;l++){var f=u[l];c.prototype[f]||(c.prototype[f]=s.prototype[f])}function c(e){if(!(this instanceof c))return new c(e);a.call(this,e),s.call(this,e),e&&!1===e.readable&&(this.readable=!1),e&&!1===e.writable&&(this.writable=!1),this.allowHalfOpen=!0,e&&!1===e.allowHalfOpen&&(this.allowHalfOpen=!1),this.once("end",h)}function h(){this.allowHalfOpen||this._writableState.ended||n.nextTick(d,this)}function d(e){e.end()}Object.defineProperty(c.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(c.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(e){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}}),c.prototype._destroy=function(e,t){this.push(null),this.end(),n.nextTick(t,e)}},{"./_stream_readable":101,"./_stream_writable":103,"core-util-is":29,inherits:67,"process-nextick-args":96}],100:[function(e,t,r){"use strict";t.exports=o;var n=e("./_stream_transform"),i=Object.create(e("core-util-is"));function o(e){if(!(this instanceof o))return new o(e);n.call(this,e)}i.inherits=e("inherits"),i.inherits(o,n),o.prototype._transform=function(e,t,r){r(null,e)}},{"./_stream_transform":102,"core-util-is":29,inherits:67}],101:[function(e,t,r){(function(r,n){(function(){"use strict";var i=e("process-nextick-args");t.exports=v;var o,a=e("isarray");v.ReadableState=m;e("events").EventEmitter;var s=function(e,t){return e.listeners(t).length},u=e("./internal/streams/stream"),l=e("safe-buffer").Buffer,f=(void 0!==n?n:"undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).Uint8Array||function(){};var c=Object.create(e("core-util-is"));c.inherits=e("inherits");var h=e("util"),d=void 0;d=h&&h.debuglog?h.debuglog("stream"):function(){};var p,g=e("./internal/streams/BufferList"),y=e("./internal/streams/destroy");c.inherits(v,u);var b=["error","close","destroy","pause","resume"];function m(t,r){o=o||e("./_stream_duplex"),t=t||{};var n=r instanceof o;this.objectMode=!!t.objectMode,n&&(this.objectMode=this.objectMode||!!t.readableObjectMode);var i=t.highWaterMark,a=t.readableHighWaterMark,s=this.objectMode?16:16384;this.highWaterMark=i||0===i?i:n&&(a||0===a)?a:s,this.highWaterMark=Math.floor(this.highWaterMark),this.buffer=new g,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.destroyed=!1,this.defaultEncoding=t.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,t.encoding&&(p||(p=e("string_decoder/").StringDecoder),this.decoder=new p(t.encoding),this.encoding=t.encoding)}function v(t){if(o=o||e("./_stream_duplex"),!(this instanceof v))return new v(t);this._readableState=new m(t,this),this.readable=!0,t&&("function"==typeof t.read&&(this._read=t.read),"function"==typeof t.destroy&&(this._destroy=t.destroy)),u.call(this)}function w(e,t,r,n,i){var o,a=e._readableState;null===t?(a.reading=!1,function(e,t){if(t.ended)return;if(t.decoder){var r=t.decoder.end();r&&r.length&&(t.buffer.push(r),t.length+=t.objectMode?1:r.length)}t.ended=!0,T(e)}(e,a)):(i||(o=function(e,t){var r;n=t,l.isBuffer(n)||n instanceof f||"string"==typeof t||void 0===t||e.objectMode||(r=new TypeError("Invalid non-string/buffer chunk"));var n;return r}(a,t)),o?e.emit("error",o):a.objectMode||t&&t.length>0?("string"==typeof t||a.objectMode||Object.getPrototypeOf(t)===l.prototype||(t=function(e){return l.from(e)}(t)),n?a.endEmitted?e.emit("error",new Error("stream.unshift() after end event")):_(e,a,t,!0):a.ended?e.emit("error",new Error("stream.push() after EOF")):(a.reading=!1,a.decoder&&!r?(t=a.decoder.write(t),a.objectMode||0!==t.length?_(e,a,t,!1):N(e,a)):_(e,a,t,!1))):n||(a.reading=!1));return function(e){return!e.ended&&(e.needReadable||e.length<e.highWaterMark||0===e.length)}(a)}function _(e,t,r,n){t.flowing&&0===t.length&&!t.sync?(e.emit("data",r),e.read(0)):(t.length+=t.objectMode?1:r.length,n?t.buffer.unshift(r):t.buffer.push(r),t.needReadable&&T(e)),N(e,t)}Object.defineProperty(v.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(e){this._readableState&&(this._readableState.destroyed=e)}}),v.prototype.destroy=y.destroy,v.prototype._undestroy=y.undestroy,v.prototype._destroy=function(e,t){this.push(null),t(e)},v.prototype.push=function(e,t){var r,n=this._readableState;return n.objectMode?r=!0:"string"==typeof e&&((t=t||n.defaultEncoding)!==n.encoding&&(e=l.from(e,t),t=""),r=!0),w(this,e,t,!1,r)},v.prototype.unshift=function(e){return w(this,e,null,!0,!1)},v.prototype.isPaused=function(){return!1===this._readableState.flowing},v.prototype.setEncoding=function(t){return p||(p=e("string_decoder/").StringDecoder),this._readableState.decoder=new p(t),this._readableState.encoding=t,this};var E=8388608;function x(e,t){return e<=0||0===t.length&&t.ended?0:t.objectMode?1:e!=e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=function(e){return e>=E?e=E:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}function T(e){var t=e._readableState;t.needReadable=!1,t.emittedReadable||(d("emitReadable",t.flowing),t.emittedReadable=!0,t.sync?i.nextTick(k,e):k(e))}function k(e){d("emit readable"),e.emit("readable"),D(e)}function N(e,t){t.readingMore||(t.readingMore=!0,i.nextTick(S,e,t))}function S(e,t){for(var r=t.length;!t.reading&&!t.flowing&&!t.ended&&t.length<t.highWaterMark&&(d("maybeReadMore read 0"),e.read(0),r!==t.length);)r=t.length;t.readingMore=!1}function I(e){d("readable nexttick read 0"),e.read(0)}function A(e,t){t.reading||(d("resume read 0"),e.read(0)),t.resumeScheduled=!1,t.awaitDrain=0,e.emit("resume"),D(e),t.flowing&&!t.reading&&e.read(0)}function D(e){var t=e._readableState;for(d("flow",t.flowing);t.flowing&&null!==e.read(););}function L(e,t){return 0===t.length?null:(t.objectMode?r=t.buffer.shift():!e||e>=t.length?(r=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.head.data:t.buffer.concat(t.length),t.buffer.clear()):r=function(e,t,r){var n;e<t.head.data.length?(n=t.head.data.slice(0,e),t.head.data=t.head.data.slice(e)):n=e===t.head.data.length?t.shift():r?function(e,t){var r=t.head,n=1,i=r.data;e-=i.length;for(;r=r.next;){var o=r.data,a=e>o.length?o.length:e;if(a===o.length?i+=o:i+=o.slice(0,e),0===(e-=a)){a===o.length?(++n,r.next?t.head=r.next:t.head=t.tail=null):(t.head=r,r.data=o.slice(a));break}++n}return t.length-=n,i}(e,t):function(e,t){var r=l.allocUnsafe(e),n=t.head,i=1;n.data.copy(r),e-=n.data.length;for(;n=n.next;){var o=n.data,a=e>o.length?o.length:e;if(o.copy(r,r.length-e,0,a),0===(e-=a)){a===o.length?(++i,n.next?t.head=n.next:t.head=t.tail=null):(t.head=n,n.data=o.slice(a));break}++i}return t.length-=i,r}(e,t);return n}(e,t.buffer,t.decoder),r);var r}function O(e){var t=e._readableState;if(t.length>0)throw new Error('"endReadable()" called on non-empty stream');t.endEmitted||(t.ended=!0,i.nextTick(M,t,e))}function M(e,t){e.endEmitted||0!==e.length||(e.endEmitted=!0,t.readable=!1,t.emit("end"))}function U(e,t){for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}v.prototype.read=function(e){d("read",e),e=parseInt(e,10);var t=this._readableState,r=e;if(0!==e&&(t.emittedReadable=!1),0===e&&t.needReadable&&(t.length>=t.highWaterMark||t.ended))return d("read: emitReadable",t.length,t.ended),0===t.length&&t.ended?O(this):T(this),null;if(0===(e=x(e,t))&&t.ended)return 0===t.length&&O(this),null;var n,i=t.needReadable;return d("need readable",i),(0===t.length||t.length-e<t.highWaterMark)&&d("length less than watermark",i=!0),t.ended||t.reading?d("reading or ended",i=!1):i&&(d("do read"),t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=x(r,t))),null===(n=e>0?L(e,t):null)?(t.needReadable=!0,e=0):t.length-=e,0===t.length&&(t.ended||(t.needReadable=!0),r!==e&&t.ended&&O(this)),null!==n&&this.emit("data",n),n},v.prototype._read=function(e){this.emit("error",new Error("_read() is not implemented"))},v.prototype.pipe=function(e,t){var n=this,o=this._readableState;switch(o.pipesCount){case 0:o.pipes=e;break;case 1:o.pipes=[o.pipes,e];break;default:o.pipes.push(e)}o.pipesCount+=1,d("pipe count=%d opts=%j",o.pipesCount,t);var u=(!t||!1!==t.end)&&e!==r.stdout&&e!==r.stderr?f:v;function l(t,r){d("onunpipe"),t===n&&r&&!1===r.hasUnpiped&&(r.hasUnpiped=!0,d("cleanup"),e.removeListener("close",b),e.removeListener("finish",m),e.removeListener("drain",c),e.removeListener("error",y),e.removeListener("unpipe",l),n.removeListener("end",f),n.removeListener("end",v),n.removeListener("data",g),h=!0,!o.awaitDrain||e._writableState&&!e._writableState.needDrain||c())}function f(){d("onend"),e.end()}o.endEmitted?i.nextTick(u):n.once("end",u),e.on("unpipe",l);var c=function(e){return function(){var t=e._readableState;d("pipeOnDrain",t.awaitDrain),t.awaitDrain&&t.awaitDrain--,0===t.awaitDrain&&s(e,"data")&&(t.flowing=!0,D(e))}}(n);e.on("drain",c);var h=!1;var p=!1;function g(t){d("ondata"),p=!1,!1!==e.write(t)||p||((1===o.pipesCount&&o.pipes===e||o.pipesCount>1&&-1!==U(o.pipes,e))&&!h&&(d("false write response, pause",o.awaitDrain),o.awaitDrain++,p=!0),n.pause())}function y(t){d("onerror",t),v(),e.removeListener("error",y),0===s(e,"error")&&e.emit("error",t)}function b(){e.removeListener("finish",m),v()}function m(){d("onfinish"),e.removeListener("close",b),v()}function v(){d("unpipe"),n.unpipe(e)}return n.on("data",g),function(e,t,r){if("function"==typeof e.prependListener)return e.prependListener(t,r);e._events&&e._events[t]?a(e._events[t])?e._events[t].unshift(r):e._events[t]=[r,e._events[t]]:e.on(t,r)}(e,"error",y),e.once("close",b),e.once("finish",m),e.emit("pipe",n),o.flowing||(d("pipe resume"),n.resume()),e},v.prototype.unpipe=function(e){var t=this._readableState,r={hasUnpiped:!1};if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes?this:(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this,r),this);if(!e){var n=t.pipes,i=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var o=0;o<i;o++)n[o].emit("unpipe",this,{hasUnpiped:!1});return this}var a=U(t.pipes,e);return-1===a?this:(t.pipes.splice(a,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this,r),this)},v.prototype.on=function(e,t){var r=u.prototype.on.call(this,e,t);if("data"===e)!1!==this._readableState.flowing&&this.resume();else if("readable"===e){var n=this._readableState;n.endEmitted||n.readableListening||(n.readableListening=n.needReadable=!0,n.emittedReadable=!1,n.reading?n.length&&T(this):i.nextTick(I,this))}return r},v.prototype.addListener=v.prototype.on,v.prototype.resume=function(){var e=this._readableState;return e.flowing||(d("resume"),e.flowing=!0,function(e,t){t.resumeScheduled||(t.resumeScheduled=!0,i.nextTick(A,e,t))}(this,e)),this},v.prototype.pause=function(){return d("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(d("pause"),this._readableState.flowing=!1,this.emit("pause")),this},v.prototype.wrap=function(e){var t=this,r=this._readableState,n=!1;for(var i in e.on("end",function(){if(d("wrapped end"),r.decoder&&!r.ended){var e=r.decoder.end();e&&e.length&&t.push(e)}t.push(null)}),e.on("data",function(i){(d("wrapped data"),r.decoder&&(i=r.decoder.write(i)),!r.objectMode||null!==i&&void 0!==i)&&((r.objectMode||i&&i.length)&&(t.push(i)||(n=!0,e.pause())))}),e)void 0===this[i]&&"function"==typeof e[i]&&(this[i]=function(t){return function(){return e[t].apply(e,arguments)}}(i));for(var o=0;o<b.length;o++)e.on(b[o],this.emit.bind(this,b[o]));return this._read=function(t){d("wrapped _read",t),n&&(n=!1,e.resume())},this},Object.defineProperty(v.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),v._fromList=L}).call(this)}).call(this,e("_process"),"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./_stream_duplex":99,"./internal/streams/BufferList":104,"./internal/streams/destroy":105,"./internal/streams/stream":106,_process:97,"core-util-is":29,events:56,inherits:67,isarray:69,"process-nextick-args":96,"safe-buffer":107,"string_decoder/":108,util:19}],102:[function(e,t,r){"use strict";t.exports=o;var n=e("./_stream_duplex"),i=Object.create(e("core-util-is"));function o(e){if(!(this instanceof o))return new o(e);n.call(this,e),this._transformState={afterTransform:function(e,t){var r=this._transformState;r.transforming=!1;var n=r.writecb;if(!n)return this.emit("error",new Error("write callback called multiple times"));r.writechunk=null,r.writecb=null,null!=t&&this.push(t),n(e);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,e&&("function"==typeof e.transform&&(this._transform=e.transform),"function"==typeof e.flush&&(this._flush=e.flush)),this.on("prefinish",a)}function a(){var e=this;"function"==typeof this._flush?this._flush(function(t,r){s(e,t,r)}):s(this,null,null)}function s(e,t,r){if(t)return e.emit("error",t);if(null!=r&&e.push(r),e._writableState.length)throw new Error("Calling transform done when ws.length != 0");if(e._transformState.transforming)throw new Error("Calling transform done when still transforming");return e.push(null)}i.inherits=e("inherits"),i.inherits(o,n),o.prototype.push=function(e,t){return this._transformState.needTransform=!1,n.prototype.push.call(this,e,t)},o.prototype._transform=function(e,t,r){throw new Error("_transform() is not implemented")},o.prototype._write=function(e,t,r){var n=this._transformState;if(n.writecb=r,n.writechunk=e,n.writeencoding=t,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},o.prototype._read=function(e){var t=this._transformState;null!==t.writechunk&&t.writecb&&!t.transforming?(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform)):t.needTransform=!0},o.prototype._destroy=function(e,t){var r=this;n.prototype._destroy.call(this,e,function(e){t(e),r.emit("close")})}},{"./_stream_duplex":99,"core-util-is":29,inherits:67}],103:[function(e,t,r){(function(r,n,i){(function(){"use strict";var o=e("process-nextick-args");function a(e){var t=this;this.next=null,this.entry=null,this.finish=function(){!function(e,t,r){var n=e.entry;e.entry=null;for(;n;){var i=n.callback;t.pendingcb--,i(r),n=n.next}t.corkedRequestsFree.next=e}(t,e)}}t.exports=m;var s,u=!r.browser&&["v0.10","v0.9."].indexOf(r.version.slice(0,5))>-1?i:o.nextTick;m.WritableState=b;var l=Object.create(e("core-util-is"));l.inherits=e("inherits");var f={deprecate:e("util-deprecate")},c=e("./internal/streams/stream"),h=e("safe-buffer").Buffer,d=(void 0!==n?n:"undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).Uint8Array||function(){};var p,g=e("./internal/streams/destroy");function y(){}function b(t,r){s=s||e("./_stream_duplex"),t=t||{};var n=r instanceof s;this.objectMode=!!t.objectMode,n&&(this.objectMode=this.objectMode||!!t.writableObjectMode);var i=t.highWaterMark,l=t.writableHighWaterMark,f=this.objectMode?16:16384;this.highWaterMark=i||0===i?i:n&&(l||0===l)?l:f,this.highWaterMark=Math.floor(this.highWaterMark),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var c=!1===t.decodeStrings;this.decodeStrings=!c,this.defaultEncoding=t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){!function(e,t){var r=e._writableState,n=r.sync,i=r.writecb;if(function(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}(r),t)!function(e,t,r,n,i){--t.pendingcb,r?(o.nextTick(i,n),o.nextTick(T,e,t),e._writableState.errorEmitted=!0,e.emit("error",n)):(i(n),e._writableState.errorEmitted=!0,e.emit("error",n),T(e,t))}(e,r,n,t,i);else{var a=E(r);a||r.corked||r.bufferProcessing||!r.bufferedRequest||_(e,r),n?u(w,e,r,a,i):w(e,r,a,i)}}(r,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new a(this)}function m(t){if(s=s||e("./_stream_duplex"),!(p.call(m,this)||this instanceof s))return new m(t);this._writableState=new b(t,this),this.writable=!0,t&&("function"==typeof t.write&&(this._write=t.write),"function"==typeof t.writev&&(this._writev=t.writev),"function"==typeof t.destroy&&(this._destroy=t.destroy),"function"==typeof t.final&&(this._final=t.final)),c.call(this)}function v(e,t,r,n,i,o,a){t.writelen=n,t.writecb=a,t.writing=!0,t.sync=!0,r?e._writev(i,t.onwrite):e._write(i,o,t.onwrite),t.sync=!1}function w(e,t,r,n){r||function(e,t){0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}(e,t),t.pendingcb--,n(),T(e,t)}function _(e,t){t.bufferProcessing=!0;var r=t.bufferedRequest;if(e._writev&&r&&r.next){var n=t.bufferedRequestCount,i=new Array(n),o=t.corkedRequestsFree;o.entry=r;for(var s=0,u=!0;r;)i[s]=r,r.isBuf||(u=!1),r=r.next,s+=1;i.allBuffers=u,v(e,t,!0,t.length,i,"",o.finish),t.pendingcb++,t.lastBufferedRequest=null,o.next?(t.corkedRequestsFree=o.next,o.next=null):t.corkedRequestsFree=new a(t),t.bufferedRequestCount=0}else{for(;r;){var l=r.chunk,f=r.encoding,c=r.callback;if(v(e,t,!1,t.objectMode?1:l.length,l,f,c),r=r.next,t.bufferedRequestCount--,t.writing)break}null===r&&(t.lastBufferedRequest=null)}t.bufferedRequest=r,t.bufferProcessing=!1}function E(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function x(e,t){e._final(function(r){t.pendingcb--,r&&e.emit("error",r),t.prefinished=!0,e.emit("prefinish"),T(e,t)})}function T(e,t){var r=E(t);return r&&(!function(e,t){t.prefinished||t.finalCalled||("function"==typeof e._final?(t.pendingcb++,t.finalCalled=!0,o.nextTick(x,e,t)):(t.prefinished=!0,e.emit("prefinish")))}(e,t),0===t.pendingcb&&(t.finished=!0,e.emit("finish"))),r}l.inherits(m,c),b.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t},function(){try{Object.defineProperty(b.prototype,"buffer",{get:f.deprecate(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(e){}}(),"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?(p=Function.prototype[Symbol.hasInstance],Object.defineProperty(m,Symbol.hasInstance,{value:function(e){return!!p.call(this,e)||this===m&&(e&&e._writableState instanceof b)}})):p=function(e){return e instanceof this},m.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))},m.prototype.write=function(e,t,r){var n,i=this._writableState,a=!1,s=!i.objectMode&&(n=e,h.isBuffer(n)||n instanceof d);return s&&!h.isBuffer(e)&&(e=function(e){return h.from(e)}(e)),"function"==typeof t&&(r=t,t=null),s?t="buffer":t||(t=i.defaultEncoding),"function"!=typeof r&&(r=y),i.ended?function(e,t){var r=new Error("write after end");e.emit("error",r),o.nextTick(t,r)}(this,r):(s||function(e,t,r,n){var i=!0,a=!1;return null===r?a=new TypeError("May not write null values to stream"):"string"==typeof r||void 0===r||t.objectMode||(a=new TypeError("Invalid non-string/buffer chunk")),a&&(e.emit("error",a),o.nextTick(n,a),i=!1),i}(this,i,e,r))&&(i.pendingcb++,a=function(e,t,r,n,i,o){if(!r){var a=function(e,t,r){e.objectMode||!1===e.decodeStrings||"string"!=typeof t||(t=h.from(t,r));return t}(t,n,i);n!==a&&(r=!0,i="buffer",n=a)}var s=t.objectMode?1:n.length;t.length+=s;var u=t.length<t.highWaterMark;u||(t.needDrain=!0);if(t.writing||t.corked){var l=t.lastBufferedRequest;t.lastBufferedRequest={chunk:n,encoding:i,isBuf:r,callback:o,next:null},l?l.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else v(e,t,!1,s,n,i,o);return u}(this,i,s,e,t,r)),a},m.prototype.cork=function(){this._writableState.corked++},m.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.bufferProcessing||!e.bufferedRequest||_(this,e))},m.prototype.setDefaultEncoding=function(e){if("string"==typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+e);return this._writableState.defaultEncoding=e,this},Object.defineProperty(m.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),m.prototype._write=function(e,t,r){r(new Error("_write() is not implemented"))},m.prototype._writev=null,m.prototype.end=function(e,t,r){var n=this._writableState;"function"==typeof e?(r=e,e=null,t=null):"function"==typeof t&&(r=t,t=null),null!==e&&void 0!==e&&this.write(e,t),n.corked&&(n.corked=1,this.uncork()),n.ending||function(e,t,r){t.ending=!0,T(e,t),r&&(t.finished?o.nextTick(r):e.once("finish",r));t.ended=!0,e.writable=!1}(this,n,r)},Object.defineProperty(m.prototype,"destroyed",{get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(e){this._writableState&&(this._writableState.destroyed=e)}}),m.prototype.destroy=g.destroy,m.prototype._undestroy=g.undestroy,m.prototype._destroy=function(e,t){this.end(),t(e)}}).call(this)}).call(this,e("_process"),"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{},e("timers").setImmediate)},{"./_stream_duplex":99,"./internal/streams/destroy":105,"./internal/streams/stream":106,_process:97,"core-util-is":29,inherits:67,"process-nextick-args":96,"safe-buffer":107,timers:118,"util-deprecate":119}],104:[function(e,t,r){"use strict";var n=e("safe-buffer").Buffer,i=e("util");t.exports=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.head=null,this.tail=null,this.length=0}return e.prototype.push=function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length},e.prototype.unshift=function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length},e.prototype.shift=function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}},e.prototype.clear=function(){this.head=this.tail=null,this.length=0},e.prototype.join=function(e){if(0===this.length)return"";for(var t=this.head,r=""+t.data;t=t.next;)r+=e+t.data;return r},e.prototype.concat=function(e){if(0===this.length)return n.alloc(0);for(var t,r,i,o=n.allocUnsafe(e>>>0),a=this.head,s=0;a;)t=a.data,r=o,i=s,t.copy(r,i),s+=a.data.length,a=a.next;return o},e}(),i&&i.inspect&&i.inspect.custom&&(t.exports.prototype[i.inspect.custom]=function(){var e=i.inspect({length:this.length});return this.constructor.name+" "+e})},{"safe-buffer":107,util:19}],105:[function(e,t,r){"use strict";var n=e("process-nextick-args");function i(e,t){e.emit("error",t)}t.exports={destroy:function(e,t){var r=this,o=this._readableState&&this._readableState.destroyed,a=this._writableState&&this._writableState.destroyed;return o||a?(t?t(e):e&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,n.nextTick(i,this,e)):n.nextTick(i,this,e)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(e||null,function(e){!t&&e?r._writableState?r._writableState.errorEmitted||(r._writableState.errorEmitted=!0,n.nextTick(i,r,e)):n.nextTick(i,r,e):t&&t(e)}),this)},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}}},{"process-nextick-args":96}],106:[function(e,t,r){t.exports=e("events").EventEmitter},{events:56}],107:[function(e,t,r){var n=e("buffer"),i=n.Buffer;function o(e,t){for(var r in e)t[r]=e[r]}function a(e,t,r){return i(e,t,r)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?t.exports=n:(o(n,r),r.Buffer=a),o(i,a),a.from=function(e,t,r){if("number"==typeof e)throw new TypeError("Argument must not be a number");return i(e,t,r)},a.alloc=function(e,t,r){if("number"!=typeof e)throw new TypeError("Argument must be a number");var n=i(e);return void 0!==t?"string"==typeof r?n.fill(t,r):n.fill(t):n.fill(0),n},a.allocUnsafe=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return i(e)},a.allocUnsafeSlow=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return n.SlowBuffer(e)}},{buffer:23}],108:[function(e,t,r){"use strict";var n=e("safe-buffer").Buffer,i=n.isEncoding||function(e){switch((e=""+e)&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function o(e){var t;switch(this.encoding=function(e){var t=function(e){if(!e)return"utf8";for(var t;;)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}(e);if("string"!=typeof t&&(n.isEncoding===i||!i(e)))throw new Error("Unknown encoding: "+e);return t||e}(e),this.encoding){case"utf16le":this.text=u,this.end=l,t=4;break;case"utf8":this.fillLast=s,t=4;break;case"base64":this.text=f,this.end=c,t=3;break;default:return this.write=h,void(this.end=d)}this.lastNeed=0,this.lastTotal=0,this.lastChar=n.allocUnsafe(t)}function a(e){return e<=127?0:e>>5==6?2:e>>4==14?3:e>>3==30?4:e>>6==2?-1:-2}function s(e){var t=this.lastTotal-this.lastNeed,r=function(e,t,r){if(128!=(192&t[0]))return e.lastNeed=0,"�";if(e.lastNeed>1&&t.length>1){if(128!=(192&t[1]))return e.lastNeed=1,"�";if(e.lastNeed>2&&t.length>2&&128!=(192&t[2]))return e.lastNeed=2,"�"}}(this,e);return void 0!==r?r:this.lastNeed<=e.length?(e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(e.copy(this.lastChar,t,0,e.length),void(this.lastNeed-=e.length))}function u(e,t){if((e.length-t)%2==0){var r=e.toString("utf16le",t);if(r){var n=r.charCodeAt(r.length-1);if(n>=55296&&n<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function l(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,r)}return t}function f(e,t){var r=(e.length-t)%3;return 0===r?e.toString("base64",t):(this.lastNeed=3-r,this.lastTotal=3,1===r?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-r))}function c(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function h(e){return e.toString(this.encoding)}function d(e){return e&&e.length?this.write(e):""}r.StringDecoder=o,o.prototype.write=function(e){if(0===e.length)return"";var t,r;if(this.lastNeed){if(void 0===(t=this.fillLast(e)))return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<e.length?t?t+this.text(e,r):this.text(e,r):t||""},o.prototype.end=function(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+"�":t},o.prototype.text=function(e,t){var r=function(e,t,r){var n=t.length-1;if(n<r)return 0;var i=a(t[n]);if(i>=0)return i>0&&(e.lastNeed=i-1),i;if(--n<r||-2===i)return 0;if((i=a(t[n]))>=0)return i>0&&(e.lastNeed=i-2),i;if(--n<r||-2===i)return 0;if((i=a(t[n]))>=0)return i>0&&(2===i?i=0:e.lastNeed=i-3),i;return 0}(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=r;var n=e.length-(r-this.lastNeed);return e.copy(this.lastChar,0,n),e.toString("utf8",t,n)},o.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length}},{"safe-buffer":107}],109:[function(e,t,r){t.exports=e("./readable").PassThrough},{"./readable":110}],110:[function(e,t,r){(r=t.exports=e("./lib/_stream_readable.js")).Stream=r,r.Readable=r,r.Writable=e("./lib/_stream_writable.js"),r.Duplex=e("./lib/_stream_duplex.js"),r.Transform=e("./lib/_stream_transform.js"),r.PassThrough=e("./lib/_stream_passthrough.js")},{"./lib/_stream_duplex.js":99,"./lib/_stream_passthrough.js":100,"./lib/_stream_readable.js":101,"./lib/_stream_transform.js":102,"./lib/_stream_writable.js":103}],111:[function(e,t,r){t.exports=e("./readable").Transform},{"./readable":110}],112:[function(e,t,r){t.exports=e("./lib/_stream_writable.js")},{"./lib/_stream_writable.js":103}],113:[function(e,t,r){"use strict";var n=e("get-intrinsic"),i=e("define-data-property"),o=e("has-property-descriptors")(),a=e("gopd"),s=n("%TypeError%"),u=n("%Math.floor%");t.exports=function(e,t){if("function"!=typeof e)throw new s("`fn` is not a function");if("number"!=typeof t||t<0||t>4294967295||u(t)!==t)throw new s("`length` must be a positive 32-bit integer");var r=arguments.length>2&&!!arguments[2],n=!0,l=!0;if("length"in e&&a){var f=a(e,"length");f&&!f.configurable&&(n=!1),f&&!f.writable&&(l=!1)}return(n||l||!r)&&(o?i(e,"length",t,!0,!0):i(e,"length",t)),e}},{"define-data-property":55,"get-intrinsic":59,gopd:60,"has-property-descriptors":61}],114:[function(e,t,r){t.exports=i;var n=e("events").EventEmitter;function i(){n.call(this)}e("inherits")(i,n),i.Readable=e("readable-stream/readable.js"),i.Writable=e("readable-stream/writable.js"),i.Duplex=e("readable-stream/duplex.js"),i.Transform=e("readable-stream/transform.js"),i.PassThrough=e("readable-stream/passthrough.js"),i.Stream=i,i.prototype.pipe=function(e,t){var r=this;function i(t){e.writable&&!1===e.write(t)&&r.pause&&r.pause()}function o(){r.readable&&r.resume&&r.resume()}r.on("data",i),e.on("drain",o),e._isStdio||t&&!1===t.end||(r.on("end",s),r.on("close",u));var a=!1;function s(){a||(a=!0,e.end())}function u(){a||(a=!0,"function"==typeof e.destroy&&e.destroy())}function l(e){if(f(),0===n.listenerCount(this,"error"))throw e}function f(){r.removeListener("data",i),e.removeListener("drain",o),r.removeListener("end",s),r.removeListener("close",u),r.removeListener("error",l),e.removeListener("error",l),r.removeListener("end",f),r.removeListener("close",f),e.removeListener("close",f)}return r.on("error",l),e.on("error",l),r.on("end",f),r.on("close",f),e.on("close",f),e.emit("pipe",r),e}},{events:56,inherits:67,"readable-stream/duplex.js":98,"readable-stream/passthrough.js":109,"readable-stream/readable.js":110,"readable-stream/transform.js":111,"readable-stream/writable.js":112}],115:[function(e,t,r){t.exports=e("stream-to").buffer},{"stream-to":116}],116:[function(e,t,r){(function(e){(function(){function t(e,t){var r=[];function n(e){r.push(e)}function i(){t(null,r),o()}function o(){r=null,e.removeListener("data",n),e.removeListener("end",i),e.removeListener("error",t),e.removeListener("error",o),e.removeListener("close",o)}return e.on("data",n),e.once("end",i),e.once("error",t),e.once("error",o),e.once("close",o),e}r.array=t,r.buffer=function(r,n){return t(r,function(t,r){t||!r?n(t):n(null,e.concat(r))}),r}}).call(this)}).call(this,e("buffer").Buffer)},{buffer:23}],117:[function(e,t,r){(function(r){(function(){"use strict";var n=e("util"),i=e("stream");t.exports.createReadStream=function(e,t){return new o(e,t)};var o=function(e,t){e instanceof r||"string"==typeof e?(t=t||{},i.Readable.call(this,{highWaterMark:t.highWaterMark,encoding:t.encoding})):i.Readable.call(this,{objectMode:!0}),this._object=e};n.inherits(o,i.Readable),o.prototype._read=function(){this.push(this._object),this._object=null}}).call(this)}).call(this,e("buffer").Buffer)},{buffer:23,stream:114,util:122}],118:[function(e,t,r){(function(t,n){(function(){var i=e("process/browser.js").nextTick,o=Function.prototype.apply,a=Array.prototype.slice,s={},u=0;function l(e,t){this._id=e,this._clearFn=t}r.setTimeout=function(){return new l(o.call(setTimeout,window,arguments),clearTimeout)},r.setInterval=function(){return new l(o.call(setInterval,window,arguments),clearInterval)},r.clearTimeout=r.clearInterval=function(e){e.close()},l.prototype.unref=l.prototype.ref=function(){},l.prototype.close=function(){this._clearFn.call(window,this._id)},r.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},r.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},r._unrefActive=r.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout(function(){e._onTimeout&&e._onTimeout()},t))},r.setImmediate="function"==typeof t?t:function(e){var t=u++,n=!(arguments.length<2)&&a.call(arguments,1);return s[t]=!0,i(function(){s[t]&&(n?e.apply(null,n):e.call(null),r.clearImmediate(t))}),t},r.clearImmediate="function"==typeof n?n:function(e){delete s[e]}}).call(this)}).call(this,e("timers").setImmediate,e("timers").clearImmediate)},{"process/browser.js":97,timers:118}],119:[function(e,t,r){(function(e){(function(){function r(t){try{if(!e.localStorage)return!1}catch(e){return!1}var r=e.localStorage[t];return null!=r&&"true"===String(r).toLowerCase()}t.exports=function(e,t){if(r("noDeprecation"))return e;var n=!1;return function(){if(!n){if(r("throwDeprecation"))throw new Error(t);r("traceDeprecation")?console.trace(t):console.warn(t),n=!0}return e.apply(this,arguments)}}}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],120:[function(e,t,r){"function"==typeof Object.create?t.exports=function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:t.exports=function(e,t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}},{}],121:[function(e,t,r){t.exports=function(e){return e&&"object"==typeof e&&"function"==typeof e.copy&&"function"==typeof e.fill&&"function"==typeof e.readUInt8}},{}],122:[function(e,t,r){(function(t,n){(function(){var i=/%[sdj%]/g;r.format=function(e){if(!b(e)){for(var t=[],r=0;r<arguments.length;r++)t.push(s(arguments[r]));return t.join(" ")}r=1;for(var n=arguments,o=n.length,a=String(e).replace(i,function(e){if("%%"===e)return"%";if(r>=o)return e;switch(e){case"%s":return String(n[r++]);case"%d":return Number(n[r++]);case"%j":try{return JSON.stringify(n[r++])}catch(e){return"[Circular]"}default:return e}}),u=n[r];r<o;u=n[++r])g(u)||!w(u)?a+=" "+u:a+=" "+s(u);return a},r.deprecate=function(e,i){if(m(n.process))return function(){return r.deprecate(e,i).apply(this,arguments)};if(!0===t.noDeprecation)return e;var o=!1;return function(){if(!o){if(t.throwDeprecation)throw new Error(i);t.traceDeprecation?console.trace(i):console.error(i),o=!0}return e.apply(this,arguments)}};var o,a={};function s(e,t){var n={seen:[],stylize:l};return arguments.length>=3&&(n.depth=arguments[2]),arguments.length>=4&&(n.colors=arguments[3]),p(t)?n.showHidden=t:t&&r._extend(n,t),m(n.showHidden)&&(n.showHidden=!1),m(n.depth)&&(n.depth=2),m(n.colors)&&(n.colors=!1),m(n.customInspect)&&(n.customInspect=!0),n.colors&&(n.stylize=u),f(n,e,n.depth)}function u(e,t){var r=s.styles[t];return r?"["+s.colors[r][0]+"m"+e+"["+s.colors[r][1]+"m":e}function l(e,t){return e}function f(e,t,n){if(e.customInspect&&t&&x(t.inspect)&&t.inspect!==r.inspect&&(!t.constructor||t.constructor.prototype!==t)){var i=t.inspect(n,e);return b(i)||(i=f(e,i,n)),i}var o=function(e,t){if(m(t))return e.stylize("undefined","undefined");if(b(t)){var r="'"+JSON.stringify(t).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return e.stylize(r,"string")}if(y(t))return e.stylize(""+t,"number");if(p(t))return e.stylize(""+t,"boolean");if(g(t))return e.stylize("null","null")}(e,t);if(o)return o;var a=Object.keys(t),s=function(e){var t={};return e.forEach(function(e,r){t[e]=!0}),t}(a);if(e.showHidden&&(a=Object.getOwnPropertyNames(t)),E(t)&&(a.indexOf("message")>=0||a.indexOf("description")>=0))return c(t);if(0===a.length){if(x(t)){var u=t.name?": "+t.name:"";return e.stylize("[Function"+u+"]","special")}if(v(t))return e.stylize(RegExp.prototype.toString.call(t),"regexp");if(_(t))return e.stylize(Date.prototype.toString.call(t),"date");if(E(t))return c(t)}var l,w="",T=!1,k=["{","}"];(d(t)&&(T=!0,k=["[","]"]),x(t))&&(w=" [Function"+(t.name?": "+t.name:"")+"]");return v(t)&&(w=" "+RegExp.prototype.toString.call(t)),_(t)&&(w=" "+Date.prototype.toUTCString.call(t)),E(t)&&(w=" "+c(t)),0!==a.length||T&&0!=t.length?n<0?v(t)?e.stylize(RegExp.prototype.toString.call(t),"regexp"):e.stylize("[Object]","special"):(e.seen.push(t),l=T?function(e,t,r,n,i){for(var o=[],a=0,s=t.length;a<s;++a)S(t,String(a))?o.push(h(e,t,r,n,String(a),!0)):o.push("");return i.forEach(function(i){i.match(/^\d+$/)||o.push(h(e,t,r,n,i,!0))}),o}(e,t,n,s,a):a.map(function(r){return h(e,t,n,s,r,T)}),e.seen.pop(),function(e,t,r){if(e.reduce(function(e,t){return 0,t.indexOf("\n")>=0&&0,e+t.replace(/\u001b\[\d\d?m/g,"").length+1},0)>60)return r[0]+(""===t?"":t+"\n ")+" "+e.join(",\n  ")+" "+r[1];return r[0]+t+" "+e.join(", ")+" "+r[1]}(l,w,k)):k[0]+w+k[1]}function c(e){return"["+Error.prototype.toString.call(e)+"]"}function h(e,t,r,n,i,o){var a,s,u;if((u=Object.getOwnPropertyDescriptor(t,i)||{value:t[i]}).get?s=u.set?e.stylize("[Getter/Setter]","special"):e.stylize("[Getter]","special"):u.set&&(s=e.stylize("[Setter]","special")),S(n,i)||(a="["+i+"]"),s||(e.seen.indexOf(u.value)<0?(s=g(r)?f(e,u.value,null):f(e,u.value,r-1)).indexOf("\n")>-1&&(s=o?s.split("\n").map(function(e){return"  "+e}).join("\n").substr(2):"\n"+s.split("\n").map(function(e){return"   "+e}).join("\n")):s=e.stylize("[Circular]","special")),m(a)){if(o&&i.match(/^\d+$/))return s;(a=JSON.stringify(""+i)).match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(a=a.substr(1,a.length-2),a=e.stylize(a,"name")):(a=a.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),a=e.stylize(a,"string"))}return a+": "+s}function d(e){return Array.isArray(e)}function p(e){return"boolean"==typeof e}function g(e){return null===e}function y(e){return"number"==typeof e}function b(e){return"string"==typeof e}function m(e){return void 0===e}function v(e){return w(e)&&"[object RegExp]"===T(e)}function w(e){return"object"==typeof e&&null!==e}function _(e){return w(e)&&"[object Date]"===T(e)}function E(e){return w(e)&&("[object Error]"===T(e)||e instanceof Error)}function x(e){return"function"==typeof e}function T(e){return Object.prototype.toString.call(e)}function k(e){return e<10?"0"+e.toString(10):e.toString(10)}r.debuglog=function(e){if(m(o)&&(o=t.env.NODE_DEBUG||""),e=e.toUpperCase(),!a[e])if(new RegExp("\\b"+e+"\\b","i").test(o)){var n=t.pid;a[e]=function(){var t=r.format.apply(r,arguments);console.error("%s %d: %s",e,n,t)}}else a[e]=function(){};return a[e]},r.inspect=s,s.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},s.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},r.isArray=d,r.isBoolean=p,r.isNull=g,r.isNullOrUndefined=function(e){return null==e},r.isNumber=y,r.isString=b,r.isSymbol=function(e){return"symbol"==typeof e},r.isUndefined=m,r.isRegExp=v,r.isObject=w,r.isDate=_,r.isError=E,r.isFunction=x,r.isPrimitive=function(e){return null===e||"boolean"==typeof e||"number"==typeof e||"string"==typeof e||"symbol"==typeof e||void 0===e},r.isBuffer=e("./support/isBuffer");var N=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function S(e,t){return Object.prototype.hasOwnProperty.call(e,t)}r.log=function(){var e,t;console.log("%s - %s",(e=new Date,t=[k(e.getHours()),k(e.getMinutes()),k(e.getSeconds())].join(":"),[e.getDate(),N[e.getMonth()],t].join(" ")),r.format.apply(r,arguments))},r.inherits=e("inherits"),r._extend=function(e,t){if(!t||!w(t))return e;for(var r=Object.keys(t),n=r.length;n--;)e[r[n]]=t[r[n]];return e}}).call(this)}).call(this,e("_process"),"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./support/isBuffer":121,_process:97,inherits:120}],123:[function(e,t,r){(function(){t.exports={Disconnected:1,Preceding:2,Following:4,Contains:8,ContainedBy:16,ImplementationSpecific:32}}).call(this)},{}],124:[function(e,t,r){(function(){t.exports={Element:1,Attribute:2,Text:3,CData:4,EntityReference:5,EntityDeclaration:6,ProcessingInstruction:7,Comment:8,Document:9,DocType:10,DocumentFragment:11,NotationDeclaration:12,Declaration:201,Raw:202,AttributeDeclaration:203,ElementDeclaration:204,Dummy:205}}).call(this)},{}],125:[function(e,t,r){(function(){var e,r,n,i,o,a,s,u={}.hasOwnProperty;e=function(e,...t){var r,n,i,a;if(o(Object.assign))Object.assign.apply(null,arguments);else for(r=0,i=t.length;r<i;r++)if(null!=(a=t[r]))for(n in a)u.call(a,n)&&(e[n]=a[n]);return e},o=function(e){return!!e&&"[object Function]"===Object.prototype.toString.call(e)},a=function(e){var t;return!!e&&("function"==(t=typeof e)||"object"===t)},n=function(e){return o(Array.isArray)?Array.isArray(e):"[object Array]"===Object.prototype.toString.call(e)},i=function(e){var t;if(n(e))return!e.length;for(t in e)if(u.call(e,t))return!1;return!0},s=function(e){var t,r;return a(e)&&(r=Object.getPrototypeOf(e))&&(t=r.constructor)&&"function"==typeof t&&t instanceof t&&Function.prototype.toString.call(t)===Function.prototype.toString.call(Object)},r=function(e){return o(e.valueOf)?e.valueOf():e},t.exports.assign=e,t.exports.isFunction=o,t.exports.isObject=a,t.exports.isArray=n,t.exports.isEmpty=i,t.exports.isPlainObject=s,t.exports.getValue=r}).call(this)},{}],126:[function(e,t,r){(function(){t.exports={None:0,OpenTag:1,InsideTag:2,CloseTag:3}}).call(this)},{}],127:[function(e,t,r){(function(){var r;r=e("./NodeType"),e("./XMLNode"),t.exports=function(){class e{constructor(e,t,n){if(this.parent=e,this.parent&&(this.options=this.parent.options,this.stringify=this.parent.stringify),null==t)throw new Error("Missing attribute name. "+this.debugInfo(t));this.name=this.stringify.name(t),this.value=this.stringify.attValue(n),this.type=r.Attribute,this.isId=!1,this.schemaTypeInfo=null}clone(){return Object.create(this)}toString(e){return this.options.writer.attribute(this,this.options.writer.filterOptions(e))}debugInfo(e){return null==(e=e||this.name)?"parent: <"+this.parent.name+">":"attribute: {"+e+"}, parent: <"+this.parent.name+">"}isEqualNode(e){return e.namespaceURI===this.namespaceURI&&(e.prefix===this.prefix&&(e.localName===this.localName&&e.value===this.value))}}return Object.defineProperty(e.prototype,"nodeType",{get:function(){return this.type}}),Object.defineProperty(e.prototype,"ownerElement",{get:function(){return this.parent}}),Object.defineProperty(e.prototype,"textContent",{get:function(){return this.value},set:function(e){return this.value=e||""}}),Object.defineProperty(e.prototype,"namespaceURI",{get:function(){return""}}),Object.defineProperty(e.prototype,"prefix",{get:function(){return""}}),Object.defineProperty(e.prototype,"localName",{get:function(){return this.name}}),Object.defineProperty(e.prototype,"specified",{get:function(){return!0}}),e}.call(this)}).call(this)},{"./NodeType":124,"./XMLNode":146}],128:[function(e,t,r){(function(){var r,n;r=e("./NodeType"),n=e("./XMLCharacterData"),t.exports=class extends n{constructor(e,t){if(super(e),null==t)throw new Error("Missing CDATA text. "+this.debugInfo());this.name="#cdata-section",this.type=r.CData,this.value=this.stringify.cdata(t)}clone(){return Object.create(this)}toString(e){return this.options.writer.cdata(this,this.options.writer.filterOptions(e))}}}).call(this)},{"./NodeType":124,"./XMLCharacterData":129}],129:[function(e,t,r){(function(){var r;r=e("./XMLNode"),t.exports=function(){class e extends r{constructor(e){super(e),this.value=""}clone(){return Object.create(this)}substringData(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())}appendData(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}insertData(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())}deleteData(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())}replaceData(e,t,r){throw new Error("This DOM method is not implemented."+this.debugInfo())}isEqualNode(e){return!!super.isEqualNode(e)&&e.data===this.data}}return Object.defineProperty(e.prototype,"data",{get:function(){return this.value},set:function(e){return this.value=e||""}}),Object.defineProperty(e.prototype,"length",{get:function(){return this.value.length}}),Object.defineProperty(e.prototype,"textContent",{get:function(){return this.value},set:function(e){return this.value=e||""}}),e}.call(this)}).call(this)},{"./XMLNode":146}],130:[function(e,t,r){(function(){var r,n;r=e("./NodeType"),n=e("./XMLCharacterData"),t.exports=class extends n{constructor(e,t){if(super(e),null==t)throw new Error("Missing comment text. "+this.debugInfo());this.name="#comment",this.type=r.Comment,this.value=this.stringify.comment(t)}clone(){return Object.create(this)}toString(e){return this.options.writer.comment(this,this.options.writer.filterOptions(e))}}}).call(this)},{"./NodeType":124,"./XMLCharacterData":129}],131:[function(e,t,r){(function(){var r,n;r=e("./XMLDOMErrorHandler"),n=e("./XMLDOMStringList"),t.exports=function(){class e{constructor(){this.defaultParams={"canonical-form":!1,"cdata-sections":!1,comments:!1,"datatype-normalization":!1,"element-content-whitespace":!0,entities:!0,"error-handler":new r,infoset:!0,"validate-if-schema":!1,namespaces:!0,"namespace-declarations":!0,"normalize-characters":!1,"schema-location":"","schema-type":"","split-cdata-sections":!0,validate:!1,"well-formed":!0},this.params=Object.create(this.defaultParams)}getParameter(e){return this.params.hasOwnProperty(e)?this.params[e]:null}canSetParameter(e,t){return!0}setParameter(e,t){return null!=t?this.params[e]=t:delete this.params[e]}}return Object.defineProperty(e.prototype,"parameterNames",{get:function(){return new n(Object.keys(this.defaultParams))}}),e}.call(this)}).call(this)},{"./XMLDOMErrorHandler":132,"./XMLDOMStringList":134}],132:[function(e,t,r){(function(){t.exports=class{constructor(){}handleError(e){throw new Error(e)}}}).call(this)},{}],133:[function(e,t,r){(function(){t.exports=class{hasFeature(e,t){return!0}createDocumentType(e,t,r){throw new Error("This DOM method is not implemented.")}createDocument(e,t,r){throw new Error("This DOM method is not implemented.")}createHTMLDocument(e){throw new Error("This DOM method is not implemented.")}getFeature(e,t){throw new Error("This DOM method is not implemented.")}}}).call(this)},{}],134:[function(e,t,r){(function(){t.exports=function(){class e{constructor(e){this.arr=e||[]}item(e){return this.arr[e]||null}contains(e){return-1!==this.arr.indexOf(e)}}return Object.defineProperty(e.prototype,"length",{get:function(){return this.arr.length}}),e}.call(this)}).call(this)},{}],135:[function(e,t,r){(function(){var r,n;n=e("./XMLNode"),r=e("./NodeType"),t.exports=class extends n{constructor(e,t,n,i,o,a){if(super(e),null==t)throw new Error("Missing DTD element name. "+this.debugInfo());if(null==n)throw new Error("Missing DTD attribute name. "+this.debugInfo(t));if(!i)throw new Error("Missing DTD attribute type. "+this.debugInfo(t));if(!o)throw new Error("Missing DTD attribute default. "+this.debugInfo(t));if(0!==o.indexOf("#")&&(o="#"+o),!o.match(/^(#REQUIRED|#IMPLIED|#FIXED|#DEFAULT)$/))throw new Error("Invalid default value type; expected: #REQUIRED, #IMPLIED, #FIXED or #DEFAULT. "+this.debugInfo(t));if(a&&!o.match(/^(#FIXED|#DEFAULT)$/))throw new Error("Default value only applies to #FIXED or #DEFAULT. "+this.debugInfo(t));this.elementName=this.stringify.name(t),this.type=r.AttributeDeclaration,this.attributeName=this.stringify.name(n),this.attributeType=this.stringify.dtdAttType(i),a&&(this.defaultValue=this.stringify.dtdAttDefault(a)),this.defaultValueType=o}toString(e){return this.options.writer.dtdAttList(this,this.options.writer.filterOptions(e))}}}).call(this)},{"./NodeType":124,"./XMLNode":146}],136:[function(e,t,r){(function(){var r,n;n=e("./XMLNode"),r=e("./NodeType"),t.exports=class extends n{constructor(e,t,n){if(super(e),null==t)throw new Error("Missing DTD element name. "+this.debugInfo());n||(n="(#PCDATA)"),Array.isArray(n)&&(n="("+n.join(",")+")"),this.name=this.stringify.name(t),this.type=r.ElementDeclaration,this.value=this.stringify.dtdElementValue(n)}toString(e){return this.options.writer.dtdElement(this,this.options.writer.filterOptions(e))}}}).call(this)},{"./NodeType":124,"./XMLNode":146}],137:[function(e,t,r){(function(){var r,n,i;({isObject:i}=e("./Utility")),n=e("./XMLNode"),r=e("./NodeType"),t.exports=function(){class e extends n{constructor(e,t,n,o){if(super(e),null==n)throw new Error("Missing DTD entity name. "+this.debugInfo(n));if(null==o)throw new Error("Missing DTD entity value. "+this.debugInfo(n));if(this.pe=!!t,this.name=this.stringify.name(n),this.type=r.EntityDeclaration,i(o)){if(!o.pubID&&!o.sysID)throw new Error("Public and/or system identifiers are required for an external entity. "+this.debugInfo(n));if(o.pubID&&!o.sysID)throw new Error("System identifier is required for a public external entity. "+this.debugInfo(n));if(this.internal=!1,null!=o.pubID&&(this.pubID=this.stringify.dtdPubID(o.pubID)),null!=o.sysID&&(this.sysID=this.stringify.dtdSysID(o.sysID)),null!=o.nData&&(this.nData=this.stringify.dtdNData(o.nData)),this.pe&&this.nData)throw new Error("Notation declaration is not allowed in a parameter entity. "+this.debugInfo(n))}else this.value=this.stringify.dtdEntityValue(o),this.internal=!0}toString(e){return this.options.writer.dtdEntity(this,this.options.writer.filterOptions(e))}}return Object.defineProperty(e.prototype,"publicId",{get:function(){return this.pubID}}),Object.defineProperty(e.prototype,"systemId",{get:function(){return this.sysID}}),Object.defineProperty(e.prototype,"notationName",{get:function(){return this.nData||null}}),Object.defineProperty(e.prototype,"inputEncoding",{get:function(){return null}}),Object.defineProperty(e.prototype,"xmlEncoding",{get:function(){return null}}),Object.defineProperty(e.prototype,"xmlVersion",{get:function(){return null}}),e}.call(this)}).call(this)},{"./NodeType":124,"./Utility":125,"./XMLNode":146}],138:[function(e,t,r){(function(){var r,n;n=e("./XMLNode"),r=e("./NodeType"),t.exports=function(){class e extends n{constructor(e,t,n){if(super(e),null==t)throw new Error("Missing DTD notation name. "+this.debugInfo(t));if(!n.pubID&&!n.sysID)throw new Error("Public or system identifiers are required for an external entity. "+this.debugInfo(t));this.name=this.stringify.name(t),this.type=r.NotationDeclaration,null!=n.pubID&&(this.pubID=this.stringify.dtdPubID(n.pubID)),null!=n.sysID&&(this.sysID=this.stringify.dtdSysID(n.sysID))}toString(e){return this.options.writer.dtdNotation(this,this.options.writer.filterOptions(e))}}return Object.defineProperty(e.prototype,"publicId",{get:function(){return this.pubID}}),Object.defineProperty(e.prototype,"systemId",{get:function(){return this.sysID}}),e}.call(this)}).call(this)},{"./NodeType":124,"./XMLNode":146}],139:[function(e,t,r){(function(){var r,n,i;({isObject:i}=e("./Utility")),n=e("./XMLNode"),r=e("./NodeType"),t.exports=class extends n{constructor(e,t,n,o){super(e),i(t)&&({version:t,encoding:n,standalone:o}=t),t||(t="1.0"),this.type=r.Declaration,this.version=this.stringify.xmlVersion(t),null!=n&&(this.encoding=this.stringify.xmlEncoding(n)),null!=o&&(this.standalone=this.stringify.xmlStandalone(o))}toString(e){return this.options.writer.declaration(this,this.options.writer.filterOptions(e))}}}).call(this)},{"./NodeType":124,"./Utility":125,"./XMLNode":146}],140:[function(e,t,r){(function(){var r,n,i,o,a,s,u,l;({isObject:l}=e("./Utility")),u=e("./XMLNode"),r=e("./NodeType"),n=e("./XMLDTDAttList"),o=e("./XMLDTDEntity"),i=e("./XMLDTDElement"),a=e("./XMLDTDNotation"),s=e("./XMLNamedNodeMap"),t.exports=function(){class e extends u{constructor(e,t,n){var i,o,a,s;if(super(e),this.type=r.DocType,e.children)for(o=0,a=(s=e.children).length;o<a;o++)if((i=s[o]).type===r.Element){this.name=i.name;break}this.documentObject=e,l(t)&&({pubID:t,sysID:n}=t),null==n&&([n,t]=[t,n]),null!=t&&(this.pubID=this.stringify.dtdPubID(t)),null!=n&&(this.sysID=this.stringify.dtdSysID(n))}element(e,t){var r;return r=new i(this,e,t),this.children.push(r),this}attList(e,t,r,i,o){var a;return a=new n(this,e,t,r,i,o),this.children.push(a),this}entity(e,t){var r;return r=new o(this,!1,e,t),this.children.push(r),this}pEntity(e,t){var r;return r=new o(this,!0,e,t),this.children.push(r),this}notation(e,t){var r;return r=new a(this,e,t),this.children.push(r),this}toString(e){return this.options.writer.docType(this,this.options.writer.filterOptions(e))}ele(e,t){return this.element(e,t)}att(e,t,r,n,i){return this.attList(e,t,r,n,i)}ent(e,t){return this.entity(e,t)}pent(e,t){return this.pEntity(e,t)}not(e,t){return this.notation(e,t)}up(){return this.root()||this.documentObject}isEqualNode(e){return!!super.isEqualNode(e)&&(e.name===this.name&&(e.publicId===this.publicId&&e.systemId===this.systemId))}}return Object.defineProperty(e.prototype,"entities",{get:function(){var e,t,n,i,o;for(i={},t=0,n=(o=this.children).length;t<n;t++)(e=o[t]).type!==r.EntityDeclaration||e.pe||(i[e.name]=e);return new s(i)}}),Object.defineProperty(e.prototype,"notations",{get:function(){var e,t,n,i,o;for(i={},t=0,n=(o=this.children).length;t<n;t++)(e=o[t]).type===r.NotationDeclaration&&(i[e.name]=e);return new s(i)}}),Object.defineProperty(e.prototype,"publicId",{get:function(){return this.pubID}}),Object.defineProperty(e.prototype,"systemId",{get:function(){return this.sysID}}),Object.defineProperty(e.prototype,"internalSubset",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),e}.call(this)}).call(this)},{"./NodeType":124,"./Utility":125,"./XMLDTDAttList":135,"./XMLDTDElement":136,"./XMLDTDEntity":137,"./XMLDTDNotation":138,"./XMLNamedNodeMap":145,"./XMLNode":146}],141:[function(e,t,r){(function(){var r,n,i,o,a,s,u;({isPlainObject:u}=e("./Utility")),i=e("./XMLDOMImplementation"),n=e("./XMLDOMConfiguration"),o=e("./XMLNode"),r=e("./NodeType"),s=e("./XMLStringifier"),a=e("./XMLStringWriter"),t.exports=function(){class e extends o{constructor(e){super(null),this.name="#document",this.type=r.Document,this.documentURI=null,this.domConfig=new n,e||(e={}),e.writer||(e.writer=new a),this.options=e,this.stringify=new s(e)}end(e){var t;return t={},e?u(e)&&(t=e,e=this.options.writer):e=this.options.writer,e.document(this,e.filterOptions(t))}toString(e){return this.options.writer.document(this,this.options.writer.filterOptions(e))}createElement(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}createDocumentFragment(){throw new Error("This DOM method is not implemented."+this.debugInfo())}createTextNode(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}createComment(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}createCDATASection(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}createProcessingInstruction(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())}createAttribute(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}createEntityReference(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}getElementsByTagName(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}importNode(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())}createElementNS(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())}createAttributeNS(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())}getElementsByTagNameNS(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())}getElementById(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}adoptNode(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}normalizeDocument(){throw new Error("This DOM method is not implemented."+this.debugInfo())}renameNode(e,t,r){throw new Error("This DOM method is not implemented."+this.debugInfo())}getElementsByClassName(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}createEvent(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}createRange(){throw new Error("This DOM method is not implemented."+this.debugInfo())}createNodeIterator(e,t,r){throw new Error("This DOM method is not implemented."+this.debugInfo())}createTreeWalker(e,t,r){throw new Error("This DOM method is not implemented."+this.debugInfo())}}return Object.defineProperty(e.prototype,"implementation",{value:new i}),Object.defineProperty(e.prototype,"doctype",{get:function(){var e,t,n,i;for(t=0,n=(i=this.children).length;t<n;t++)if((e=i[t]).type===r.DocType)return e;return null}}),Object.defineProperty(e.prototype,"documentElement",{get:function(){return this.rootObject||null}}),Object.defineProperty(e.prototype,"inputEncoding",{get:function(){return null}}),Object.defineProperty(e.prototype,"strictErrorChecking",{get:function(){return!1}}),Object.defineProperty(e.prototype,"xmlEncoding",{get:function(){return 0!==this.children.length&&this.children[0].type===r.Declaration?this.children[0].encoding:null}}),Object.defineProperty(e.prototype,"xmlStandalone",{get:function(){return 0!==this.children.length&&this.children[0].type===r.Declaration&&"yes"===this.children[0].standalone}}),Object.defineProperty(e.prototype,"xmlVersion",{get:function(){return 0!==this.children.length&&this.children[0].type===r.Declaration?this.children[0].version:"1.0"}}),Object.defineProperty(e.prototype,"URL",{get:function(){return this.documentURI}}),Object.defineProperty(e.prototype,"origin",{get:function(){return null}}),Object.defineProperty(e.prototype,"compatMode",{get:function(){return null}}),Object.defineProperty(e.prototype,"characterSet",{get:function(){return null}}),Object.defineProperty(e.prototype,"contentType",{get:function(){return null}}),e}.call(this)}).call(this)},{"./NodeType":124,"./Utility":125,"./XMLDOMConfiguration":131,"./XMLDOMImplementation":133,"./XMLNode":146,"./XMLStringWriter":151,"./XMLStringifier":152}],142:[function(e,t,r){(function(){var r,n,i,o,a,s,u,l,f,c,h,d,p,g,y,b,m,v,w,_,E,x,T={}.hasOwnProperty;({isObject:E,isFunction:_,isPlainObject:x,getValue:w}=e("./Utility")),r=e("./NodeType"),d=e("./XMLDocument"),p=e("./XMLElement"),o=e("./XMLCData"),a=e("./XMLComment"),y=e("./XMLRaw"),v=e("./XMLText"),g=e("./XMLProcessingInstruction"),c=e("./XMLDeclaration"),h=e("./XMLDocType"),s=e("./XMLDTDAttList"),l=e("./XMLDTDEntity"),u=e("./XMLDTDElement"),f=e("./XMLDTDNotation"),i=e("./XMLAttribute"),m=e("./XMLStringifier"),b=e("./XMLStringWriter"),n=e("./WriterState"),t.exports=class{constructor(e,t,n){var i;this.name="?xml",this.type=r.Document,e||(e={}),i={},e.writer?x(e.writer)&&(i=e.writer,e.writer=new b):e.writer=new b,this.options=e,this.writer=e.writer,this.writerOptions=this.writer.filterOptions(i),this.stringify=new m(e),this.onDataCallback=t||function(){},this.onEndCallback=n||function(){},this.currentNode=null,this.currentLevel=-1,this.openTags={},this.documentStarted=!1,this.documentCompleted=!1,this.root=null}createChildNode(e){var t,n,i,o,a,s,u,l;switch(e.type){case r.CData:this.cdata(e.value);break;case r.Comment:this.comment(e.value);break;case r.Element:for(n in i={},u=e.attribs)T.call(u,n)&&(t=u[n],i[n]=t.value);this.node(e.name,i);break;case r.Dummy:this.dummy();break;case r.Raw:this.raw(e.value);break;case r.Text:this.text(e.value);break;case r.ProcessingInstruction:this.instruction(e.target,e.value);break;default:throw new Error("This XML node type is not supported in a JS object: "+e.constructor.name)}for(a=0,s=(l=e.children).length;a<s;a++)o=l[a],this.createChildNode(o),o.type===r.Element&&this.up();return this}dummy(){return this}node(e,t,r){if(null==e)throw new Error("Missing node name.");if(this.root&&-1===this.currentLevel)throw new Error("Document can only have one root node. "+this.debugInfo(e));return this.openCurrent(),e=w(e),null==t&&(t={}),t=w(t),E(t)||([r,t]=[t,r]),this.currentNode=new p(this,e,t),this.currentNode.children=!1,this.currentLevel++,this.openTags[this.currentLevel]=this.currentNode,null!=r&&this.text(r),this}element(e,t,n){var i,o,a,s,u,l;if(this.currentNode&&this.currentNode.type===r.DocType)this.dtdElement(...arguments);else if(Array.isArray(e)||E(e)||_(e))for(s=this.options.noValidation,this.options.noValidation=!0,(l=new d(this.options).element("TEMP_ROOT")).element(e),this.options.noValidation=s,o=0,a=(u=l.children).length;o<a;o++)i=u[o],this.createChildNode(i),i.type===r.Element&&this.up();else this.node(e,t,n);return this}attribute(e,t){var r,n;if(!this.currentNode||this.currentNode.children)throw new Error("att() can only be used immediately after an ele() call in callback mode. "+this.debugInfo(e));if(null!=e&&(e=w(e)),E(e))for(r in e)T.call(e,r)&&(n=e[r],this.attribute(r,n));else _(t)&&(t=t.apply()),this.options.keepNullAttributes&&null==t?this.currentNode.attribs[e]=new i(this,e,""):null!=t&&(this.currentNode.attribs[e]=new i(this,e,t));return this}text(e){var t;return this.openCurrent(),t=new v(this,e),this.onData(this.writer.text(t,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this}cdata(e){var t;return this.openCurrent(),t=new o(this,e),this.onData(this.writer.cdata(t,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this}comment(e){var t;return this.openCurrent(),t=new a(this,e),this.onData(this.writer.comment(t,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this}raw(e){var t;return this.openCurrent(),t=new y(this,e),this.onData(this.writer.raw(t,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this}instruction(e,t){var r,n,i,o,a;if(this.openCurrent(),null!=e&&(e=w(e)),null!=t&&(t=w(t)),Array.isArray(e))for(r=0,o=e.length;r<o;r++)n=e[r],this.instruction(n);else if(E(e))for(n in e)T.call(e,n)&&(i=e[n],this.instruction(n,i));else _(t)&&(t=t.apply()),a=new g(this,e,t),this.onData(this.writer.processingInstruction(a,this.writerOptions,this.currentLevel+1),this.currentLevel+1);return this}declaration(e,t,r){var n;if(this.openCurrent(),this.documentStarted)throw new Error("declaration() must be the first node.");return n=new c(this,e,t,r),this.onData(this.writer.declaration(n,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this}doctype(e,t,r){if(this.openCurrent(),null==e)throw new Error("Missing root node name.");if(this.root)throw new Error("dtd() must come before the root node.");return this.currentNode=new h(this,t,r),this.currentNode.rootNodeName=e,this.currentNode.children=!1,this.currentLevel++,this.openTags[this.currentLevel]=this.currentNode,this}dtdElement(e,t){var r;return this.openCurrent(),r=new u(this,e,t),this.onData(this.writer.dtdElement(r,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this}attList(e,t,r,n,i){var o;return this.openCurrent(),o=new s(this,e,t,r,n,i),this.onData(this.writer.dtdAttList(o,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this}entity(e,t){var r;return this.openCurrent(),r=new l(this,!1,e,t),this.onData(this.writer.dtdEntity(r,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this}pEntity(e,t){var r;return this.openCurrent(),r=new l(this,!0,e,t),this.onData(this.writer.dtdEntity(r,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this}notation(e,t){var r;return this.openCurrent(),r=new f(this,e,t),this.onData(this.writer.dtdNotation(r,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this}up(){if(this.currentLevel<0)throw new Error("The document node has no parent.");return this.currentNode?(this.currentNode.children?this.closeNode(this.currentNode):this.openNode(this.currentNode),this.currentNode=null):this.closeNode(this.openTags[this.currentLevel]),delete this.openTags[this.currentLevel],this.currentLevel--,this}end(){for(;this.currentLevel>=0;)this.up();return this.onEnd()}openCurrent(){if(this.currentNode)return this.currentNode.children=!0,this.openNode(this.currentNode)}openNode(e){var t,i,o,a;if(!e.isOpen){if(this.root||0!==this.currentLevel||e.type!==r.Element||(this.root=e),i="",e.type===r.Element){for(o in this.writerOptions.state=n.OpenTag,i=this.writer.indent(e,this.writerOptions,this.currentLevel)+"<"+e.name,a=e.attribs)T.call(a,o)&&(t=a[o],i+=this.writer.attribute(t,this.writerOptions,this.currentLevel));i+=(e.children?">":"/>")+this.writer.endline(e,this.writerOptions,this.currentLevel),this.writerOptions.state=n.InsideTag}else this.writerOptions.state=n.OpenTag,i=this.writer.indent(e,this.writerOptions,this.currentLevel)+"<!DOCTYPE "+e.rootNodeName,e.pubID&&e.sysID?i+=' PUBLIC "'+e.pubID+'" "'+e.sysID+'"':e.sysID&&(i+=' SYSTEM "'+e.sysID+'"'),e.children?(i+=" [",this.writerOptions.state=n.InsideTag):(this.writerOptions.state=n.CloseTag,i+=">"),i+=this.writer.endline(e,this.writerOptions,this.currentLevel);return this.onData(i,this.currentLevel),e.isOpen=!0}}closeNode(e){var t;if(!e.isClosed)return t="",this.writerOptions.state=n.CloseTag,t=e.type===r.Element?this.writer.indent(e,this.writerOptions,this.currentLevel)+"</"+e.name+">"+this.writer.endline(e,this.writerOptions,this.currentLevel):this.writer.indent(e,this.writerOptions,this.currentLevel)+"]>"+this.writer.endline(e,this.writerOptions,this.currentLevel),this.writerOptions.state=n.None,this.onData(t,this.currentLevel),e.isClosed=!0}onData(e,t){return this.documentStarted=!0,this.onDataCallback(e,t+1)}onEnd(){return this.documentCompleted=!0,this.onEndCallback()}debugInfo(e){return null==e?"":"node: <"+e+">"}ele(){return this.element(...arguments)}nod(e,t,r){return this.node(e,t,r)}txt(e){return this.text(e)}dat(e){return this.cdata(e)}com(e){return this.comment(e)}ins(e,t){return this.instruction(e,t)}dec(e,t,r){return this.declaration(e,t,r)}dtd(e,t,r){return this.doctype(e,t,r)}e(e,t,r){return this.element(e,t,r)}n(e,t,r){return this.node(e,t,r)}t(e){return this.text(e)}d(e){return this.cdata(e)}c(e){return this.comment(e)}r(e){return this.raw(e)}i(e,t){return this.instruction(e,t)}att(){return this.currentNode&&this.currentNode.type===r.DocType?this.attList(...arguments):this.attribute(...arguments)}a(){return this.currentNode&&this.currentNode.type===r.DocType?this.attList(...arguments):this.attribute(...arguments)}ent(e,t){return this.entity(e,t)}pent(e,t){return this.pEntity(e,t)}not(e,t){return this.notation(e,t)}}}).call(this)},{"./NodeType":124,"./Utility":125,"./WriterState":126,"./XMLAttribute":127,"./XMLCData":128,"./XMLComment":130,"./XMLDTDAttList":135,"./XMLDTDElement":136,"./XMLDTDEntity":137,"./XMLDTDNotation":138,"./XMLDeclaration":139,"./XMLDocType":140,"./XMLDocument":141,"./XMLElement":144,"./XMLProcessingInstruction":148,"./XMLRaw":149,"./XMLStringWriter":151,"./XMLStringifier":152,"./XMLText":153}],143:[function(e,t,r){(function(){var r,n;n=e("./XMLNode"),r=e("./NodeType"),t.exports=class extends n{constructor(e){super(e),this.type=r.Dummy}clone(){return Object.create(this)}toString(e){return""}}}).call(this)},{"./NodeType":124,"./XMLNode":146}],144:[function(e,t,r){(function(){var r,n,i,o,a,s,u,l={}.hasOwnProperty;({isObject:u,isFunction:s,getValue:a}=e("./Utility")),o=e("./XMLNode"),r=e("./NodeType"),n=e("./XMLAttribute"),i=e("./XMLNamedNodeMap"),t.exports=function(){class e extends o{constructor(e,t,n){var i,o,a,s;if(super(e),null==t)throw new Error("Missing element name. "+this.debugInfo());if(this.name=this.stringify.name(t),this.type=r.Element,this.attribs={},this.schemaTypeInfo=null,null!=n&&this.attribute(n),e.type===r.Document&&(this.isRoot=!0,this.documentObject=e,e.rootObject=this,e.children))for(o=0,a=(s=e.children).length;o<a;o++)if((i=s[o]).type===r.DocType){i.name=this.name;break}}clone(){var e,t,r,n;for(t in(r=Object.create(this)).isRoot&&(r.documentObject=null),r.attribs={},n=this.attribs)l.call(n,t)&&(e=n[t],r.attribs[t]=e.clone());return r.children=[],this.children.forEach(function(e){var t;return(t=e.clone()).parent=r,r.children.push(t)}),r}attribute(e,t){var r,i;if(null!=e&&(e=a(e)),u(e))for(r in e)l.call(e,r)&&(i=e[r],this.attribute(r,i));else s(t)&&(t=t.apply()),this.options.keepNullAttributes&&null==t?this.attribs[e]=new n(this,e,""):null!=t&&(this.attribs[e]=new n(this,e,t));return this}removeAttribute(e){var t,r,n;if(null==e)throw new Error("Missing attribute name. "+this.debugInfo());if(e=a(e),Array.isArray(e))for(r=0,n=e.length;r<n;r++)t=e[r],delete this.attribs[t];else delete this.attribs[e];return this}toString(e){return this.options.writer.element(this,this.options.writer.filterOptions(e))}att(e,t){return this.attribute(e,t)}a(e,t){return this.attribute(e,t)}getAttribute(e){return this.attribs.hasOwnProperty(e)?this.attribs[e].value:null}setAttribute(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())}getAttributeNode(e){return this.attribs.hasOwnProperty(e)?this.attribs[e]:null}setAttributeNode(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}removeAttributeNode(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}getElementsByTagName(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}getAttributeNS(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())}setAttributeNS(e,t,r){throw new Error("This DOM method is not implemented."+this.debugInfo())}removeAttributeNS(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())}getAttributeNodeNS(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())}setAttributeNodeNS(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}getElementsByTagNameNS(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())}hasAttribute(e){return this.attribs.hasOwnProperty(e)}hasAttributeNS(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())}setIdAttribute(e,t){return this.attribs.hasOwnProperty(e)?this.attribs[e].isId:t}setIdAttributeNS(e,t,r){throw new Error("This DOM method is not implemented."+this.debugInfo())}setIdAttributeNode(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())}getElementsByTagName(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}getElementsByTagNameNS(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())}getElementsByClassName(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}isEqualNode(e){var t,r,n;if(!super.isEqualNode(e))return!1;if(e.namespaceURI!==this.namespaceURI)return!1;if(e.prefix!==this.prefix)return!1;if(e.localName!==this.localName)return!1;if(e.attribs.length!==this.attribs.length)return!1;for(t=r=0,n=this.attribs.length-1;0<=n?r<=n:r>=n;t=0<=n?++r:--r)if(!this.attribs[t].isEqualNode(e.attribs[t]))return!1;return!0}}return Object.defineProperty(e.prototype,"tagName",{get:function(){return this.name}}),Object.defineProperty(e.prototype,"namespaceURI",{get:function(){return""}}),Object.defineProperty(e.prototype,"prefix",{get:function(){return""}}),Object.defineProperty(e.prototype,"localName",{get:function(){return this.name}}),Object.defineProperty(e.prototype,"id",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(e.prototype,"className",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(e.prototype,"classList",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(e.prototype,"attributes",{get:function(){return this.attributeMap&&this.attributeMap.nodes||(this.attributeMap=new i(this.attribs)),this.attributeMap}}),e}.call(this)}).call(this)},{"./NodeType":124,"./Utility":125,"./XMLAttribute":127,"./XMLNamedNodeMap":145,"./XMLNode":146}],145:[function(e,t,r){(function(){t.exports=function(){class e{constructor(e){this.nodes=e}clone(){return this.nodes=null}getNamedItem(e){return this.nodes[e]}setNamedItem(e){var t;return t=this.nodes[e.nodeName],this.nodes[e.nodeName]=e,t||null}removeNamedItem(e){var t;return t=this.nodes[e],delete this.nodes[e],t||null}item(e){return this.nodes[Object.keys(this.nodes)[e]]||null}getNamedItemNS(e,t){throw new Error("This DOM method is not implemented.")}setNamedItemNS(e){throw new Error("This DOM method is not implemented.")}removeNamedItemNS(e,t){throw new Error("This DOM method is not implemented.")}}return Object.defineProperty(e.prototype,"length",{get:function(){return Object.keys(this.nodes).length||0}}),e}.call(this)}).call(this)},{}],146:[function(e,t,r){(function(){var r,n,i,o,a,s,u,l,f,c,h,d,p,g,y,b,m={}.hasOwnProperty,v=[].splice;({isObject:b,isFunction:y,isEmpty:g,getValue:p}=e("./Utility")),l=null,i=null,o=null,a=null,s=null,h=null,d=null,c=null,u=null,n=null,f=null,r=null,t.exports=function(){class t{constructor(t){this.parent=t,this.parent&&(this.options=this.parent.options,this.stringify=this.parent.stringify),this.value=null,this.children=[],this.baseURI=null,l||(l=e("./XMLElement"),i=e("./XMLCData"),o=e("./XMLComment"),a=e("./XMLDeclaration"),s=e("./XMLDocType"),h=e("./XMLRaw"),d=e("./XMLText"),c=e("./XMLProcessingInstruction"),u=e("./XMLDummy"),n=e("./NodeType"),f=e("./XMLNodeList"),e("./XMLNamedNodeMap"),r=e("./DocumentPosition"))}setParent(e){var t,r,n,i,o;for(this.parent=e,e&&(this.options=e.options,this.stringify=e.stringify),o=[],r=0,n=(i=this.children).length;r<n;r++)t=i[r],o.push(t.setParent(this));return o}element(e,t,r){var n,i,o,a,s,u,l,f,c;if(u=null,null===t&&null==r&&([t,r]=[{},null]),null==t&&(t={}),t=p(t),b(t)||([r,t]=[t,r]),null!=e&&(e=p(e)),Array.isArray(e))for(o=0,l=e.length;o<l;o++)i=e[o],u=this.element(i);else if(y(e))u=this.element(e.apply());else if(b(e)){for(s in e)if(m.call(e,s))if(c=e[s],y(c)&&(c=c.apply()),!this.options.ignoreDecorators&&this.stringify.convertAttKey&&0===s.indexOf(this.stringify.convertAttKey))u=this.attribute(s.substr(this.stringify.convertAttKey.length),c);else if(!this.options.separateArrayItems&&Array.isArray(c)&&g(c))u=this.dummy();else if(b(c)&&g(c))u=this.element(s);else if(this.options.keepNullNodes||null!=c)if(!this.options.separateArrayItems&&Array.isArray(c))for(a=0,f=c.length;a<f;a++)i=c[a],(n={})[s]=i,u=this.element(n);else b(c)?!this.options.ignoreDecorators&&this.stringify.convertTextKey&&0===s.indexOf(this.stringify.convertTextKey)?u=this.element(c):(u=this.element(s)).element(c):u=this.element(s,c);else u=this.dummy()}else u=this.options.keepNullNodes||null!==r?!this.options.ignoreDecorators&&this.stringify.convertTextKey&&0===e.indexOf(this.stringify.convertTextKey)?this.text(r):!this.options.ignoreDecorators&&this.stringify.convertCDataKey&&0===e.indexOf(this.stringify.convertCDataKey)?this.cdata(r):!this.options.ignoreDecorators&&this.stringify.convertCommentKey&&0===e.indexOf(this.stringify.convertCommentKey)?this.comment(r):!this.options.ignoreDecorators&&this.stringify.convertRawKey&&0===e.indexOf(this.stringify.convertRawKey)?this.raw(r):!this.options.ignoreDecorators&&this.stringify.convertPIKey&&0===e.indexOf(this.stringify.convertPIKey)?this.instruction(e.substr(this.stringify.convertPIKey.length),r):this.node(e,t,r):this.dummy();if(null==u)throw new Error("Could not create any elements with: "+e+". "+this.debugInfo());return u}insertBefore(e,t,r){var n,i,o,a,s;if(null!=e?e.type:void 0)return a=t,(o=e).setParent(this),a?(i=children.indexOf(a),s=children.splice(i),children.push(o),Array.prototype.push.apply(children,s)):children.push(o),o;if(this.isRoot)throw new Error("Cannot insert elements at root level. "+this.debugInfo(e));return i=this.parent.children.indexOf(this),s=this.parent.children.splice(i),n=this.parent.element(e,t,r),Array.prototype.push.apply(this.parent.children,s),n}insertAfter(e,t,r){var n,i,o;if(this.isRoot)throw new Error("Cannot insert elements at root level. "+this.debugInfo(e));return i=this.parent.children.indexOf(this),o=this.parent.children.splice(i+1),n=this.parent.element(e,t,r),Array.prototype.push.apply(this.parent.children,o),n}remove(){var e;if(this.isRoot)throw new Error("Cannot remove the root element. "+this.debugInfo());return e=this.parent.children.indexOf(this),v.apply(this.parent.children,[e,e-e+1].concat([])),this.parent}node(e,t,r){var n;return null!=e&&(e=p(e)),t||(t={}),t=p(t),b(t)||([r,t]=[t,r]),n=new l(this,e,t),null!=r&&n.text(r),this.children.push(n),n}text(e){var t;return b(e)&&this.element(e),t=new d(this,e),this.children.push(t),this}cdata(e){var t;return t=new i(this,e),this.children.push(t),this}comment(e){var t;return t=new o(this,e),this.children.push(t),this}commentBefore(e){var t,r;return t=this.parent.children.indexOf(this),r=this.parent.children.splice(t),this.parent.comment(e),Array.prototype.push.apply(this.parent.children,r),this}commentAfter(e){var t,r;return t=this.parent.children.indexOf(this),r=this.parent.children.splice(t+1),this.parent.comment(e),Array.prototype.push.apply(this.parent.children,r),this}raw(e){var t;return t=new h(this,e),this.children.push(t),this}dummy(){return new u(this)}instruction(e,t){var r,n,i,o,a;if(null!=e&&(e=p(e)),null!=t&&(t=p(t)),Array.isArray(e))for(o=0,a=e.length;o<a;o++)r=e[o],this.instruction(r);else if(b(e))for(r in e)m.call(e,r)&&(n=e[r],this.instruction(r,n));else y(t)&&(t=t.apply()),i=new c(this,e,t),this.children.push(i);return this}instructionBefore(e,t){var r,n;return r=this.parent.children.indexOf(this),n=this.parent.children.splice(r),this.parent.instruction(e,t),Array.prototype.push.apply(this.parent.children,n),this}instructionAfter(e,t){var r,n;return r=this.parent.children.indexOf(this),n=this.parent.children.splice(r+1),this.parent.instruction(e,t),Array.prototype.push.apply(this.parent.children,n),this}declaration(e,t,r){var i,o;return i=this.document(),o=new a(i,e,t,r),0===i.children.length?i.children.unshift(o):i.children[0].type===n.Declaration?i.children[0]=o:i.children.unshift(o),i.root()||i}dtd(e,t){var r,i,o,a,u,l,f,c,h;for(r=this.document(),i=new s(r,e,t),o=a=0,l=(c=r.children).length;a<l;o=++a)if(c[o].type===n.DocType)return r.children[o]=i,i;for(o=u=0,f=(h=r.children).length;u<f;o=++u)if(h[o].isRoot)return r.children.splice(o,0,i),i;return r.children.push(i),i}up(){if(this.isRoot)throw new Error("The root node has no parent. Use doc() if you need to get the document object.");return this.parent}root(){var e;for(e=this;e;){if(e.type===n.Document)return e.rootObject;if(e.isRoot)return e;e=e.parent}}document(){var e;for(e=this;e;){if(e.type===n.Document)return e;e=e.parent}}end(e){return this.document().end(e)}prev(){var e;if((e=this.parent.children.indexOf(this))<1)throw new Error("Already at the first node. "+this.debugInfo());return this.parent.children[e-1]}next(){var e;if(-1===(e=this.parent.children.indexOf(this))||e===this.parent.children.length-1)throw new Error("Already at the last node. "+this.debugInfo());return this.parent.children[e+1]}importDocument(e){var t,r,i,o,a;if((r=e.root().clone()).parent=this,r.isRoot=!1,this.children.push(r),this.type===n.Document&&(r.isRoot=!0,r.documentObject=this,this.rootObject=r,this.children))for(i=0,o=(a=this.children).length;i<o;i++)if((t=a[i]).type===n.DocType){t.name=r.name;break}return this}debugInfo(e){var t,r;return null!=(e=e||this.name)||(null!=(t=this.parent)?t.name:void 0)?null==e?"parent: <"+this.parent.name+">":(null!=(r=this.parent)?r.name:void 0)?"node: <"+e+">, parent: <"+this.parent.name+">":"node: <"+e+">":""}ele(e,t,r){return this.element(e,t,r)}nod(e,t,r){return this.node(e,t,r)}txt(e){return this.text(e)}dat(e){return this.cdata(e)}com(e){return this.comment(e)}ins(e,t){return this.instruction(e,t)}doc(){return this.document()}dec(e,t,r){return this.declaration(e,t,r)}e(e,t,r){return this.element(e,t,r)}n(e,t,r){return this.node(e,t,r)}t(e){return this.text(e)}d(e){return this.cdata(e)}c(e){return this.comment(e)}r(e){return this.raw(e)}i(e,t){return this.instruction(e,t)}u(){return this.up()}importXMLBuilder(e){return this.importDocument(e)}attribute(e,t){throw new Error("attribute() applies to element nodes only.")}att(e,t){return this.attribute(e,t)}a(e,t){return this.attribute(e,t)}removeAttribute(e){throw new Error("attribute() applies to element nodes only.")}replaceChild(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())}removeChild(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}appendChild(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}hasChildNodes(){return 0!==this.children.length}cloneNode(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}normalize(){throw new Error("This DOM method is not implemented."+this.debugInfo())}isSupported(e,t){return!0}hasAttributes(){return 0!==this.attribs.length}compareDocumentPosition(e){var t;return this,this===e?0:this.document()!==e.document()?(t=r.Disconnected|r.ImplementationSpecific,Math.random()<.5?t|=r.Preceding:t|=r.Following,t):this.isAncestor(e)?r.Contains|r.Preceding:this.isDescendant(e)?r.Contains|r.Following:this.isPreceding(e)?r.Preceding:r.Following}isSameNode(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}lookupPrefix(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}isDefaultNamespace(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}lookupNamespaceURI(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}isEqualNode(e){var t,r,n;if(e.nodeType!==this.nodeType)return!1;if(e.children.length!==this.children.length)return!1;for(t=r=0,n=this.children.length-1;0<=n?r<=n:r>=n;t=0<=n?++r:--r)if(!this.children[t].isEqualNode(e.children[t]))return!1;return!0}getFeature(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())}setUserData(e,t,r){throw new Error("This DOM method is not implemented."+this.debugInfo())}getUserData(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}contains(e){return!!e&&(e===this||this.isDescendant(e))}isDescendant(e){var t,r,n,i;for(r=0,n=(i=this.children).length;r<n;r++){if(e===(t=i[r]))return!0;if(t.isDescendant(e))return!0}return!1}isAncestor(e){return e.isDescendant(this)}isPreceding(e){var t,r;return t=this.treePosition(e),r=this.treePosition(this),-1!==t&&-1!==r&&t<r}isFollowing(e){var t,r;return t=this.treePosition(e),r=this.treePosition(this),-1!==t&&-1!==r&&t>r}treePosition(e){var t,r;return r=0,t=!1,this.foreachTreeNode(this.document(),function(n){if(r++,!t&&n===e)return t=!0}),t?r:-1}foreachTreeNode(e,t){var r,n,i,o,a;for(e||(e=this.document()),n=0,i=(o=e.children).length;n<i;n++){if(a=t(r=o[n]))return a;if(a=this.foreachTreeNode(r,t))return a}}}return Object.defineProperty(t.prototype,"nodeName",{get:function(){return this.name}}),Object.defineProperty(t.prototype,"nodeType",{get:function(){return this.type}}),Object.defineProperty(t.prototype,"nodeValue",{get:function(){return this.value}}),Object.defineProperty(t.prototype,"parentNode",{get:function(){return this.parent}}),Object.defineProperty(t.prototype,"childNodes",{get:function(){return this.childNodeList&&this.childNodeList.nodes||(this.childNodeList=new f(this.children)),this.childNodeList}}),Object.defineProperty(t.prototype,"firstChild",{get:function(){return this.children[0]||null}}),Object.defineProperty(t.prototype,"lastChild",{get:function(){return this.children[this.children.length-1]||null}}),Object.defineProperty(t.prototype,"previousSibling",{get:function(){var e;return e=this.parent.children.indexOf(this),this.parent.children[e-1]||null}}),Object.defineProperty(t.prototype,"nextSibling",{get:function(){var e;return e=this.parent.children.indexOf(this),this.parent.children[e+1]||null}}),Object.defineProperty(t.prototype,"ownerDocument",{get:function(){return this.document()||null}}),Object.defineProperty(t.prototype,"textContent",{get:function(){var e,t,r,i,o;if(this.nodeType===n.Element||this.nodeType===n.DocumentFragment){for(o="",t=0,r=(i=this.children).length;t<r;t++)(e=i[t]).textContent&&(o+=e.textContent);return o}return null},set:function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),t}.call(this)}).call(this)},{"./DocumentPosition":123,"./NodeType":124,"./Utility":125,"./XMLCData":128,"./XMLComment":130,"./XMLDeclaration":139,"./XMLDocType":140,"./XMLDummy":143,"./XMLElement":144,"./XMLNamedNodeMap":145,"./XMLNodeList":147,"./XMLProcessingInstruction":148,"./XMLRaw":149,"./XMLText":153}],147:[function(e,t,r){(function(){t.exports=function(){class e{constructor(e){this.nodes=e}clone(){return this.nodes=null}item(e){return this.nodes[e]||null}}return Object.defineProperty(e.prototype,"length",{get:function(){return this.nodes.length||0}}),e}.call(this)}).call(this)},{}],148:[function(e,t,r){(function(){var r,n;r=e("./NodeType"),n=e("./XMLCharacterData"),t.exports=class extends n{constructor(e,t,n){if(super(e),null==t)throw new Error("Missing instruction target. "+this.debugInfo());this.type=r.ProcessingInstruction,this.target=this.stringify.insTarget(t),this.name=this.target,n&&(this.value=this.stringify.insValue(n))}clone(){return Object.create(this)}toString(e){return this.options.writer.processingInstruction(this,this.options.writer.filterOptions(e))}isEqualNode(e){return!!super.isEqualNode(e)&&e.target===this.target}}}).call(this)},{"./NodeType":124,"./XMLCharacterData":129}],149:[function(e,t,r){(function(){var r,n;r=e("./NodeType"),n=e("./XMLNode"),t.exports=class extends n{constructor(e,t){if(super(e),null==t)throw new Error("Missing raw text. "+this.debugInfo());this.type=r.Raw,this.value=this.stringify.raw(t)}clone(){return Object.create(this)}toString(e){return this.options.writer.raw(this,this.options.writer.filterOptions(e))}}}).call(this)},{"./NodeType":124,"./XMLNode":146}],150:[function(e,t,r){(function(){var r,n,i,o={}.hasOwnProperty;r=e("./NodeType"),i=e("./XMLWriterBase"),n=e("./WriterState"),t.exports=class extends i{constructor(e,t){super(t),this.stream=e}endline(e,t,r){return e.isLastRootNode&&t.state===n.CloseTag?"":super.endline(e,t,r)}document(e,t){var r,n,i,o,a,s,u,l,f;for(n=i=0,a=(u=e.children).length;i<a;n=++i)(r=u[n]).isLastRootNode=n===e.children.length-1;for(t=this.filterOptions(t),f=[],o=0,s=(l=e.children).length;o<s;o++)r=l[o],f.push(this.writeChildNode(r,t,0));return f}cdata(e,t,r){return this.stream.write(super.cdata(e,t,r))}comment(e,t,r){return this.stream.write(super.comment(e,t,r))}declaration(e,t,r){return this.stream.write(super.declaration(e,t,r))}docType(e,t,r){var i,o,a,s;if(r||(r=0),this.openNode(e,t,r),t.state=n.OpenTag,this.stream.write(this.indent(e,t,r)),this.stream.write("<!DOCTYPE "+e.root().name),e.pubID&&e.sysID?this.stream.write(' PUBLIC "'+e.pubID+'" "'+e.sysID+'"'):e.sysID&&this.stream.write(' SYSTEM "'+e.sysID+'"'),e.children.length>0){for(this.stream.write(" ["),this.stream.write(this.endline(e,t,r)),t.state=n.InsideTag,o=0,a=(s=e.children).length;o<a;o++)i=s[o],this.writeChildNode(i,t,r+1);t.state=n.CloseTag,this.stream.write("]")}return t.state=n.CloseTag,this.stream.write(t.spaceBeforeSlash+">"),this.stream.write(this.endline(e,t,r)),t.state=n.None,this.closeNode(e,t,r)}element(e,t,i){var a,s,u,l,f,c,h,d,p,g,y,b,m,v;if(i||(i=0),this.openNode(e,t,i),t.state=n.OpenTag,p=this.indent(e,t,i)+"<"+e.name,t.pretty&&t.width>0)for(d in c=p.length,y=e.attribs)o.call(y,d)&&(a=y[d],c+(g=this.attribute(a,t,i)).length>t.width?(v=this.indent(e,t,i+1)+g,p+=this.endline(e,t,i)+v,c=v.length):(p+=v=" "+g,c+=v.length));else for(d in b=e.attribs)o.call(b,d)&&(a=b[d],p+=this.attribute(a,t,i));if(this.stream.write(p),l=0===(u=e.children.length)?null:e.children[0],0===u||e.children.every(function(e){return(e.type===r.Text||e.type===r.Raw||e.type===r.CData)&&""===e.value}))t.allowEmpty?(this.stream.write(">"),t.state=n.CloseTag,this.stream.write("</"+e.name+">")):(t.state=n.CloseTag,this.stream.write(t.spaceBeforeSlash+"/>"));else if(!t.pretty||1!==u||l.type!==r.Text&&l.type!==r.Raw&&l.type!==r.CData||null==l.value){for(this.stream.write(">"+this.endline(e,t,i)),t.state=n.InsideTag,f=0,h=(m=e.children).length;f<h;f++)s=m[f],this.writeChildNode(s,t,i+1);t.state=n.CloseTag,this.stream.write(this.indent(e,t,i)+"</"+e.name+">")}else this.stream.write(">"),t.state=n.InsideTag,t.suppressPrettyCount++,!0,this.writeChildNode(l,t,i+1),t.suppressPrettyCount--,!1,t.state=n.CloseTag,this.stream.write("</"+e.name+">");return this.stream.write(this.endline(e,t,i)),t.state=n.None,this.closeNode(e,t,i)}processingInstruction(e,t,r){return this.stream.write(super.processingInstruction(e,t,r))}raw(e,t,r){return this.stream.write(super.raw(e,t,r))}text(e,t,r){return this.stream.write(super.text(e,t,r))}dtdAttList(e,t,r){return this.stream.write(super.dtdAttList(e,t,r))}dtdElement(e,t,r){return this.stream.write(super.dtdElement(e,t,r))}dtdEntity(e,t,r){return this.stream.write(super.dtdEntity(e,t,r))}dtdNotation(e,t,r){return this.stream.write(super.dtdNotation(e,t,r))}}}).call(this)},{"./NodeType":124,"./WriterState":126,"./XMLWriterBase":154}],151:[function(e,t,r){(function(){var r;r=e("./XMLWriterBase"),t.exports=class extends r{constructor(e){super(e)}document(e,t){var r,n,i,o,a;for(t=this.filterOptions(t),o="",n=0,i=(a=e.children).length;n<i;n++)r=a[n],o+=this.writeChildNode(r,t,0);return t.pretty&&o.slice(-t.newline.length)===t.newline&&(o=o.slice(0,-t.newline.length)),o}}}).call(this)},{"./XMLWriterBase":154}],152:[function(e,t,r){(function(){var e={}.hasOwnProperty;t.exports=function(){class t{constructor(t){var r,n,i;for(r in this.assertLegalChar=this.assertLegalChar.bind(this),this.assertLegalName=this.assertLegalName.bind(this),t||(t={}),this.options=t,this.options.version||(this.options.version="1.0"),n=t.stringify||{})e.call(n,r)&&(i=n[r],this[r]=i)}name(e){return this.options.noValidation?e:this.assertLegalName(""+e||"")}text(e){return this.options.noValidation?e:this.assertLegalChar(this.textEscape(""+e||""))}cdata(e){return this.options.noValidation?e:(e=(e=""+e||"").replace("]]>","]]]]><![CDATA[>"),this.assertLegalChar(e))}comment(e){if(this.options.noValidation)return e;if((e=""+e||"").match(/--/))throw new Error("Comment text cannot contain double-hypen: "+e);return this.assertLegalChar(e)}raw(e){return this.options.noValidation?e:""+e||""}attValue(e){return this.options.noValidation?e:this.assertLegalChar(this.attEscape(e=""+e||""))}insTarget(e){return this.options.noValidation?e:this.assertLegalChar(""+e||"")}insValue(e){if(this.options.noValidation)return e;if((e=""+e||"").match(/\?>/))throw new Error("Invalid processing instruction value: "+e);return this.assertLegalChar(e)}xmlVersion(e){if(this.options.noValidation)return e;if(!(e=""+e||"").match(/1\.[0-9]+/))throw new Error("Invalid version number: "+e);return e}xmlEncoding(e){if(this.options.noValidation)return e;if(!(e=""+e||"").match(/^[A-Za-z](?:[A-Za-z0-9._-])*$/))throw new Error("Invalid encoding: "+e);return this.assertLegalChar(e)}xmlStandalone(e){return this.options.noValidation?e:e?"yes":"no"}dtdPubID(e){return this.options.noValidation?e:this.assertLegalChar(""+e||"")}dtdSysID(e){return this.options.noValidation?e:this.assertLegalChar(""+e||"")}dtdElementValue(e){return this.options.noValidation?e:this.assertLegalChar(""+e||"")}dtdAttType(e){return this.options.noValidation?e:this.assertLegalChar(""+e||"")}dtdAttDefault(e){return this.options.noValidation?e:this.assertLegalChar(""+e||"")}dtdEntityValue(e){return this.options.noValidation?e:this.assertLegalChar(""+e||"")}dtdNData(e){return this.options.noValidation?e:this.assertLegalChar(""+e||"")}assertLegalChar(e){var t,r;if(this.options.noValidation)return e;if("1.0"===this.options.version){if(t=/[\0-\x08\x0B\f\x0E-\x1F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/g,void 0!==this.options.invalidCharReplacement)e=e.replace(t,this.options.invalidCharReplacement);else if(r=e.match(t))throw new Error(`Invalid character in string: ${e} at index ${r.index}`)}else if("1.1"===this.options.version)if(t=/[\0\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/g,void 0!==this.options.invalidCharReplacement)e=e.replace(t,this.options.invalidCharReplacement);else if(r=e.match(t))throw new Error(`Invalid character in string: ${e} at index ${r.index}`);return e}assertLegalName(e){var t;if(this.options.noValidation)return e;if(t=/^([:A-Z_a-z\xC0-\xD6\xD8-\xF6\xF8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]|[\uD800-\uDB7F][\uDC00-\uDFFF])([\x2D\.0-:A-Z_a-z\xB7\xC0-\xD6\xD8-\xF6\xF8-\u037D\u037F-\u1FFF\u200C\u200D\u203F\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]|[\uD800-\uDB7F][\uDC00-\uDFFF])*$/,!(e=this.assertLegalChar(e)).match(t))throw new Error(`Invalid character in name: ${e}`);return e}textEscape(e){var t;return this.options.noValidation?e:(t=this.options.noDoubleEncoding?/(?!&(lt|gt|amp|apos|quot);)&/g:/&/g,e.replace(t,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\r/g,"&#xD;"))}attEscape(e){var t;return this.options.noValidation?e:(t=this.options.noDoubleEncoding?/(?!&(lt|gt|amp|apos|quot);)&/g:/&/g,e.replace(t,"&amp;").replace(/</g,"&lt;").replace(/"/g,"&quot;").replace(/\t/g,"&#x9;").replace(/\n/g,"&#xA;").replace(/\r/g,"&#xD;"))}}return t.prototype.convertAttKey="@",t.prototype.convertPIKey="?",t.prototype.convertTextKey="#text",t.prototype.convertCDataKey="#cdata",t.prototype.convertCommentKey="#comment",t.prototype.convertRawKey="#raw",t}.call(this)}).call(this)},{}],153:[function(e,t,r){(function(){var r,n;r=e("./NodeType"),n=e("./XMLCharacterData"),t.exports=function(){class e extends n{constructor(e,t){if(super(e),null==t)throw new Error("Missing element text. "+this.debugInfo());this.name="#text",this.type=r.Text,this.value=this.stringify.text(t)}clone(){return Object.create(this)}toString(e){return this.options.writer.text(this,this.options.writer.filterOptions(e))}splitText(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}replaceWholeText(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}}return Object.defineProperty(e.prototype,"isElementContentWhitespace",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(e.prototype,"wholeText",{get:function(){var e,t,r;for(r="",t=this.previousSibling;t;)r=t.data+r,t=t.previousSibling;for(r+=this.data,e=this.nextSibling;e;)r+=e.data,e=e.nextSibling;return r}}),e}.call(this)}).call(this)},{"./NodeType":124,"./XMLCharacterData":129}],154:[function(e,t,r){(function(){var r,n,i,o={}.hasOwnProperty;({assign:i}=e("./Utility")),r=e("./NodeType"),e("./XMLDeclaration"),e("./XMLDocType"),e("./XMLCData"),e("./XMLComment"),e("./XMLElement"),e("./XMLRaw"),e("./XMLText"),e("./XMLProcessingInstruction"),e("./XMLDummy"),e("./XMLDTDAttList"),e("./XMLDTDElement"),e("./XMLDTDEntity"),e("./XMLDTDNotation"),n=e("./WriterState"),t.exports=class{constructor(e){var t,r,n;for(t in e||(e={}),this.options=e,r=e.writer||{})o.call(r,t)&&(n=r[t],this["_"+t]=this[t],this[t]=n)}filterOptions(e){var t,r,o,a,s,u,l,f,c;return e||(e={}),e=i({},this.options,e),(t={writer:this}).pretty=e.pretty||!1,t.allowEmpty=e.allowEmpty||!1,t.indent=null!=(r=e.indent)?r:"  ",t.newline=null!=(o=e.newline)?o:"\n",t.offset=null!=(a=e.offset)?a:0,t.width=null!=(s=e.width)?s:0,t.dontPrettyTextNodes=null!=(u=null!=(l=e.dontPrettyTextNodes)?l:e.dontprettytextnodes)?u:0,t.spaceBeforeSlash=null!=(f=null!=(c=e.spaceBeforeSlash)?c:e.spacebeforeslash)?f:"",!0===t.spaceBeforeSlash&&(t.spaceBeforeSlash=" "),t.suppressPrettyCount=0,t.user={},t.state=n.None,t}indent(e,t,r){var n;return!t.pretty||t.suppressPrettyCount?"":t.pretty&&(n=(r||0)+t.offset+1)>0?new Array(n).join(t.indent):""}endline(e,t,r){return!t.pretty||t.suppressPrettyCount?"":t.newline}attribute(e,t,r){var n;return this.openAttribute(e,t,r),n=t.pretty&&t.width>0?e.name+'="'+e.value+'"':" "+e.name+'="'+e.value+'"',this.closeAttribute(e,t,r),n}cdata(e,t,r){var i;return this.openNode(e,t,r),t.state=n.OpenTag,i=this.indent(e,t,r)+"<![CDATA[",t.state=n.InsideTag,i+=e.value,t.state=n.CloseTag,i+="]]>"+this.endline(e,t,r),t.state=n.None,this.closeNode(e,t,r),i}comment(e,t,r){var i;return this.openNode(e,t,r),t.state=n.OpenTag,i=this.indent(e,t,r)+"\x3c!-- ",t.state=n.InsideTag,i+=e.value,t.state=n.CloseTag,i+=" --\x3e"+this.endline(e,t,r),t.state=n.None,this.closeNode(e,t,r),i}declaration(e,t,r){var i;return this.openNode(e,t,r),t.state=n.OpenTag,i=this.indent(e,t,r)+"<?xml",t.state=n.InsideTag,i+=' version="'+e.version+'"',null!=e.encoding&&(i+=' encoding="'+e.encoding+'"'),null!=e.standalone&&(i+=' standalone="'+e.standalone+'"'),t.state=n.CloseTag,i+=t.spaceBeforeSlash+"?>",i+=this.endline(e,t,r),t.state=n.None,this.closeNode(e,t,r),i}docType(e,t,r){var i,o,a,s,u;if(r||(r=0),this.openNode(e,t,r),t.state=n.OpenTag,s=this.indent(e,t,r),s+="<!DOCTYPE "+e.root().name,e.pubID&&e.sysID?s+=' PUBLIC "'+e.pubID+'" "'+e.sysID+'"':e.sysID&&(s+=' SYSTEM "'+e.sysID+'"'),e.children.length>0){for(s+=" [",s+=this.endline(e,t,r),t.state=n.InsideTag,o=0,a=(u=e.children).length;o<a;o++)i=u[o],s+=this.writeChildNode(i,t,r+1);t.state=n.CloseTag,s+="]"}return t.state=n.CloseTag,s+=t.spaceBeforeSlash+">",s+=this.endline(e,t,r),t.state=n.None,this.closeNode(e,t,r),s}element(e,t,i){var a,s,u,l,f,c,h,d,p,g,y,b,m,v,w,_,E,x;if(i||(i=0),y=!1,this.openNode(e,t,i),t.state=n.OpenTag,b=this.indent(e,t,i)+"<"+e.name,t.pretty&&t.width>0)for(g in h=b.length,v=e.attribs)o.call(v,g)&&(a=v[g],h+(m=this.attribute(a,t,i)).length>t.width?(x=this.indent(e,t,i+1)+m,b+=this.endline(e,t,i)+x,h=x.length):(b+=x=" "+m,h+=x.length));else for(g in w=e.attribs)o.call(w,g)&&(a=w[g],b+=this.attribute(a,t,i));if(l=0===(u=e.children.length)?null:e.children[0],0===u||e.children.every(function(e){return(e.type===r.Text||e.type===r.Raw||e.type===r.CData)&&""===e.value}))t.allowEmpty?(b+=">",t.state=n.CloseTag,b+="</"+e.name+">"+this.endline(e,t,i)):(t.state=n.CloseTag,b+=t.spaceBeforeSlash+"/>"+this.endline(e,t,i));else if(!t.pretty||1!==u||l.type!==r.Text&&l.type!==r.Raw&&l.type!==r.CData||null==l.value){if(t.dontPrettyTextNodes)for(f=0,d=(_=e.children).length;f<d;f++)if(((s=_[f]).type===r.Text||s.type===r.Raw||s.type===r.CData)&&null!=s.value){t.suppressPrettyCount++,y=!0;break}for(b+=">"+this.endline(e,t,i),t.state=n.InsideTag,c=0,p=(E=e.children).length;c<p;c++)s=E[c],b+=this.writeChildNode(s,t,i+1);t.state=n.CloseTag,b+=this.indent(e,t,i)+"</"+e.name+">",y&&t.suppressPrettyCount--,b+=this.endline(e,t,i),t.state=n.None}else b+=">",t.state=n.InsideTag,t.suppressPrettyCount++,y=!0,b+=this.writeChildNode(l,t,i+1),t.suppressPrettyCount--,y=!1,t.state=n.CloseTag,b+="</"+e.name+">"+this.endline(e,t,i);return this.closeNode(e,t,i),b}writeChildNode(e,t,n){switch(e.type){case r.CData:return this.cdata(e,t,n);case r.Comment:return this.comment(e,t,n);case r.Element:return this.element(e,t,n);case r.Raw:return this.raw(e,t,n);case r.Text:return this.text(e,t,n);case r.ProcessingInstruction:return this.processingInstruction(e,t,n);case r.Dummy:return"";case r.Declaration:return this.declaration(e,t,n);case r.DocType:return this.docType(e,t,n);case r.AttributeDeclaration:return this.dtdAttList(e,t,n);case r.ElementDeclaration:return this.dtdElement(e,t,n);case r.EntityDeclaration:return this.dtdEntity(e,t,n);case r.NotationDeclaration:return this.dtdNotation(e,t,n);default:throw new Error("Unknown XML node type: "+e.constructor.name)}}processingInstruction(e,t,r){var i;return this.openNode(e,t,r),t.state=n.OpenTag,i=this.indent(e,t,r)+"<?",t.state=n.InsideTag,i+=e.target,e.value&&(i+=" "+e.value),t.state=n.CloseTag,i+=t.spaceBeforeSlash+"?>",i+=this.endline(e,t,r),t.state=n.None,this.closeNode(e,t,r),i}raw(e,t,r){var i;return this.openNode(e,t,r),t.state=n.OpenTag,i=this.indent(e,t,r),t.state=n.InsideTag,i+=e.value,t.state=n.CloseTag,i+=this.endline(e,t,r),t.state=n.None,this.closeNode(e,t,r),i}text(e,t,r){var i;return this.openNode(e,t,r),t.state=n.OpenTag,i=this.indent(e,t,r),t.state=n.InsideTag,i+=e.value,t.state=n.CloseTag,i+=this.endline(e,t,r),t.state=n.None,this.closeNode(e,t,r),i}dtdAttList(e,t,r){var i;return this.openNode(e,t,r),t.state=n.OpenTag,i=this.indent(e,t,r)+"<!ATTLIST",t.state=n.InsideTag,i+=" "+e.elementName+" "+e.attributeName+" "+e.attributeType,"#DEFAULT"!==e.defaultValueType&&(i+=" "+e.defaultValueType),e.defaultValue&&(i+=' "'+e.defaultValue+'"'),t.state=n.CloseTag,i+=t.spaceBeforeSlash+">"+this.endline(e,t,r),t.state=n.None,this.closeNode(e,t,r),i}dtdElement(e,t,r){var i;return this.openNode(e,t,r),t.state=n.OpenTag,i=this.indent(e,t,r)+"<!ELEMENT",t.state=n.InsideTag,i+=" "+e.name+" "+e.value,t.state=n.CloseTag,i+=t.spaceBeforeSlash+">"+this.endline(e,t,r),t.state=n.None,this.closeNode(e,t,r),i}dtdEntity(e,t,r){var i;return this.openNode(e,t,r),t.state=n.OpenTag,i=this.indent(e,t,r)+"<!ENTITY",t.state=n.InsideTag,e.pe&&(i+=" %"),i+=" "+e.name,e.value?i+=' "'+e.value+'"':(e.pubID&&e.sysID?i+=' PUBLIC "'+e.pubID+'" "'+e.sysID+'"':e.sysID&&(i+=' SYSTEM "'+e.sysID+'"'),e.nData&&(i+=" NDATA "+e.nData)),t.state=n.CloseTag,i+=t.spaceBeforeSlash+">"+this.endline(e,t,r),t.state=n.None,this.closeNode(e,t,r),i}dtdNotation(e,t,r){var i;return this.openNode(e,t,r),t.state=n.OpenTag,i=this.indent(e,t,r)+"<!NOTATION",t.state=n.InsideTag,i+=" "+e.name,e.pubID&&e.sysID?i+=' PUBLIC "'+e.pubID+'" "'+e.sysID+'"':e.pubID?i+=' PUBLIC "'+e.pubID+'"':e.sysID&&(i+=' SYSTEM "'+e.sysID+'"'),t.state=n.CloseTag,i+=t.spaceBeforeSlash+">"+this.endline(e,t,r),t.state=n.None,this.closeNode(e,t,r),i}openNode(e,t,r){}closeNode(e,t,r){}openAttribute(e,t,r){}closeAttribute(e,t,r){}}}).call(this)},{"./NodeType":124,"./Utility":125,"./WriterState":126,"./XMLCData":128,"./XMLComment":130,"./XMLDTDAttList":135,"./XMLDTDElement":136,"./XMLDTDEntity":137,"./XMLDTDNotation":138,"./XMLDeclaration":139,"./XMLDocType":140,"./XMLDummy":143,"./XMLElement":144,"./XMLProcessingInstruction":148,"./XMLRaw":149,"./XMLText":153}],155:[function(e,t,r){(function(){var r,n,i,o,a,s,u,l,f;({assign:l,isFunction:f}=e("./Utility")),i=e("./XMLDOMImplementation"),o=e("./XMLDocument"),a=e("./XMLDocumentCB"),u=e("./XMLStringWriter"),s=e("./XMLStreamWriter"),r=e("./NodeType"),n=e("./WriterState"),t.exports.create=function(e,t,r,n){var i,a;if(null==e)throw new Error("Root element needs a name.");return n=l({},t,r,n),a=(i=new o(n)).element(e),n.headless||(i.declaration(n),null==n.pubID&&null==n.sysID||i.dtd(n)),a},t.exports.begin=function(e,t,r){return f(e)&&([t,r]=[e,t],e={}),t?new a(e,t,r):new o(e)},t.exports.stringWriter=function(e){return new u(e)},t.exports.streamWriter=function(e,t){return new s(e,t)},t.exports.implementation=new i,t.exports.nodeType=r,t.exports.writerState=n}).call(this)},{"./NodeType":124,"./Utility":125,"./WriterState":126,"./XMLDOMImplementation":133,"./XMLDocument":141,"./XMLDocumentCB":142,"./XMLStreamWriter":150,"./XMLStringWriter":151}]},{},[2])(2)});
//# sourceMappingURL=./dist/app-info-parser.mini.js.map