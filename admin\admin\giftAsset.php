<?php
session_start();
?>
<?php
include_once 'connect.php';
$uid = $_GET['uid'];
$ipres2 = mysqli_query($connect, "SELECT * FROM typecho_users WHERE uid = '$uid'");
$userdata = mysqli_fetch_array($ipres2);
?>

<?php
include_once 'Nav.php';
?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">用户积分充扣</h4>

                <form class="needs-validation" action="giftAssetPost.php" method="post" novalidate onsubmit="return check()">
                    <div class="form-group mb-3">
                        <label for="validationCustom03">UID</label>
                        <?php if(isset($_GET['uid'])){
                            echo '<input id="validationCustom03" type="number" class="form-control"
                                value="'.$uid.'" name="uid" readonly>';
                        } else {
                            echo '<input id="validationCustom03" type="number" class="form-control"
                                value="" name="uid" required>';
                        }
                        ?>
                        
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom05">积分数量</label>
                        <input type="number" class="form-control" id="validationCustom05" placeholder="输入积分变动数量"
                               name="assest" value="" required>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">变动方式</label>
                        <select class="form-control" id="example-select" name="way">
                             <option value="add" selected>充值</option>
                              <option value="sub">扣除</option>
                        </select>
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success"  type="submit" id="giftAssetPost">立即执行</button>
                    </div>
                   
                </form>

            </div>  
        </div> 
    </div>  
</div>
<script>
     function check() {
       
        let uid = document.getElementsByName('uid')[0].value.trim();
        let assest = document.getElementsByName('assest')[0].value.trim();
        
        if (uid.length == 0) {
            alert("请输入用户UID");
            return false;
        }
        
        if (assest.length == 0) {
            alert("请输入积分变动");
            return false;
        }
    }
</script>
<?php
include_once 'Footer.php';
?>

</body>
</html>