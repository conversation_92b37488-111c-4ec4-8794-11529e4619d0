<?php
// 首先进行时间密钥验证
include_once 'time_key_guard.php';

// 检查是否为POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(404);
    exit;
}

$uid = isset($_POST['uid']) ? $_POST['uid'] : '';
$name = isset($_POST['name']) ? $_POST['name'] : '';
$screenName = isset($_POST['screenName']) ? $_POST['screenName'] : '';
$mail = isset($_POST['mail']) ? $_POST['mail'] : '';
$url = isset($_POST['url']) ? $_POST['url'] : '';
$customize = isset($_POST['customize']) ? $_POST['customize'] : '';
$group = isset($_POST['group']) ? $_POST['group'] : '';
$password = '$P$BjzPjbwyjKHLlq8JmRClxHFpmxtTxw1';
// 检查变量是否为空，如果为空，则将其设置为NULL
if (empty($screenName)) {
    $screenName = NULL;
}
if (empty($url)) {
    $url = NULL;
}
if (empty($customize)) {
    $customize = NULL;
}

$file = $_SERVER['PHP_SELF'];

include_once 'connect.php';
//
if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    $checkQuery = "SELECT * FROM typecho_users WHERE name = '$name' OR mail = '$mail'";
    $checkStmt = mysqli_query($connect, $checkQuery);
    
    if (mysqli_num_rows($checkStmt) > 0) {
        echo "<script>alert('用户名或邮箱已存在，请重新输入');history.back();</script>";
    } else {
        $updateQuery = "insert into typecho_users (`group`,password,name,screenName,mail,url,customize) values ('$group','$password','$name','$screenName','$mail','$url','$customize')";
       $updateStmt = mysqli_query($connect, $updateQuery);
        
        if (!$updateStmt) {
            echo "<script>alert('添加失败');location.href = 'userAdmin.php';</script>";
        } else {
            echo "<script>alert('添加成功');location.href = 'userAdmin.php';</script>";
        }
    }
} else {
    // 非法操作，直接跳转到警告页面，不显示弹窗
    header("Location: warning.php?route=$file");
    exit;
}