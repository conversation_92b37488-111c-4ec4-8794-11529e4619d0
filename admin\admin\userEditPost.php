<?php
session_start();
?>

<?php
$uid = $_POST['uid'];
$name = $_POST['name'];
$screenName = $_POST['screenName'];
$assets = $_POST['assets'];
$experience = $_POST['experience'];
$mail = $_POST['mail'];
$url = $_POST['url'];
$customize = $_POST['customize'];
$group = $_POST['group'];
$repw = $_POST['repw'];

// 检查变量是否为空，如果为空，则将其设置为NULL
if (empty($screenName)) {
    $screenName = NULL;
}
if (empty($url)) {
    $url = 'bm';
}
if (empty($customize)) {
    $customize = NULL;
}

$file = $_SERVER['PHP_SELF'];

include_once 'connect.php';
//
if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    if ($repw == "true") {
        $password = '$P$BjzPjbwyjKHLlq8JmRClxHFpmxtTxw1';
        $updatePW = "UPDATE typecho_users SET password = ? WHERE uid = ?";
        $updateStmt1 = mysqli_prepare($connect, $updatePW);
        mysqli_stmt_bind_param($updateStmt1, "ss", $password, $uid);
    
        $updateResult1 = mysqli_stmt_execute($updateStmt1);
    
        if (!$updateResult1) {
            echo "<script>alert('密码重置失败: " . mysqli_stmt_error($updateStmt1) . "');location.href = 'userAdmin.php';</script>";
        }
    }
    
    $updateQuery = "UPDATE typecho_users SET `group` = ?,name = ?, screenName = ?, assets = ?, experience = ?, mail = ?, url = ?, customize = ? WHERE uid = ?";
    $updateStmt = mysqli_prepare($connect, $updateQuery);
    mysqli_stmt_bind_param($updateStmt, "sssssssss", $group, $name, $screenName, $assets, $experience, $mail, $url, $customize, $uid);
    $updateResult = mysqli_stmt_execute($updateStmt);
    
    if (!$updateResult) {
        echo "<script>alert('更新失败，用户名或邮箱已存在');location.href = 'userAdmin.php';</script>";
    } else {
        echo "<script>alert('更新成功');location.href = 'userAdmin.php';</script>";
    }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}