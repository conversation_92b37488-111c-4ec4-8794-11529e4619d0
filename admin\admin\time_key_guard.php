<?php
// 时间密钥守护模块 - 统一验证访问权限
// 关闭错误显示，防止暴露文件路径
error_reporting(0);
ini_set('display_errors', 0);

session_start();

// 检查是否已经通过时间密钥验证
if (!isset($_SESSION['time_key_verified']) || $_SESSION['time_key_verified'] !== true) {
    // 检查是否有时间密钥验证的会话标记
    $current_time = time();
    $session_time = isset($_SESSION['time_key_verify_time']) ? $_SESSION['time_key_verify_time'] : 0;

    // 验证会话是否在有效期内（30分钟）
    if (($current_time - $session_time) > 1800) {
        // 会话过期或未验证，清除会话并返回404
        unset($_SESSION['time_key_verified']);
        unset($_SESSION['time_key_verify_time']);

        // 返回404错误，让黑客以为页面不存在
        http_response_code(404);
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <title>404 Not Found</title>
            <style>
                body { font-family: Arial, sans-serif; text-align: center; margin-top: 100px; }
                h1 { color: #666; }
                p { color: #999; }
            </style>
        </head>
        <body>
            <h1>404 Not Found</h1>
            <p>The requested resource was not found on this server.</p>
        </body>
        </html>
        <?php
        exit;
    }
}

// 如果通过验证，继续执行后续代码
?>
