# StarPro Service Manager GUI - Chinese Version
# PowerShell Windows Forms Application

# Set console encoding to UTF-8 for proper Chinese character support
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# Define Chinese UI text strings to avoid encoding issues
$ChineseTexts = @{
    WindowTitle = "StarPro " + [char]26381 + [char]21153 + [char]31649 + [char]29702 + [char]22120  # "StarPro 服务管理器"
    StatusChecking = [char]26381 + [char]21153 + [char]29366 + [char]24577 + ": " + [char]26816 + [char]26597 + [char]20013 + "..."  # "服务状态: 检查中..."
    StartAPI = [char]21551 + [char]21160 + " API"  # "启动 API"
    StopAPI = [char]20572 + [char]27490 + " API"   # "停止 API"
    RebuildAPI = [char]37325 + [char]26500 + " API"  # "重构 API"
    StartAdmin = [char]21551 + [char]21160 + [char]31649 + [char]29702  # "启动管理"
    StopAdmin = [char]20572 + [char]27490 + [char]31649 + [char]29702  # "停止管理"
    StartAll = [char]19968 + [char]38190 + [char]21551 + [char]21160  # "一键启动"
    StopAll = [char]19968 + [char]38190 + [char]20572 + [char]27490  # "一键停止"
    CheckStatus = [char]26816 + [char]26597 + [char]29366 + [char]24577  # "检查状态"
    OpenAPI = [char]25171 + [char]24320 + " API"  # "打开 API"
    OpenAdmin = [char]25171 + [char]24320 + [char]31649 + [char]29702  # "打开管理"
    Exit = [char]36864 + [char]20986  # "退出"
    OperationLog = [char]25805 + [char]20316 + [char]26085 + [char]24535 + ":"  # "操作日志:"
    Processing = [char]22788 + [char]29702 + [char]20013 + "..."  # "处理中..."
    Compiling = [char]32534 + [char]35793 + [char]20013 + "..."   # "编译中..."
    
    # Application messages
    AppStarted = "StarPro " + [char]26381 + [char]21153 + [char]31649 + [char]29702 + [char]22120 + [char]24050 + [char]21551 + [char]21160  # "StarPro 服务管理器已启动"
    CurrentDirectory = [char]24403 + [char]21069 + [char]24037 + [char]20316 + [char]30446 + [char]24405 + ":"  # "当前工作目录:"
    CheckingStatus = [char]27491 + [char]22312 + [char]26816 + [char]26597 + [char]26381 + [char]21153 + [char]29366 + [char]24577 + "..."  # "正在检查服务状态..."
    StatusCompleted = [char]26381 + [char]21153 + [char]29366 + [char]24577 + [char]26816 + [char]26597 + [char]23436 + [char]25104 + ":"  # "服务状态检查完成:"
    StartingTask = [char]27491 + [char]22312 + [char]25191 + [char]34892 + ":"  # "正在执行:"
    StartingAPI = [char]27491 + [char]22312 + [char]21551 + [char]21160 + " API " + [char]26381 + [char]21153 + "..."  # "正在启动 API 服务..."
    StoppingAPI = [char]27491 + [char]22312 + [char]20572 + [char]27490 + " API " + [char]26381 + [char]21153 + "..."  # "正在停止 API 服务..."
    StartingAdmin = [char]27491 + [char]22312 + [char]21551 + [char]21160 + [char]31649 + [char]29702 + [char]26381 + [char]21153 + "..."  # "正在启动管理服务..."
    StoppingAdmin = [char]27491 + [char]22312 + [char]20572 + [char]27490 + [char]31649 + [char]29702 + [char]26381 + [char]21153 + "..."  # "正在停止管理服务..."
    StartingAll = [char]27491 + [char]22312 + [char]19968 + [char]38190 + [char]21551 + [char]21160 + [char]25152 + [char]26377 + [char]26381 + [char]21153 + "..."  # "正在一键启动所有服务..."
    StoppingAll = [char]27491 + [char]22312 + [char]19968 + [char]38190 + [char]20572 + [char]27490 + [char]25152 + [char]26377 + [char]26381 + [char]21153 + "..."  # "正在一键停止所有服务..."
    RebuildingAPI = [char]27491 + [char]22312 + [char]37325 + [char]26500 + " API " + [char]26381 + [char]21153 + "..."  # "正在重构 API 服务..."
    TaskCompleted = [char]20219 + [char]21153 + " '{0}' " + [char]23436 + [char]25104 + [char]25104 + [char]21151  # "任务 '{0}' 完成成功"
    TaskFailed = [char]20219 + [char]21153 + " '{0}' " + [char]25191 + [char]34892 + [char]22833 + [char]36133  # "任务 '{0}' 执行失败"
    TaskFailedWithError = [char]20219 + [char]21153 + " '{0}' " + [char]25191 + [char]34892 + [char]22833 + [char]36133 + ": {1}"  # "任务 '{0}' 执行失败: {1}"
    OpeningURL = [char]27491 + [char]22312 + [char]25171 + [char]24320 + " {0} " + [char]22320 + [char]22336 + "..."  # "正在打开 {0} 地址..."
    AppClosing = [char]24212 + [char]29992 + [char]31243 + [char]24207 + [char]20851 + [char]38381 + [char]65292 + [char]21518 + [char]21488 + [char]20219 + [char]21153 + [char]24050 + [char]28165 + [char]29702  # "应用程序关闭，后台任务已清理"
    
    # Service status messages
    APIService = "API " + [char]26381 + [char]21153  # "API 服务"
    AdminService = [char]31649 + [char]29702 + [char]26381 + [char]21153  # "管理服务"
    ServiceRunning = [char]36816 + [char]34892 + [char]20013  # "运行中"
    ServiceStopped = [char]24050 + [char]20572 + [char]27490  # "已停止"
    
    # Error messages
    ErrorUpdatingButtonStates = [char]26356 + [char]26032 + [char]25353 + [char]38062 + [char]29366 + [char]24577 + [char]26102 + [char]20986 + [char]29616 + [char]38169 + [char]35823  # "更新按钮状态时出现错误"
    ErrorInStatusUpdate = [char]29366 + [char]24577 + [char]26356 + [char]26032 + [char]29992 + [char]25143 + [char]38754 + [char]20013 + [char]20986 + [char]29616 + [char]38169 + [char]35823  # "状态更新用户界面中出现错误"
    
    # UI Section Labels
    ServiceControl = [char]26381 + [char]21153 + [char]25511 + [char]21046  # "服务控制"
    UtilityTools = [char]23454 + [char]29992 + [char]24037 + [char]20855  # "实用工具"
}

Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# Global variables
$script:apiProcess = $null
$script:logTextBox = $null
$script:runningJobs = @()
$script:statusTimer = $null

# Utility functions
function Write-Log {
    param([string]$message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] $message"
    
    if ($script:logTextBox) {
        $script:logTextBox.AppendText("$logMessage`r`n")
        $script:logTextBox.ScrollToCaret()
    }
    
    Write-Host $logMessage
}

function Test-Port {
    param([int]$port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("127.0.0.1", $port)
        $connection.Close()
        return $true
    } catch {
        return $false
    }
}

function Stop-ServiceProcess {
    param(
        [int]$port,
        [string]$serviceName
    )
    
    Write-Log "Stopping $serviceName..."
    
    try {
        # Find and kill processes using the port - avoid XML parsing issues
        try {
            $connections = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
            if ($connections) {
                foreach ($conn in $connections) {
                    try {
                        $processId = $conn.OwningProcess
                        if ($processId) {
                            $proc = Get-Process -Id $processId -ErrorAction SilentlyContinue
                            if ($proc) {
                                Write-Log "Terminating process $($proc.ProcessName) (PID: $($proc.Id))"
                                $proc.Kill()
                                $proc.WaitForExit(5000)
                            }
                        }
                    } catch {
                        Write-Log "Could not terminate process: $($_.Exception.Message)"
                    }
                }
            }
        } catch {
            Write-Log "Error accessing network connections: $($_.Exception.Message)"
        }
        
        # Kill all Java processes as a simple fallback (safer than WMI/CIM queries)
        $javaProcesses = Get-Process -Name "java" -ErrorAction SilentlyContinue
        foreach ($javaProc in $javaProcesses) {
            Write-Log "Terminating Java process (PID: $($javaProc.Id))"
            try {
                $javaProc.Kill()
                $javaProc.WaitForExit(5000)
            } catch {
                Write-Log "Could not terminate Java process: $($_.Exception.Message)"
            }
        }
        
        Start-Sleep -Seconds 2
        
        if (-not (Test-Port $port)) {
            Write-Log "$serviceName stopped successfully"
        } else {
            Write-Log "$serviceName may still be running"
        }
    } catch {
        Write-Log "Error stopping ${serviceName}: $($_.Exception.Message)"
    }
}

function Check-ServiceStatus {
    Write-Log $ChineseTexts.CheckingStatus
    
    $apiStatus = if (Test-Port 7979) { $ChineseTexts.ServiceRunning } else { $ChineseTexts.ServiceStopped }
    $adminStatus = if (Test-Port 6969) { $ChineseTexts.ServiceRunning } else { $ChineseTexts.ServiceStopped }
    
    # Update status label
    $statusText = "$($ChineseTexts.APIService)(7979): $apiStatus | $($ChineseTexts.AdminService)(6969): $adminStatus"
    $script:statusLabel.Text = $statusText
    
    # Modern color coding with improved status display
    if ($apiStatus -eq $ChineseTexts.ServiceRunning -and $adminStatus -eq $ChineseTexts.ServiceRunning) {
        $script:statusLabel.ForeColor = $ModernColors.Success
    } elseif ($apiStatus -eq $ChineseTexts.ServiceRunning -or $adminStatus -eq $ChineseTexts.ServiceRunning) {
        $script:statusLabel.ForeColor = $ModernColors.Warning
    } else {
        $script:statusLabel.ForeColor = $ModernColors.Error
    }
    
    # Update button texts based on service status
    Update-ButtonStates
    
    Write-Log ($ChineseTexts.StatusCompleted + " " + $statusText)
}

function Update-ButtonStates {
    # Update API button text
    if (Test-Port 7979) {
        $script:startApiBtn.Text = $ChineseTexts.StopAPI
    } else {
        $script:startApiBtn.Text = $ChineseTexts.StartAPI
    }
    
    # Update Admin button text
    if (Test-Port 6969) {
        $script:startAdminBtn.Text = $ChineseTexts.StopAdmin
    } else {
        $script:startAdminBtn.Text = $ChineseTexts.StartAdmin
    }
    
    # Update Start All button text
    $apiRunning = Test-Port 7979
    $adminRunning = Test-Port 6969
    if ($apiRunning -and $adminRunning) {
        $script:startAllBtn.Text = $ChineseTexts.StopAll
    } else {
        $script:startAllBtn.Text = $ChineseTexts.StartAll
    }
}

# Service management functions
function Start-ApiService {
    Write-Log "Starting API service..."
    
    if (Test-Port 7979) {
        Write-Log "API service is already running"
        return
    }
    
    $jarPath = "API\target\StarProApi-2.6.jar"
    if (-not (Test-Path $jarPath)) {
        Write-Log "Error: $jarPath not found, please compile the project first"
        return
    }
    
    try {
        $processInfo = New-Object System.Diagnostics.ProcessStartInfo
        $processInfo.FileName = "java"
        $processInfo.Arguments = "-jar `"$jarPath`""
        $processInfo.WorkingDirectory = $PWD.Path
        $processInfo.UseShellExecute = $false
        $processInfo.CreateNoWindow = $true
        
        $script:apiProcess = [System.Diagnostics.Process]::Start($processInfo)
        Write-Log "API service starting... (PID: $($script:apiProcess.Id))"
        
        # Wait for service to start
        Start-Sleep -Seconds 3
        
        if (Test-Port 7979) {
            Write-Log "API service started successfully, URL: http://localhost:7979"
        } else {
            Write-Log "API service may have failed to start, please check logs"
        }
    } catch {
        Write-Log "Error starting API service: $($_.Exception.Message)"
    }
}

function Restart-ApiService {
    Write-Log "Restarting API service..."
    Stop-ServiceProcess -port 7979 -serviceName "API Service"
    Start-Sleep -Seconds 2
    Start-ApiService
}

# Rebuild and start API
function Rebuild-ApiService {
    Write-Log "Rebuilding API service..."
    
    # Stop existing service first
    Stop-ServiceProcess -port 7979 -serviceName "API Service"
    Start-Sleep -Seconds 2
    
    try {
        # Execute Maven compile
        Write-Log "Starting Maven compilation..."
        $process = Start-Process -FilePath "mvn" -ArgumentList "clean", "package", "-DskipTests" -WorkingDirectory "API" -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0) {
            Write-Log "Maven compilation successful"
            Start-Sleep -Seconds 1
            Start-ApiService
        } else {
            Write-Log "Maven compilation failed, exit code: $($process.ExitCode)"
        }
    } catch {
        Write-Log "Error during compilation: $($_.Exception.Message)"
    }
}

# Start admin service
function Start-AdminService {
    Write-Log "Starting admin service..."
    
    if (Test-Port 6969) {
        Write-Log "Admin service is already running"
        return
    }
    
    # Check if PHP is installed
    try {
        $phpVersion = php -v 2>$null
        if (-not $phpVersion) {
            Write-Log "[Error] PHP not found, please install PHP first"
            return
        }
    } catch {
        Write-Log "[Error] PHP not found, please install PHP first"
        return
    }
    
    # Check if admin directory exists
    if (-not (Test-Path "admin")) {
        Write-Log "[Error] Admin directory not found"
        return
    }
    
    try {
        Write-Log "Starting PHP built-in server for admin panel..."
        $processInfo = New-Object System.Diagnostics.ProcessStartInfo
        $processInfo.FileName = "php"
        $processInfo.Arguments = "-S 0.0.0.0:6969 -t admin"
        $processInfo.WorkingDirectory = $PWD.Path
        $processInfo.UseShellExecute = $false
        $processInfo.CreateNoWindow = $true
        
        $script:adminProcess = [System.Diagnostics.Process]::Start($processInfo)
        Write-Log "Admin service starting... (PID: $($script:adminProcess.Id))"
        
        # Wait for service to start
        Start-Sleep -Seconds 3
        
        if (Test-Port 6969) {
            Write-Log "Admin service started successfully, URL: http://localhost:6969"
        } else {
            Write-Log "Admin service may have failed to start, please check logs"
        }
    } catch {
        Write-Log "Error starting admin service: $($_.Exception.Message)"
    }
}

# Stop admin service
function Stop-AdminService {
    Write-Log "Stopping admin service..."
    Stop-ServiceProcess -port 6969 -serviceName "Admin Service"
}

# Start all services
function Start-AllServices {
    Write-Log "Starting all services..."
    Start-ApiService
    Start-AdminService
}

# Stop all services
function Stop-AllServices {
    Write-Log "Stopping all services..."
    Stop-ServiceProcess -port 7979 -serviceName "API Service"
    Stop-ServiceProcess -port 6969 -serviceName "Admin Service"
}

function Open-ApiUrl {
    Write-Log ([string]::Format($ChineseTexts.OpeningURL, "API"))
    Start-Process "http://localhost:7979"
}

function Open-AdminUrl {
    Write-Log ([string]::Format($ChineseTexts.OpeningURL, $ChineseTexts.AdminService))
    Start-Process "http://localhost:6969"
}

# Async job management functions
function Start-AsyncJob {
    param(
        [ScriptBlock]$ScriptBlock,
        [string]$JobName,
        [System.Windows.Forms.Button]$Button,
        [string]$ProcessingText,
        [string]$OriginalText,
        [string]$ActionMessage = ""
    )
    
    # Disable button and show processing status
    $Button.Enabled = $false
    $Button.Text = $ProcessingText
    [System.Windows.Forms.Application]::DoEvents()
    
    # Use specific action message if provided, otherwise use generic message
    if ($ActionMessage) {
        Write-Log $ActionMessage
    } else {
        Write-Log ($ChineseTexts.StartingTask + " $JobName")
    }
    
    # Start the job
    $job = Start-Job -ScriptBlock $ScriptBlock -Name $JobName
    
    # Add to running jobs list
    $jobInfo = [PSCustomObject]@{
        Job = $job
        Button = $Button
        OriginalText = $OriginalText
        JobName = $JobName
        IsStatusUpdate = $false
    }
    $script:runningJobs += $jobInfo
    
    # Start monitoring timer if not already running
    if ($script:statusTimer -eq $null) {
        Start-JobMonitorTimer
    }
}

function Start-JobMonitorTimer {
    $script:statusTimer = New-Object System.Windows.Forms.Timer
    $script:statusTimer.Interval = 250  # Check every 250ms for faster response
    $script:statusTimer.Add_Tick({
        Check-JobStatus
    })
    $script:statusTimer.Start()
}

function Update-ButtonStatesAsync {
    # Create a quick non-blocking status check
    $quickStatusJob = Start-Job -ScriptBlock {
        function Test-Port {
            param([int]$port, [int]$timeoutMs = 300)
            try {
                $connection = New-Object System.Net.Sockets.TcpClient
                $task = $connection.ConnectAsync("127.0.0.1", $port)
                if ($task.Wait($timeoutMs)) {
                    $connection.Close()
                    return $true
                } else {
                    $connection.Close()
                    return $false
                }
            } catch {
                return $false
            }
        }
        
        $apiStatus = if (Test-Port 7979 300) { $using:ChineseTexts.ServiceRunning } else { $using:ChineseTexts.ServiceStopped }
        $adminStatus = if (Test-Port 6969 300) { $using:ChineseTexts.ServiceRunning } else { $using:ChineseTexts.ServiceStopped }
        
        return @{
            ApiStatus = $apiStatus
            AdminStatus = $adminStatus
        }
    }
    
    # Wait for quick result and update immediately
    $timeout = 0
    while ($quickStatusJob.State -eq "Running" -and $timeout -lt 20) {  # Max 1 second wait
        Start-Sleep -Milliseconds 50
        $timeout++
    }
    
    if ($quickStatusJob.State -eq "Completed") {
        $statusResult = Receive-Job -Job $quickStatusJob
        
        if ($statusResult) {
            # Update UI immediately on main thread
            try {
                $script:statusLabel.Text = "$($ChineseTexts.APIService)(7979): $($statusResult.ApiStatus) | $($ChineseTexts.AdminService)(6969): $($statusResult.AdminStatus)"
                
                if ($statusResult.ApiStatus -eq $ChineseTexts.ServiceRunning -and $statusResult.AdminStatus -eq $ChineseTexts.ServiceRunning) {
                    $script:statusLabel.ForeColor = $ModernColors.Success
                } elseif ($statusResult.ApiStatus -eq $ChineseTexts.ServiceRunning -or $statusResult.AdminStatus -eq $ChineseTexts.ServiceRunning) {
                    $script:statusLabel.ForeColor = $ModernColors.Warning
                } else {
                    $script:statusLabel.ForeColor = $ModernColors.Error
                }
                
                # Update button texts
                if ($statusResult.ApiStatus -eq $ChineseTexts.ServiceRunning) {
                    $script:startApiBtn.Text = $ChineseTexts.StopAPI
                } else {
                    $script:startApiBtn.Text = $ChineseTexts.StartAPI
                }
                
                if ($statusResult.AdminStatus -eq $ChineseTexts.ServiceRunning) {
                    $script:startAdminBtn.Text = $ChineseTexts.StopAdmin
                } else {
                    $script:startAdminBtn.Text = $ChineseTexts.StartAdmin
                }
                
                if ($statusResult.ApiStatus -eq $ChineseTexts.ServiceRunning -and $statusResult.AdminStatus -eq $ChineseTexts.ServiceRunning) {
                    $script:startAllBtn.Text = $ChineseTexts.StopAll
                } else {
                    $script:startAllBtn.Text = $ChineseTexts.StartAll
                }
                

            } catch {
                Write-Log ($ChineseTexts.ErrorUpdatingButtonStates + ": " + $_.Exception.Message)
            }
        }
    }
    
    # Clean up the job
    Remove-Job -Job $quickStatusJob -Force -ErrorAction SilentlyContinue
}

function Start-AsyncStatusUpdate {
    # Clean up any completed status update jobs first - avoid XML parsing with pipeline
    try {
        $statusJobs = Get-Job -Name "StatusUpdate*" -ErrorAction SilentlyContinue
        if ($statusJobs) {
            foreach ($job in $statusJobs) {
                try {
                    if ($job.State -ne "Running") {
                        Remove-Job -Job $job -Force -ErrorAction SilentlyContinue
                    }
                } catch {
                    # Continue with next job
                }
            }
        }
    } catch {
        # Ignore cleanup errors
    }
    
    # Only start if there isn't already a status update job running
    $statusUpdateJob = Get-Job -Name "StatusUpdate" -ErrorAction SilentlyContinue
    if ($statusUpdateJob -and $statusUpdateJob.State -eq "Running") {
        return
    }
    
    $statusJob = Start-Job -Name "StatusUpdate" -ScriptBlock {
        function Test-Port {
            param([int]$port, [int]$timeoutMs = 1000)
            try {
                $connection = New-Object System.Net.Sockets.TcpClient
                $task = $connection.ConnectAsync("127.0.0.1", $port)
                if ($task.Wait($timeoutMs)) {
                    $connection.Close()
                    return $true
                } else {
                    $connection.Close()
                    return $false
                }
            } catch {
                return $false
            }
        }
        
        $apiStatus = if (Test-Port 7979 500) { $using:ChineseTexts.ServiceRunning } else { $using:ChineseTexts.ServiceStopped }
        $adminStatus = if (Test-Port 6969 500) { $using:ChineseTexts.ServiceRunning } else { $using:ChineseTexts.ServiceStopped }
        
        return @{
            ApiStatus = $apiStatus
            AdminStatus = $adminStatus
            ApiPort = 7979
            AdminPort = 6969
        }
    }
    
    # Add to monitoring list for cleanup but with special handling
    $jobInfo = [PSCustomObject]@{
        Job = $statusJob
        Button = $null  # No button for status update
        OriginalText = ""
        JobName = "StatusUpdate"
        IsStatusUpdate = $true
    }
    $script:runningJobs += $jobInfo
}

function Check-JobStatus {
    $completedJobs = @()
    
    # Global error handler to prevent XML parsing errors from crashing the monitor
    try {
        foreach ($jobInfo in $script:runningJobs) {
            $job = $jobInfo.Job
            
            # Safe job state check to avoid XML parsing issues
            $jobState = $null
            try {
                $jobState = $job.State
            } catch {
                # If we can't even get the job state, consider it failed
                $jobState = "Failed"
            }
            
            if ($jobState -eq "Completed" -or $jobState -eq "Failed" -or $jobState -eq "Stopped") {
                
                if ($jobInfo.IsStatusUpdate -eq $true) {
                # Handle status update job
                if ($jobState -eq "Completed") {
                    try {
                        $statusResult = Receive-Job -Job $job -ErrorAction SilentlyContinue
                    } catch {
                        Write-Log "Error receiving status update job results: $($_.Exception.Message)"
                        $statusResult = $null
                    }
                    if ($statusResult) {
                        try {
                            # Update UI on main thread using Invoke
                            $script:statusLabel.Invoke([Action]{
                                $statusText = "$($ChineseTexts.APIService)($($statusResult.ApiPort)): $($statusResult.ApiStatus) | $($ChineseTexts.AdminService)($($statusResult.AdminPort)): $($statusResult.AdminStatus)"
                                $script:statusLabel.Text = $statusText
                                
                                if ($statusResult.ApiStatus -eq $ChineseTexts.ServiceRunning -and $statusResult.AdminStatus -eq $ChineseTexts.ServiceRunning) {
                                    $script:statusLabel.ForeColor = $ModernColors.Success
                                } elseif ($statusResult.ApiStatus -eq $ChineseTexts.ServiceRunning -or $statusResult.AdminStatus -eq $ChineseTexts.ServiceRunning) {
                                    $script:statusLabel.ForeColor = $ModernColors.Warning
                                } else {
                                    $script:statusLabel.ForeColor = $ModernColors.Error
                                }
                            })
                            
                            # Update button texts on main thread
                            $script:startApiBtn.Invoke([Action]{
                                if ($statusResult.ApiStatus -eq $ChineseTexts.ServiceRunning) {
                                    $script:startApiBtn.Text = $ChineseTexts.StopAPI
                                } else {
                                    $script:startApiBtn.Text = $ChineseTexts.StartAPI
                                }
                            })
                            
                            $script:startAdminBtn.Invoke([Action]{
                                if ($statusResult.AdminStatus -eq $ChineseTexts.ServiceRunning) {
                                    $script:startAdminBtn.Text = $ChineseTexts.StopAdmin
                                } else {
                                    $script:startAdminBtn.Text = $ChineseTexts.StartAdmin
                                }
                            })
                            
                            $script:startAllBtn.Invoke([Action]{
                                if ($statusResult.ApiStatus -eq $ChineseTexts.ServiceRunning -and $statusResult.AdminStatus -eq $ChineseTexts.ServiceRunning) {
                                    $script:startAllBtn.Text = $ChineseTexts.StopAll
                                } else {
                                    $script:startAllBtn.Text = $ChineseTexts.StartAll
                                }
                            })
                            

                        } catch {
                            Write-Log ($ChineseTexts.ErrorInStatusUpdate + ": " + $_.Exception.Message)
                        }
                    }
                }
            } else {
                # Handle regular job completion
                $button = $jobInfo.Button
                $originalText = $jobInfo.OriginalText
                $jobName = $jobInfo.JobName
                
                # Get job output if any
                if ($jobState -eq "Completed") {
                    try {
                        $jobOutput = Receive-Job -Job $job -ErrorAction SilentlyContinue
                    } catch {
                        Write-Log "Error receiving job output: $($_.Exception.Message)"
                        $jobOutput = $null
                    }
                    
                    # Handle special case for StatusCheck job
                    if ($jobName -eq "StatusCheck" -and $jobOutput -and $jobOutput.StatusText) {
                        # Update status label immediately
                        $script:statusLabel.Text = $jobOutput.StatusText
                        
                        # Update status colors
                        if ($jobOutput.ApiStatus -eq $ChineseTexts.ServiceRunning -and $jobOutput.AdminStatus -eq $ChineseTexts.ServiceRunning) {
                            $script:statusLabel.ForeColor = $ModernColors.Success
                        } elseif ($jobOutput.ApiStatus -eq $ChineseTexts.ServiceRunning -or $jobOutput.AdminStatus -eq $ChineseTexts.ServiceRunning) {
                            $script:statusLabel.ForeColor = $ModernColors.Warning
                        } else {
                            $script:statusLabel.ForeColor = $ModernColors.Error
                        }
                        
                        # Update button texts based on service status
                        if ($jobOutput.ApiStatus -eq $ChineseTexts.ServiceRunning) {
                            $script:startApiBtn.Text = $ChineseTexts.StopAPI
                        } else {
                            $script:startApiBtn.Text = $ChineseTexts.StartAPI
                        }
                        
                        if ($jobOutput.AdminStatus -eq $ChineseTexts.ServiceRunning) {
                            $script:startAdminBtn.Text = $ChineseTexts.StopAdmin
                        } else {
                            $script:startAdminBtn.Text = $ChineseTexts.StartAdmin
                        }
                        
                        if ($jobOutput.ApiStatus -eq $ChineseTexts.ServiceRunning -and $jobOutput.AdminStatus -eq $ChineseTexts.ServiceRunning) {
                            $script:startAllBtn.Text = $ChineseTexts.StopAll
                        } else {
                            $script:startAllBtn.Text = $ChineseTexts.StartAll
                        }
                        
                        Write-Log ($ChineseTexts.StatusCompleted + " " + $jobOutput.StatusText)
                    } else {
                        # Handle regular job output
                        if ($jobOutput) {
                            Write-Log "$jobOutput"
                        }
                        Write-Log ([string]::Format($ChineseTexts.TaskCompleted, $jobName))
                    }
                } elseif ($jobState -eq "Failed") {
                    # Try multiple ways to get error information
                    $errorMessage = $null
                    
                    # Enhanced safe error object to string conversion function
                    function Safe-ToString {
                        param($obj)
                        try {
                            if ($obj -eq $null) { return $null }
                            if ($obj -is [string]) { return $obj }
                            
                            # Handle ErrorRecord objects specifically
                            if ($obj -is [System.Management.Automation.ErrorRecord]) {
                                if ($obj.Exception.Message) { return $obj.Exception.Message }
                                if ($obj.ToString) { 
                                    # Use ToString() with try-catch to avoid XML parsing issues
                                    try { return $obj.ToString() } catch { }
                                }
                                return "ErrorRecord could not be converted to string"
                            }
                            
                            # Handle Exception objects
                            if ($obj -is [System.Exception]) {
                                return $obj.Message
                            }
                            
                            # Handle objects with Message property
                            if ($obj.PSObject.Properties['Message']) {
                                return $obj.Message
                            }
                            
                            # Handle objects with Exception property
                            if ($obj.PSObject.Properties['Exception'] -and $obj.Exception.Message) {
                                return $obj.Exception.Message
                            }
                            
                            # Last resort - try direct string conversion but catch XML parsing errors
                            try {
                                $stringValue = [string]$obj
                                # Check if the string looks like XML that might cause parsing issues
                                if ($stringValue -match '<.*?>') {
                                    return "Error object contains XML content - details suppressed to avoid parsing errors"
                                }
                                return $stringValue
                            } catch {
                                return "Error object could not be converted to string (XML parsing issue avoided)"
                            }
                        } catch {
                            return "Error object could not be converted to string safely: $($_.Exception.Message)"
                        }
                    }
                    
                    # Method 1: Receive-Job with proper error handling
                    try {
                        # Use single Receive-Job call with proper error variable handling
                        $jobOutput = Receive-Job -Job $job -ErrorVariable jobErrors -ErrorAction SilentlyContinue 2>&1
                        if ($jobErrors -and $jobErrors.Count -gt 0) {
                            $errorMessage = Safe-ToString $jobErrors[0]
                        }
                        # Also check if jobOutput contains error records
                        if (-not $errorMessage -and $jobOutput) {
                            foreach ($output in $jobOutput) {
                                if ($output -is [System.Management.Automation.ErrorRecord]) {
                                    $errorMessage = Safe-ToString $output
                                    break
                                }
                            }
                        }
                    } catch { 
                        # Catch any issues with Receive-Job itself
                        $errorMessage = "Error retrieving job results: $($_.Exception.Message)"
                    }
                    
                    # Method 2: Check ChildJobs for errors with safe handling
                    if (-not $errorMessage) {
                        try {
                            foreach ($childJob in $job.ChildJobs) {
                                try {
                                    if ($childJob.Error -and $childJob.Error.Count -gt 0) {
                                        $errorMessage = Safe-ToString $childJob.Error[0]
                                        break
                                    }
                                    if ($childJob.JobStateInfo.Reason) {
                                        $errorMessage = Safe-ToString $childJob.JobStateInfo.Reason
                                        break
                                    }
                                } catch {
                                    # Continue to next child job if this one causes issues
                                    continue
                                }
                            }
                        } catch { 
                            # Ignore errors in child job enumeration
                        }
                    }
                    
                    # Method 3: Check job's reason for failure with safe handling
                    if (-not $errorMessage) {
                        try {
                            if ($job.JobStateInfo -and $job.JobStateInfo.Reason) {
                                $errorMessage = Safe-ToString $job.JobStateInfo.Reason
                            }
                        } catch { 
                            # Ignore errors in JobStateInfo access
                        }
                    }
                    
                    # Method 4: Final fallback - provide a generic error message
                    if (-not $errorMessage) {
                        $errorMessage = "Job failed without detailed error information (error details may contain XML that cannot be safely processed)"
                    }
                    
                    # Log the error with available information
                    if ($errorMessage) {
                        Write-Log ([string]::Format($ChineseTexts.TaskFailedWithError, $jobName, $errorMessage))
                    } else {
                        Write-Log ([string]::Format($ChineseTexts.TaskFailed, $jobName))
                        # Safe debug information extraction
                        try {
                            $hasMoreData = $job.HasMoreData
                            Write-Log "Debug: Job State = $jobState, HasMoreData = $hasMoreData"
                        } catch {
                            Write-Log "Debug: Job State = $jobState, HasMoreData = <unable to retrieve safely>"
                        }
                    }
                }
                
                # Re-enable button and restore original text
                if ($button) {
                    $button.Enabled = $true
                    # For RebuildAPI, always restore text immediately and let status update handle API button
                    $button.Text = $originalText
                }
                
                # Force immediate status update after job completion
                Update-ButtonStatesAsync
                
                # For RebuildAPI, ensure proper button state update with short delay
                if ($jobName -eq "RebuildAPI") {
                    # Give API service time to fully start, then update button states
                    Start-Sleep -Seconds 2
                    Update-ButtonStatesAsync
                }
            }
            
            # Clean up job
            try {
                Remove-Job -Job $job -Force
            } catch {
                # Ignore cleanup errors
                Write-Log "Warning: Could not clean up job $($jobInfo.JobName) safely"
            }
            $completedJobs += $jobInfo
            }
        }
    } catch {
        # Global catch for any XML parsing or other critical errors
        Write-Log "Error in job status monitoring (possibly XML parsing issue): $($_.Exception.Message)"
        Write-Log "Job monitoring will continue with remaining jobs"
    }
    
    # Remove completed jobs from running list
    try {
        foreach ($completedJob in $completedJobs) {
            $script:runningJobs = $script:runningJobs | Where-Object { $_.Job -ne $completedJob.Job }
        }
    } catch {
        Write-Log "Error updating running jobs list: $($_.Exception.Message)"
    }
    
    # Stop timer if no jobs are running
    try {
        if ($script:runningJobs.Count -eq 0 -and $script:statusTimer -ne $null) {
            $script:statusTimer.Stop()
            $script:statusTimer.Dispose()
            $script:statusTimer = $null
        }
    } catch {
        Write-Log "Error stopping job monitor timer: $($_.Exception.Message)"
    }
}

# Modern UI Color Scheme
$ModernColors = @{
    Primary = [System.Drawing.Color]::FromArgb(52, 73, 94)        # Dark Blue-Gray
    Secondary = [System.Drawing.Color]::FromArgb(46, 204, 113)    # Modern Green
    Accent = [System.Drawing.Color]::FromArgb(52, 152, 219)       # Bright Blue
    Background = [System.Drawing.Color]::FromArgb(240, 244, 248)  # Light Gray
    Surface = [System.Drawing.Color]::White
    TextPrimary = [System.Drawing.Color]::FromArgb(45, 55, 72)    # Dark Gray
    TextSecondary = [System.Drawing.Color]::FromArgb(113, 128, 150) # Medium Gray
    Success = [System.Drawing.Color]::FromArgb(34, 197, 94)       # Green
    Warning = [System.Drawing.Color]::FromArgb(251, 146, 60)      # Orange
    Error = [System.Drawing.Color]::FromArgb(239, 68, 68)         # Red
    Border = [System.Drawing.Color]::FromArgb(226, 232, 240)      # Light Border
}

# Create main form with modern design
$form = New-Object System.Windows.Forms.Form
$form.Text = $ChineseTexts.WindowTitle
$form.Size = New-Object System.Drawing.Size(900, 700)
$form.StartPosition = [System.Windows.Forms.FormStartPosition]::CenterScreen
$form.FormBorderStyle = [System.Windows.Forms.FormBorderStyle]::FixedSingle
$form.MaximizeBox = $true
$form.MinimumSize = New-Object System.Drawing.Size(800, 600)
$form.BackColor = $ModernColors.Background
$form.Font = New-Object System.Drawing.Font("Microsoft YaHei UI", 9, [System.Drawing.FontStyle]::Regular)

# Create header panel for title and status
$headerPanel = New-Object System.Windows.Forms.Panel
$headerPanel.Location = New-Object System.Drawing.Point(0, 0)
$headerPanel.Size = New-Object System.Drawing.Size(900, 90)
$headerPanel.BackColor = $ModernColors.Surface
$headerPanel.Anchor = ([System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Left -bor [System.Windows.Forms.AnchorStyles]::Right)
$form.Controls.Add($headerPanel)

# Create title label
$titleLabel = New-Object System.Windows.Forms.Label
$titleLabel.Location = New-Object System.Drawing.Point(30, 15)
$titleLabel.Size = New-Object System.Drawing.Size(400, 25)
$titleLabel.Text = $ChineseTexts.WindowTitle
$titleLabel.Font = New-Object System.Drawing.Font("Microsoft YaHei UI", 14, [System.Drawing.FontStyle]::Bold)
$titleLabel.ForeColor = $ModernColors.Primary
$titleLabel.BackColor = [System.Drawing.Color]::Transparent
$headerPanel.Controls.Add($titleLabel)

# Create modern status label with card-like appearance
$script:statusLabel = New-Object System.Windows.Forms.Label
$script:statusLabel.Location = New-Object System.Drawing.Point(30, 50)
$script:statusLabel.Size = New-Object System.Drawing.Size(820, 25)
$script:statusLabel.Text = $ChineseTexts.StatusChecking
$script:statusLabel.Font = New-Object System.Drawing.Font("Microsoft YaHei UI", 10, [System.Drawing.FontStyle]::Regular)
$script:statusLabel.ForeColor = $ModernColors.TextSecondary
$script:statusLabel.BackColor = [System.Drawing.Color]::Transparent
$headerPanel.Controls.Add($script:statusLabel)

# Modern Button Creation Helper Function
function New-ModernButton {
    param(
        [string]$Text,
        [System.Drawing.Point]$Location,
        [System.Drawing.Size]$Size,
        [string]$Style = "Primary"  # Primary, Secondary, Success, Warning, Error
    )
    
    $button = New-Object System.Windows.Forms.Button
    $button.Text = $Text
    $button.Location = $Location
    $button.Size = $Size
    $button.Font = New-Object System.Drawing.Font("Microsoft YaHei UI", 9, [System.Drawing.FontStyle]::Regular)
    $button.FlatStyle = [System.Windows.Forms.FlatStyle]::Flat
    $button.Cursor = [System.Windows.Forms.Cursors]::Hand
    
    # Set colors based on style
    switch ($Style) {
        "Primary" {
            $button.BackColor = $ModernColors.Primary
            $button.ForeColor = [System.Drawing.Color]::White
            $button.FlatAppearance.BorderColor = $ModernColors.Primary
        }
        "Secondary" {
            $button.BackColor = $ModernColors.Secondary
            $button.ForeColor = [System.Drawing.Color]::White
            $button.FlatAppearance.BorderColor = $ModernColors.Secondary
        }
        "Success" {
            $button.BackColor = $ModernColors.Success
            $button.ForeColor = [System.Drawing.Color]::White
            $button.FlatAppearance.BorderColor = $ModernColors.Success
        }
        "Warning" {
            $button.BackColor = $ModernColors.Warning
            $button.ForeColor = [System.Drawing.Color]::White
            $button.FlatAppearance.BorderColor = $ModernColors.Warning
        }
        "Error" {
            $button.BackColor = $ModernColors.Error
            $button.ForeColor = [System.Drawing.Color]::White
            $button.FlatAppearance.BorderColor = $ModernColors.Error
        }
        "Outline" {
            $button.BackColor = $ModernColors.Surface
            $button.ForeColor = $ModernColors.Primary
            $button.FlatAppearance.BorderColor = $ModernColors.Border
        }
    }
    
    $button.FlatAppearance.BorderSize = 1
    $button.FlatAppearance.MouseDownBackColor = [System.Drawing.Color]::FromArgb(
        [Math]::Max(0, $button.BackColor.R - 20),
        [Math]::Max(0, $button.BackColor.G - 20),
        [Math]::Max(0, $button.BackColor.B - 20)
    )
    $button.FlatAppearance.MouseOverBackColor = [System.Drawing.Color]::FromArgb(
        [Math]::Min(255, $button.BackColor.R + 20),
        [Math]::Min(255, $button.BackColor.G + 20),
        [Math]::Min(255, $button.BackColor.B + 20)
    )
    
    return $button
}

# Create main content panel
$contentPanel = New-Object System.Windows.Forms.Panel
$contentPanel.Location = New-Object System.Drawing.Point(0, 90)
$contentPanel.Size = New-Object System.Drawing.Size(900, 610)
$contentPanel.BackColor = $ModernColors.Background
$contentPanel.Anchor = ([System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Bottom -bor [System.Windows.Forms.AnchorStyles]::Left -bor [System.Windows.Forms.AnchorStyles]::Right)
$form.Controls.Add($contentPanel)

# Create buttons with modern design and improved layout
$buttonWidth = 130
$buttonHeight = 45
$buttonSpacing = 20
$startX = 30
$startY = 30

# Create a card-like panel for service control buttons with modern styling
$servicePanel = New-Object System.Windows.Forms.Panel
$servicePanel.Location = New-Object System.Drawing.Point(30, 20)
$servicePanel.Size = New-Object System.Drawing.Size(820, 100)
$servicePanel.BackColor = $ModernColors.Surface
$servicePanel.BorderStyle = [System.Windows.Forms.BorderStyle]::FixedSingle
$contentPanel.Controls.Add($servicePanel)

# Service Control Section Label
$serviceLabel = New-Object System.Windows.Forms.Label
$serviceLabel.Location = New-Object System.Drawing.Point(20, 10)
$serviceLabel.Size = New-Object System.Drawing.Size(200, 20)
$serviceLabel.Text = $ChineseTexts.ServiceControl
$serviceLabel.Font = New-Object System.Drawing.Font("Microsoft YaHei UI", 11, [System.Drawing.FontStyle]::Bold)
$serviceLabel.ForeColor = $ModernColors.Primary
$serviceLabel.BackColor = [System.Drawing.Color]::Transparent
$servicePanel.Controls.Add($serviceLabel)

# First row - Service Control buttons
$buttonY = 35

# Start/Stop API button (toggle button)
$startApiBtn = New-ModernButton -Text $ChineseTexts.StartAPI -Location (New-Object System.Drawing.Point(20, $buttonY)) -Size (New-Object System.Drawing.Size($buttonWidth, $buttonHeight)) -Style "Primary"
$servicePanel.Controls.Add($startApiBtn)

# Start/Stop Admin button (toggle button)
$startAdminBtn = New-ModernButton -Text $ChineseTexts.StartAdmin -Location (New-Object System.Drawing.Point((20 + $buttonWidth + $buttonSpacing), $buttonY)) -Size (New-Object System.Drawing.Size($buttonWidth, $buttonHeight)) -Style "Primary"
$servicePanel.Controls.Add($startAdminBtn)

# Start/Stop All Services button (toggle button)
$startAllBtn = New-ModernButton -Text $ChineseTexts.StartAll -Location (New-Object System.Drawing.Point((20 + 2*($buttonWidth + $buttonSpacing)), $buttonY)) -Size (New-Object System.Drawing.Size($buttonWidth, $buttonHeight)) -Style "Success"
$servicePanel.Controls.Add($startAllBtn)

# Rebuild API button
$rebuildApiBtn = New-ModernButton -Text $ChineseTexts.RebuildAPI -Location (New-Object System.Drawing.Point((20 + 3*($buttonWidth + $buttonSpacing)), $buttonY)) -Size (New-Object System.Drawing.Size($buttonWidth, $buttonHeight)) -Style "Warning"
$servicePanel.Controls.Add($rebuildApiBtn)

# Create a card-like panel for utility buttons with modern styling
$utilityPanel = New-Object System.Windows.Forms.Panel
$utilityPanel.Location = New-Object System.Drawing.Point(30, 140)
$utilityPanel.Size = New-Object System.Drawing.Size(820, 100)
$utilityPanel.BackColor = $ModernColors.Surface
$utilityPanel.BorderStyle = [System.Windows.Forms.BorderStyle]::FixedSingle
$contentPanel.Controls.Add($utilityPanel)

# Utility Section Label
$utilityLabel = New-Object System.Windows.Forms.Label
$utilityLabel.Location = New-Object System.Drawing.Point(20, 10)
$utilityLabel.Size = New-Object System.Drawing.Size(200, 20)
$utilityLabel.Text = $ChineseTexts.UtilityTools
$utilityLabel.Font = New-Object System.Drawing.Font("Microsoft YaHei UI", 11, [System.Drawing.FontStyle]::Bold)
$utilityLabel.ForeColor = $ModernColors.Primary
$utilityLabel.BackColor = [System.Drawing.Color]::Transparent
$utilityPanel.Controls.Add($utilityLabel)

# Second row - Utility buttons
$buttonY2 = 35

# Check Status button
$checkStatusBtn = New-ModernButton -Text $ChineseTexts.CheckStatus -Location (New-Object System.Drawing.Point(20, $buttonY2)) -Size (New-Object System.Drawing.Size($buttonWidth, $buttonHeight)) -Style "Outline"
$utilityPanel.Controls.Add($checkStatusBtn)

# Open API URL button
$openApiBtn = New-ModernButton -Text $ChineseTexts.OpenAPI -Location (New-Object System.Drawing.Point((20 + $buttonWidth + $buttonSpacing), $buttonY2)) -Size (New-Object System.Drawing.Size($buttonWidth, $buttonHeight)) -Style "Secondary"
$utilityPanel.Controls.Add($openApiBtn)

# Open Admin URL button
$openAdminBtn = New-ModernButton -Text $ChineseTexts.OpenAdmin -Location (New-Object System.Drawing.Point((20 + 2*($buttonWidth + $buttonSpacing)), $buttonY2)) -Size (New-Object System.Drawing.Size($buttonWidth, $buttonHeight)) -Style "Secondary"
$utilityPanel.Controls.Add($openAdminBtn)

# Exit button
$exitBtn = New-ModernButton -Text $ChineseTexts.Exit -Location (New-Object System.Drawing.Point((20 + 3*($buttonWidth + $buttonSpacing)), $buttonY2)) -Size (New-Object System.Drawing.Size($buttonWidth, $buttonHeight)) -Style "Error"
$utilityPanel.Controls.Add($exitBtn)

# Create modern log area with card design and styling
$logPanel = New-Object System.Windows.Forms.Panel
$logPanel.Location = New-Object System.Drawing.Point(30, 260)
$logPanel.Size = New-Object System.Drawing.Size(820, 320)
$logPanel.BackColor = $ModernColors.Surface
$logPanel.BorderStyle = [System.Windows.Forms.BorderStyle]::FixedSingle
$contentPanel.Controls.Add($logPanel)

# Log Section Label
$logLabel = New-Object System.Windows.Forms.Label
$logLabel.Location = New-Object System.Drawing.Point(20, 15)
$logLabel.Size = New-Object System.Drawing.Size(200, 20)
$logLabel.Text = $ChineseTexts.OperationLog
$logLabel.Font = New-Object System.Drawing.Font("Microsoft YaHei UI", 11, [System.Drawing.FontStyle]::Bold)
$logLabel.ForeColor = $ModernColors.Primary
$logLabel.BackColor = [System.Drawing.Color]::Transparent
$logPanel.Controls.Add($logLabel)

# Modern log text box with improved styling
$script:logTextBox = New-Object System.Windows.Forms.TextBox
$script:logTextBox.Location = New-Object System.Drawing.Point(20, 45)
$script:logTextBox.Size = New-Object System.Drawing.Size(780, 260)
$script:logTextBox.Multiline = $true
$script:logTextBox.ScrollBars = "Vertical"
$script:logTextBox.ReadOnly = $true
$script:logTextBox.Font = New-Object System.Drawing.Font("Consolas", 9)
$script:logTextBox.BackColor = [System.Drawing.Color]::FromArgb(248, 250, 252)  # Very light gray
$script:logTextBox.ForeColor = $ModernColors.TextPrimary
$script:logTextBox.BorderStyle = [System.Windows.Forms.BorderStyle]::FixedSingle
$logPanel.Controls.Add($script:logTextBox)

# Button event handlers (using async to prevent UI freezing)
$startApiBtn.Add_Click({ 
    $jobScript = {
        try {
            # Import required functions and variables into job context
            $currentDir = Get-Location
            Set-Location $using:PWD.Path
            
            function Test-Port {
                param([int]$port, [int]$timeoutMs = 2000)
                try {
                    $connection = New-Object System.Net.Sockets.TcpClient
                    $connection.ReceiveTimeout = $timeoutMs
                    $connection.SendTimeout = $timeoutMs
                    $connection.Connect("127.0.0.1", $port)
                    $connection.Close()
                    return $true
                } catch {
                    return $false
                } finally {
                    if ($connection) {
                        try { $connection.Dispose() } catch { }
                    }
                }
            }
            
            function Stop-ServiceProcess {
                param([int]$port, [string]$serviceName)
                
                try {
                    # Find and kill processes using the port - avoid XML parsing issues
                    try {
                        $connections = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
                        if ($connections) {
                            foreach ($conn in $connections) {
                                try {
                                    $processId = $conn.OwningProcess
                                    if ($processId) {
                                        $proc = Get-Process -Id $processId -ErrorAction SilentlyContinue
                                        if ($proc) {
                                            $proc.Kill()
                                            $proc.WaitForExit(5000)
                                        }
                                    }
                                } catch {
                                    # Continue with next connection
                                }
                            }
                        }
                    } catch {
                        # Fallback to netstat if Get-NetTCPConnection fails
                        try {
                            $netstatOutput = netstat -ano | findstr ":$port "
                            if ($netstatOutput) {
                                $lines = $netstatOutput -split "`n"
                                foreach ($line in $lines) {
                                    if ($line -match "\s+(\d+)\s*$") {
                                        $processId = $matches[1]
                                        $proc = Get-Process -Id $processId -ErrorAction SilentlyContinue
                                        if ($proc) {
                                            $proc.Kill()
                                            $proc.WaitForExit(5000)
                                        }
                                    }
                                }
                            }
                        } catch {
                            # Continue to Java process cleanup
                        }
                    }
                    
                    # Kill all Java processes as a simple fallback
                    $javaProcesses = Get-Process -Name "java" -ErrorAction SilentlyContinue
                    foreach ($javaProc in $javaProcesses) {
                        try {
                            $javaProc.Kill()
                            $javaProc.WaitForExit(5000)
                        } catch { }
                    }
                    
                    Start-Sleep -Seconds 2
                } catch {
                    throw "Failed to stop service: $($_.Exception.Message)"
                }
            }
            
            function Start-ApiService {
                $jarPath = "API\target\StarProApi-2.6.jar"
                # Avoid Join-Path and (Get-Location) to prevent XML parsing issues
                $currentLocation = Get-Location
                $fullJarPath = $currentLocation.Path + "\" + $jarPath
                if (-not (Test-Path $jarPath)) {
                    # Check if the target directory exists
                    $targetDir = "API\target"
                    if (-not (Test-Path $targetDir)) {
                        throw "Target directory not found: $targetDir. Please compile the project first using Maven."
                    }
                    
                    # List files in target directory for debugging - avoid XML parsing issues
                    try {
                        $targetItems = Get-ChildItem $targetDir -ErrorAction SilentlyContinue
                        $fileList = if ($targetItems) { 
                            $names = @()
                            foreach ($item in $targetItems) {
                                $names += $item.Name
                            }
                            $names -join ", "
                        } else { 
                            "No files found" 
                        }
                    } catch {
                        $fileList = "Could not list directory contents"
                    }
                    throw "JAR file not found: $fullJarPath. Files in target directory: $fileList"
                }
                
                try {
                    # Use Start-Process to avoid potential XML parsing issues with ProcessStartInfo
                    $currentLocation = Get-Location
                    $currentPath = $currentLocation.Path
                    
                    # Start the process using Start-Process cmdlet instead of .NET Process class
                    $proc = Start-Process -FilePath "java" -ArgumentList "-jar", "`"$jarPath`"" -WorkingDirectory $currentPath -WindowStyle Hidden -PassThru
                    
                    if (-not $proc) {
                        throw "Failed to start Java process"
                    }
                    
                    # Give the service time to start and check multiple times
                    $maxAttempts = 10
                    $attempt = 0
                    $started = $false
                    
                    while ($attempt -lt $maxAttempts -and -not $started) {
                        Start-Sleep -Seconds 1
                        $attempt++
                        
                        try {
                            $started = Test-Port 7979 1500  # 1.5 second timeout per attempt
                            if ($started) {
                                return "API service started successfully on attempt $attempt"
                            }
                        } catch {
                            # Continue trying
                        }
                    }
                    
                    # Final check - just use port check to avoid any object serialization issues
                    if (Test-Port 7979 5000) {  # Give it 5 seconds for final check
                        return "API service started successfully (port responding)"
                    } else {
                        throw "API service failed to start - port 7979 not responding after $maxAttempts attempts"
                    }
                } catch {
                    throw "Failed to start API service: $($_.Exception.Message)"
                }
            }
            
            # Check current status and toggle
            if (Test-Port 7979 2000) {  # Use 2 second timeout for initial check
                # API is running, stop it
                Stop-ServiceProcess -port 7979 -serviceName "API Service"
                return "API service stopped successfully"
            } else {
                # API is not running, start it
                try {
                    $result = Start-ApiService
                    return $result
                } catch {
                    # Even if Start-ApiService throws an error, check if API is actually running
                    Start-Sleep -Seconds 2
                    if (Test-Port 7979 3000) {
                        return "API service started successfully (verified by port check despite internal errors)"
                    } else {
                        throw $_.Exception.Message
                    }
                }
            }
        } catch {
            # Final fallback - if we get here due to XML parsing or other non-critical errors,
            # but the API is actually running, report success
            Start-Sleep -Seconds 1
            if (Test-Port 7979 3000) {
                return "API service is running (verified by port check)"
            } else {
                throw "ToggleAPI Error: $($_.Exception.Message)"
            }
        }
    }
    
    # Determine action message based on current status
    $actionMessage = if (Test-Port 7979) { $ChineseTexts.StoppingAPI } else { $ChineseTexts.StartingAPI }
    Start-AsyncJob -ScriptBlock $jobScript -JobName "ToggleAPI" -Button $startApiBtn -ProcessingText $ChineseTexts.Processing -OriginalText $startApiBtn.Text -ActionMessage $actionMessage
})

$rebuildApiBtn.Add_Click({ 
    $jobScript = {
        Set-Location $using:PWD.Path
        
        function Test-Port {
            param([int]$port)
            try {
                $connection = New-Object System.Net.Sockets.TcpClient
                $connection.Connect("127.0.0.1", $port)
                $connection.Close()
                return $true
            } catch {
                return $false
            }
        }
        
        function Stop-ServiceProcess {
            param([int]$port, [string]$serviceName)
            try {
                # Avoid XML parsing issues with pipeline operations
                try {
                    $connections = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
                    if ($connections) {
                        foreach ($conn in $connections) {
                            try {
                                $processId = $conn.OwningProcess
                                if ($processId) {
                                    $proc = Get-Process -Id $processId -ErrorAction SilentlyContinue
                                    if ($proc) {
                                        try {
                                            $proc.Kill()
                                            $proc.WaitForExit(5000)
                                        } catch { }
                                    }
                                }
                            } catch {
                                # Continue with next connection
                            }
                        }
                    }
                } catch {
                    # Continue to Java process cleanup
                }
                
                $javaProcesses = Get-Process -Name "java" -ErrorAction SilentlyContinue
                foreach ($javaProc in $javaProcesses) {
                    try {
                        $javaProc.Kill()
                        $javaProc.WaitForExit(5000)
                    } catch { }
                }
                Start-Sleep -Seconds 2
            } catch { }
        }
        
        function Start-ApiService {
            $jarPath = "API\target\StarProApi-2.6.jar"
            if (-not (Test-Path $jarPath)) {
                throw "JAR file not found: $jarPath"
            }
            
            try {
                # Use Start-Process to avoid potential XML parsing issues
                $currentLocation = Get-Location
                $currentPath = $currentLocation.Path
                
                # Start the process using Start-Process cmdlet with hidden window
                $proc = Start-Process -FilePath "java" -ArgumentList "-jar", "`"$jarPath`"" -WorkingDirectory $currentPath -WindowStyle Hidden -PassThru
                
                if (-not $proc) {
                    throw "Failed to start Java process"
                }
                
                # Give the service time to start and check multiple times
                $maxAttempts = 10
                $attempt = 0
                $started = $false
                
                while ($attempt -lt $maxAttempts -and -not $started) {
                    Start-Sleep -Seconds 1
                    $attempt++
                    
                    try {
                        $started = Test-Port 7979
                        if ($started) {
                            return "API service started successfully on attempt $attempt"
                        }
                    } catch {
                        # Continue trying
                    }
                }
                
                # Final check - just use port check to avoid any object serialization issues
                if (Test-Port 7979 5000) {  # Give it 5 seconds for final check
                    return "API service started successfully (port responding)"
                } else {
                    throw "API service failed to start - port 7979 not responding after startup"
                }
            } catch {
                throw "Failed to start API service: $($_.Exception.Message)"
            }
        }
        
        # Stop existing service first
        Stop-ServiceProcess -port 7979 -serviceName "API Service"
        Start-Sleep -Seconds 2
        
        try {
            # Execute Maven compile - avoid PassThru to prevent XML parsing issues
            $currentLocation = Get-Location
            $apiPath = $currentLocation.Path + "\API"
            
            # Change to API directory and run mvn command directly
            Push-Location $apiPath
            try {
                # Use cmd to execute maven to avoid PowerShell object serialization issues
                $exitCode = 0
                try {
                    & cmd /c "mvn clean package -DskipTests >nul 2>&1"
                    if ($LASTEXITCODE -ne 0) {
                        $exitCode = $LASTEXITCODE
                    }
                } catch {
                    $exitCode = 1
                }
                
                if ($exitCode -eq 0) {
                    Pop-Location
                    Start-Sleep -Seconds 1
                    $result = Start-ApiService
                    return "Maven compilation successful. $result"
                } else {
                    Pop-Location
                    return "Maven compilation failed, exit code: $exitCode"
                }
            } catch {
                Pop-Location
                return "Error during Maven execution: $($_.Exception.Message)"
            }
        } catch {
            return "Error during compilation setup: $($_.Exception.Message)"
        }
    }
    
    Start-AsyncJob -ScriptBlock $jobScript -JobName "RebuildAPI" -Button $rebuildApiBtn -ProcessingText $ChineseTexts.Compiling -OriginalText $rebuildApiBtn.Text -ActionMessage $ChineseTexts.RebuildingAPI
})
$startAdminBtn.Add_Click({ 
    $jobScript = {
        try {
            Set-Location $using:PWD.Path
            
            function Test-Port {
                param([int]$port, [int]$timeoutMs = 2000)
                try {
                    $connection = New-Object System.Net.Sockets.TcpClient
                    $connection.ReceiveTimeout = $timeoutMs
                    $connection.SendTimeout = $timeoutMs
                    $connection.Connect("127.0.0.1", $port)
                    $connection.Close()
                    return $true
                } catch {
                    return $false
                } finally {
                    if ($connection) {
                        try { $connection.Dispose() } catch { }
                    }
                }
            }
            
            function Stop-ServiceProcess {
                param([int]$port, [string]$serviceName)
                try {
                    # Avoid XML parsing issues with pipeline operations
                    try {
                        $connections = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
                        if ($connections) {
                            foreach ($conn in $connections) {
                                try {
                                    $processId = $conn.OwningProcess
                                    if ($processId) {
                                        $proc = Get-Process -Id $processId -ErrorAction SilentlyContinue
                                        if ($proc) {
                                            try {
                                                $proc.Kill()
                                                $proc.WaitForExit(5000)
                                            } catch { }
                                        }
                                    }
                                } catch {
                                    # Continue with next connection
                                }
                            }
                        }
                    } catch {
                        # Continue to next step
                    }
                    Start-Sleep -Seconds 2
                } catch {
                    throw "Failed to stop admin service: $($_.Exception.Message)"
                }
            }
            
            function Start-AdminService {
                # Check if PHP is installed
                try {
                    $phpVersion = php -v 2>$null
                    if (-not $phpVersion) {
                        throw "PHP not found, please install PHP first"
                    }
                } catch {
                    throw "PHP not found, please install PHP first"
                }
                
                # Check if admin directory exists
                if (-not (Test-Path "admin")) {
                    throw "Admin directory not found"
                }
                
                try {
                    $processInfo = New-Object System.Diagnostics.ProcessStartInfo
                    $processInfo.FileName = "php"
                    $processInfo.Arguments = "-S 0.0.0.0:6969 -t admin"
                    $currentLocation = Get-Location
                $processInfo.WorkingDirectory = $currentLocation.Path
                    $processInfo.UseShellExecute = $false
                    $processInfo.CreateNoWindow = $true
                    
                    $adminProcess = [System.Diagnostics.Process]::Start($processInfo)
                    Start-Sleep -Seconds 3
                    
    if (Test-Port 6969) {
                        return "Admin service started successfully"
                    } else {
                        throw "Admin service failed to start - port 6969 not responding"
                    }
                } catch {
                    throw "Failed to start admin service: $($_.Exception.Message)"
                }
            }
            
            # Check current status and toggle
            if (Test-Port 6969 2000) {  # Use 2 second timeout for initial check
        # Admin is running, stop it
                Stop-ServiceProcess -port 6969 -serviceName "Admin Service"
                return "Admin service stopped successfully"
    } else {
        # Admin is not running, start it
                $result = Start-AdminService
                return $result
            }
        } catch {
            throw "ToggleAdmin Error: $($_.Exception.Message)"
        }
    }
    
    # Determine action message based on current status
    $actionMessage = if (Test-Port 6969) { $ChineseTexts.StoppingAdmin } else { $ChineseTexts.StartingAdmin }
    Start-AsyncJob -ScriptBlock $jobScript -JobName "ToggleAdmin" -Button $startAdminBtn -ProcessingText $ChineseTexts.Processing -OriginalText $startAdminBtn.Text -ActionMessage $actionMessage
})
$startAllBtn.Add_Click({ 
    $jobScript = {
        try {
            Set-Location $using:PWD.Path
            
            function Test-Port {
                param([int]$port, [int]$timeoutMs = 2000)
                try {
                    $connection = New-Object System.Net.Sockets.TcpClient
                    $connection.ReceiveTimeout = $timeoutMs
                    $connection.SendTimeout = $timeoutMs
                    $connection.Connect("127.0.0.1", $port)
                    $connection.Close()
                    return $true
                } catch {
                    return $false
                } finally {
                    if ($connection) {
                        try { $connection.Dispose() } catch { }
                    }
                }
            }
            
            function Stop-ServiceProcess {
                param([int]$port, [string]$serviceName)
                try {
                    # Avoid XML parsing issues with pipeline operations
                    try {
                        $connections = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
                        if ($connections) {
                            foreach ($conn in $connections) {
                                try {
                                    $processId = $conn.OwningProcess
                                    if ($processId) {
                                        $proc = Get-Process -Id $processId -ErrorAction SilentlyContinue
                                        if ($proc) {
                                            try {
                                                $proc.Kill()
                                                $proc.WaitForExit(5000)
                                            } catch { }
                                        }
                                    }
                                } catch {
                                    # Continue with next connection
                                }
                            }
                        }
                    } catch {
                        # Continue to next step
                    }
                    
                    # Handle Java processes for API (simplified - no WMI/CIM queries)
                    if ($port -eq 7979) {
                        $javaProcesses = Get-Process -Name "java" -ErrorAction SilentlyContinue
                        foreach ($javaProc in $javaProcesses) {
                            try {
                                $javaProc.Kill()
                                $javaProc.WaitForExit(5000)
                            } catch { }
                        }
                    }
                    
                    Start-Sleep -Seconds 2
                } catch {
                    throw "Failed to stop $serviceName : $($_.Exception.Message)"
                }
            }
            
            function Start-ApiService {
                $jarPath = "API\target\StarProApi-2.6.jar"
                if (-not (Test-Path $jarPath)) {
                    throw "API JAR file not found: $jarPath"
                }
                
                try {
                    $processInfo = New-Object System.Diagnostics.ProcessStartInfo
                    $processInfo.FileName = "java"
                    $processInfo.Arguments = "-jar `"$jarPath`""
                    $currentLocation = Get-Location
                $processInfo.WorkingDirectory = $currentLocation.Path
                    $processInfo.UseShellExecute = $false
                    $processInfo.CreateNoWindow = $true
                    
                    $apiProcess = [System.Diagnostics.Process]::Start($processInfo)
                    Start-Sleep -Seconds 5  # Increased wait time for stability
                    
                    if (Test-Port 7979 3000) {  # 3 second timeout for startup verification
                        return "API started successfully"
                    } else {
                        throw "API service failed to start - port 7979 not responding"
                    }
                } catch {
                    throw "Failed to start API service: $($_.Exception.Message)"
                }
            }
            
            function Start-AdminService {
                try {
                    $phpVersion = php -v 2>$null
                    if (-not $phpVersion) {
                        throw "PHP not found, please install PHP first"
                    }
                } catch {
                    throw "PHP not found, please install PHP first"
                }
                
                if (-not (Test-Path "admin")) {
                    throw "Admin directory not found"
                }
                
                try {
                    $processInfo = New-Object System.Diagnostics.ProcessStartInfo
                    $processInfo.FileName = "php"
                    $processInfo.Arguments = "-S 0.0.0.0:6969 -t admin"
                    $currentLocation = Get-Location
                $processInfo.WorkingDirectory = $currentLocation.Path
                    $processInfo.UseShellExecute = $false
                    $processInfo.CreateNoWindow = $true
                    
                    $adminProcess = [System.Diagnostics.Process]::Start($processInfo)
                    Start-Sleep -Seconds 3
                    
                    if (Test-Port 6969 3000) {  # 3 second timeout for startup verification
                        return "Admin started successfully"
                    } else {
                        throw "Admin service failed to start - port 6969 not responding"
                    }
                } catch {
                    throw "Failed to start Admin service: $($_.Exception.Message)"
                }
            }
    
    # Check if both services are running
            $apiRunning = Test-Port 7979 2000  # 2 second timeout for status check
            $adminRunning = Test-Port 6969 2000  # 2 second timeout for status check
    
    if ($apiRunning -and $adminRunning) {
        # Both services running, stop all
                Stop-ServiceProcess -port 7979 -serviceName "API Service"
                Stop-ServiceProcess -port 6969 -serviceName "Admin Service"
                return "All services stopped successfully"
    } else {
        # At least one service not running, start all
                $results = @()
                $errors = @()
                
                if (-not $apiRunning) {
                    try {
                        $apiResult = Start-ApiService
                        $results += $apiResult
                    } catch {
                        $errors += "API: $($_.Exception.Message)"
                    }
                }
                
                if (-not $adminRunning) {
                    try {
                        $adminResult = Start-AdminService
                        $results += $adminResult
                    } catch {
                        $errors += "Admin: $($_.Exception.Message)"
                    }
                }
                
                # Check if there were any errors
                if ($errors.Count -gt 0) {
                    $errorMessage = "Some services failed to start: " + ($errors -join "; ")
                    if ($results.Count -gt 0) {
                        $errorMessage += ". Successfully started: " + ($results -join ", ")
                    }
                    throw $errorMessage
                }
                
                return "All services started successfully: " + ($results -join ", ")
            }
        } catch {
            throw "ToggleAll Error: $($_.Exception.Message)"
        }
    }
    
    # Determine action message based on current status
    $apiRunning = Test-Port 7979
    $adminRunning = Test-Port 6969
    $actionMessage = if ($apiRunning -and $adminRunning) { $ChineseTexts.StoppingAll } else { $ChineseTexts.StartingAll }
    Start-AsyncJob -ScriptBlock $jobScript -JobName "ToggleAll" -Button $startAllBtn -ProcessingText $ChineseTexts.Processing -OriginalText $startAllBtn.Text -ActionMessage $actionMessage
})
$checkStatusBtn.Add_Click({ 
    $jobScript = {
        try {
            Set-Location $using:PWD.Path
            
            function Test-Port {
                param([int]$port, [int]$timeoutMs = 2000)
                try {
                    $connection = New-Object System.Net.Sockets.TcpClient
                    $connection.ReceiveTimeout = $timeoutMs
                    $connection.SendTimeout = $timeoutMs
                    $connection.Connect("127.0.0.1", $port)
                    $connection.Close()
                    return $true
                } catch {
                    return $false
                } finally {
                    if ($connection) {
                        try { $connection.Dispose() } catch { }
                    }
                }
            }
            
            $apiStatus = if (Test-Port 7979 2000) { $using:ChineseTexts.ServiceRunning } else { $using:ChineseTexts.ServiceStopped }
            $adminStatus = if (Test-Port 6969 2000) { $using:ChineseTexts.ServiceRunning } else { $using:ChineseTexts.ServiceStopped }
            
            return @{
                ApiStatus = $apiStatus
                AdminStatus = $adminStatus
                StatusText = "$($using:ChineseTexts.APIService)(7979): $apiStatus | $($using:ChineseTexts.AdminService)(6969): $adminStatus"
            }
        } catch {
            throw "Status Check Error: $($_.Exception.Message)"
        }
    }
    
    Start-AsyncJob -ScriptBlock $jobScript -JobName "StatusCheck" -Button $checkStatusBtn -ProcessingText $ChineseTexts.Processing -OriginalText $checkStatusBtn.Text -ActionMessage $ChineseTexts.CheckingStatus
})
$openApiBtn.Add_Click({ Open-ApiUrl })
$openAdminBtn.Add_Click({ Open-AdminUrl })
$exitBtn.Add_Click({ $form.Close() })

# Add form closing event to cleanup jobs
$form.Add_FormClosing({
    # Stop all running jobs
    foreach ($jobInfo in $script:runningJobs) {
        if ($jobInfo.Job.State -eq "Running") {
            Write-Log ([char]27491 + [char]22312 + [char]20572 + [char]27490 + [char]21518 + [char]21488 + [char]20219 + [char]21153 + ": " + $jobInfo.JobName)
            Stop-Job -Job $jobInfo.Job -ErrorAction SilentlyContinue
            Remove-Job -Job $jobInfo.Job -Force -ErrorAction SilentlyContinue
        }
    }
    
    # Also clean up any named status update jobs
    $statusJobs = Get-Job -Name "StatusUpdate*" -ErrorAction SilentlyContinue
    foreach ($statusJob in $statusJobs) {
        if ($statusJob.State -eq "Running") {
            Stop-Job -Job $statusJob -ErrorAction SilentlyContinue
        }
        Remove-Job -Job $statusJob -Force -ErrorAction SilentlyContinue
    }
    
    # Stop and dispose timer
    if ($script:statusTimer -ne $null) {
        $script:statusTimer.Stop()
        $script:statusTimer.Dispose()
        $script:statusTimer = $null
    }
    
    Write-Log $ChineseTexts.AppClosing
})

# Store button references in script scope for async access
$script:startApiBtn = $startApiBtn
$script:startAdminBtn = $startAdminBtn
$script:startAllBtn = $startAllBtn
$script:rebuildApiBtn = $rebuildApiBtn

# Initialize
Write-Log $ChineseTexts.AppStarted
Write-Log ($ChineseTexts.CurrentDirectory + " " + $PWD.Path)
Check-ServiceStatus

# Show form
$form.ShowDialog()