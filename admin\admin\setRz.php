<?php
session_start();
?>



<?php
include_once 'Nav.php';

$sql2 = "SELECT * FROM Sy_set";
$result2 = mysqli_query($connect, $sql2);
if (mysqli_num_rows($result2) > 0) {
    $row2 = mysqli_fetch_assoc($result2);
}

//获取配置有问题
$curl = curl_init();
$url = $API_GET_API_CONFIG.'?webkey='.$api_key;
curl_setopt_array($curl, array(
   CURLOPT_URL => $url,
   CURLOPT_RETURNTRANSFER => true,
   CURLOPT_ENCODING => '',
   CURLOPT_MAXREDIRS => 10,
   CURLOPT_TIMEOUT => 0,
   CURLOPT_SSL_VERIFYPEER => false,
   CURLOPT_SSL_VERIFYHOST => false,
   CURLOPT_FOLLOWLOCATION => true,
   CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
   CURLOPT_CUSTOMREQUEST => 'GET',
));

$response = curl_exec($curl);
$responseData = json_decode($response, true);  
if ($responseData && isset($responseData['code']) && $responseData['code'] == 1) {  
    $identifyiLv = $responseData['data']['identifyiLv'];    
    $identifyiIdcardHost = $responseData['data']['identifyiIdcardHost'];  
    $identifyiIdcardPath = $responseData['data']['identifyiIdcardPath'];  
    $identifyiIdcardAppcode = $responseData['data']['identifyiIdcardAppcode'];
    //企业认证接口
    // $identifyiCompanyHost = $responseData['data']['identifyiCompanyHost'];  
    // $identifyiCompanyPath = $responseData['data']['identifyiCompanyPath'];  
    // $identifyiCompanyAppcode = $responseData['data']['identifyiCompanyAppcode'];  

} 
?>
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">实名认证配置</h4>
                <form class="needs-validation" action="setRzPost.php" method="post"
                      novalidate>
                    <div class="form-group mb-3">
                          <p>在这里配置阿里的实人认证接口，用于APP中实名认证的自动审核功能。接口购买：<a href="https://market.aliyun.com/products/57000002/cmapi022049.html?spm=5176.2020520132.101.12.53ff7218GhxAJE#sku=yuncode16049000020" target="_blank">前往购买</a></p>
                    </div>
                     <div class="form-group col-sm-4">
                        <label for="identifyiLv">实名认证等级</label>
                            <select class="form-control" id="identifyiLv" name="identifyiLv">
                                <?php
                                $regions = [
                                    "0" => "关闭自动审核接口",
                                    "1" => "开启自动审核接口"
                                ];
                                
                                foreach ($regions as $key => $value) {
                                    $selected = ($identifyiLv == $key) ? "selected" : "";
                                    echo "<option value=\"$key\" $selected>$value</option>";
                                }
                                ?>
                            </select>
                    </div>
                    <div class="form-group mb-3">
                          <label for="identifyiIdcardHost">个人认证接口地址
                         </label>
                          <input name="identifyiIdcardHost" class="form-control" type="text" id="identifyiIdcardHost" placeholder="请输入个人认证接口地址" value="<?php echo $identifyiIdcardHost;  ?>">
                    </div>
                    <div class="form-group mb-3">
                          <label for="identifyiIdcardPath">个人认证接口路径
                          </label>
                          <input name="identifyiIdcardPath" class="form-control" type="text" id="identifyiIdcardPath" placeholder="请输入个人认证接口路径" value="<?php echo $identifyiIdcardPath;  ?>">
                    </div>
                    <div class="form-group mb-3">
                          <label for="identifyiIdcardAppcode">个人认证APPCode</label>
                          <input name="identifyiIdcardAppcode" class="form-control" type="text" id="identifyiIdcardAppcode" placeholder="请输入个人认证APPCode" value="<?php echo $identifyiIdcardAppcode;  ?>">
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success" type="submit" id="setRzPost">保存修改</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->




<?php
include_once 'Footer.php';
?>

</body>
</html>