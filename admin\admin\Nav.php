<?php

// 首先进行时间密钥验证
include_once 'time_key_guard.php';

include('ipban.php');

include_once 'connect.php';
$sql = "select * from Sy_login where user = '" . $_SESSION['loginadmin'] . " ' ";
$loginresult = mysqli_query($connect, $sql);
if (mysqli_num_rows($loginresult)) {
    $login = mysqli_fetch_array($loginresult);
} else {
    header("Location:auth_7f9e2a8b4c6d1e3f.php");
    die("<script>alert('参数错误')</script>");
}
$sql = "select * from Sy_login";
$result = mysqli_query($connect, $sql);
if (mysqli_num_rows($result)) {
    $login = mysqli_fetch_array($result);
}

if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['fetch'])) {
    header('Content-Type: application/json');
    function fetchData($API_IS_KEY, $api_key) {
        $url = $API_IS_KEY . "?webkey=" . $api_key;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_VERBOSE, true); 
        curl_setopt($ch, CURLOPT_HEADER, true); 

        $response = curl_exec($ch);

        if ($response === false) {
            $error = curl_error($ch);
            curl_close($ch);
            return ['code' => 0, 'msg' => '请求失败！', 'error' => $error.$url];
        } else {
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
            $header = substr($response, 0, $headerSize);
            $body = substr($response, $headerSize);

            curl_close($ch);
            if ($httpCode != 200) {
                return ['code' => 0, 'msg' => '请求失败，HTTP状态码：' . $httpCode, 'response_header' => $header];
            }

            if (empty($body)) {
                return ['code' => 0, 'msg' => '服务器返回空响应！'];
            }

            $responseData = json_decode($body, true);
            return $responseData;
        }
    }
    echo json_encode(fetchData($API_IS_KEY, $api_key));
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="A fully featured admin theme which can be used to build CRM, CMS, etc." name="description"/>
    <meta content="Coderthemes" name="author"/>
    <link href="/admin/assets/css/icons.min.css" rel="stylesheet" type="text/css"/>
    <link href="/admin/assets/css/app.min.css" rel="stylesheet" type="text/css"/>
    <link href="/Style/css/loading.css" rel="stylesheet">
    <link href="../Style/toastr/toastr.css" rel="stylesheet">
    
    
    
<style>
    .overflow-hidden {
      white-space: nowrap; /* 防止换行 */
      overflow: hidden; /* 隐藏溢出的内容 */
      text-overflow: ellipsis; /* 使用省略号表示溢出的内容 */
    }
    .badge-success-lighten {
        font-size: 0.9rem;
        font-family: "Noto Serif SC", serif;
        font-weight: 400;
        padding: 0.4rem;
    }
    .badge-info-lighten {
        font-size: 0.9rem;
        font-family: "Noto Serif SC", serif;
        font-weight: 400;
        padding: 0.4rem;
    }
    .badge-warning-lighten {
        font-size: 0.9rem;
        font-family: "Noto Serif SC", serif;
        font-weight: 400;
        padding: 0.4rem;
    }
    .badge-danger-lighten {
        font-size: 0.9rem;
        font-family: "Noto Serif SC", serif;
        font-weight: 400;
        padding: 0.4rem;
    }
    .btn.btn-warning {
        color:white;
    }
    .btn.btn-warning {
        padding: 10px 25px;
        border-radius: 20px;
    }
    .btn.btn-info {
        padding: 10px 25px;
        border-radius: 20px;
    }
    .btn.btn-primary {
        padding: 10px 25px;
        border-radius: 20px;
    }
    .button-menu-mobile span {
        background-color: #ffffff;
    }
    .side-nav .side-nav-link.active {
        color: #0acf97;
    }
    button.btn.btn-success.width-100 {
        width: 100%;
    }
    button.btn.btn-danger.width-100 {
        width: 100%;
    }
    .card {
        border-radius: 15px;
    }
    .margin-t5{
        margin-top: .5rem;
    }
    .card-header.pt-4.pb-4.text-center.bg-primary {
        border-radius: 15px 15px 0 0;
    }

    .btn-success {
        padding: 10px 25px;
        border-radius: 20px;
    }
    .btn-danger {
        padding: 10px 25px;
        border-radius: 20px;
    }
    .anchor span {
        font-family: Source Sans Pro,Helvetica Neue,Arial,sans-serif;
        background: linear-gradient(45deg,#0dcda4,#c2fcd4);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-weight: 300;
       
    }
    .btn-success {
        box-shadow: 0 2px 6px 0 #32b48f59;
        color: #fff;
        width: 100%;
        background-color: #50d5b0;
        border-color: #0acf97;
    }
    .btn-success2 {
        box-shadow: 0 2px 6px 0 #32b48f59;
        color: #fff;
        background-color: #50d5b0;
        border-color: #0acf97;
    }
    .btn-success:hover {
        box-shadow: 0 2px 6px 0 #0acf972b;
        color: #fff;
        background-color: #43c29e;
        border-color: #0acf97;
    }
    .custom-control-input:checked~.custom-control-label::before {
        color: #43c29e;
        border-color: #43c29e;
        background-color: #50d5b0;
    }
    .button-menu-mobile span {
        background-color: #6c757d;
    }
    .button-menu-mobile {
        margin: 22px 20px;
    }
    .navbar-custom .topbar-right-menu .nav-link {
        color: #6c757d;
    }
    .navbar-custom .topbar-right-menu .nav-link {
        margin: -8px 10px;
    }
</style>
</head>
<body>

<script src="../Style/jquery/jquery.min.js"></script>

<?php
$adminuser = "admin";
$adminpw = "123456";
?>

<div class="navbar-custom topnav-navbar" style="background-color: white;">
    <div class="container-fluid" style="display:flex;justify-content: space-between;">

        <div class="topnav-logo anchor">
            <span class="topnav-logo">
                StarPro
            </span>
        </div>
        <div class="button-menu-mobile disable-btn">
            <div class="lines">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
        <div class="topnav-logo-app anchor" style="display: flex;justify-content: center;align-items: center;">
            <span class="topnav-logo-app">
                StarPro
            </span>
        </div>
        <ul class="list-unstyled topbar-right-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link right-bar-toggle" href="/admin/userOperate.php">
                    <i class="dripicons-gear noti-icon"></i>
                </a>
            </li>
        </ul>
    </div>
</div>
<div class="container-fluid">

    <div class="wrapper">
        
        <div class="left-side-menu">

            <ul class="metismenu side-nav margin-menu-top">
                <li class="side-nav-item">
                    <a href="/admin/index.php" class="side-nav-link">
                        <i class="dripicons-meter"></i>
                        <span>数据面板</span> 
                        <span class="menu-arrow"></span>
                    </a>
                </li>
                <li class="side-nav-item">
                    <a href="#" class="side-nav-link">
                        <i class="dripicons-gear"></i>
                        <span>系统设置</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="metismenu side-nav">
                        <li class="side-nav-item right_10">
                            <a href="/admin/settings.php" class="side-nav-link">
                                <i class="dripicons-device-desktop"></i>
                                <span>全局设置</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                        <li class="side-nav-item right_10">
                            <a href="/admin/setUser.php" class="side-nav-link">
                                <i class="dripicons-user"></i>
                                <span>用户设置</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                        
                      <li class="side-nav-item right_10">
                        <a href="/admin/Popups.php" class="side-nav-link">
                            <i class="dripicons-message"></i>
                            <span>弹窗设置</span>
                            <span class="menu-arrow"></span>
                        </a>
                     </li>
                     <li class="side-nav-item right_10">
                            <a href="/admin/vipAdmin.php" class="side-nav-link">
                                <i class="dripicons-view-list-large"></i>
                                <span>VIP套餐</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                        <li class="side-nav-item right_10">
                            <a href="/admin/setAd.php" class="side-nav-link">
                                <i class="dripicons-broadcast"></i>   
                                <span>广告配置</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
					    <li class="side-nav-item right_10">
                            <a href="/admin/setRz.php" class="side-nav-link">
                                <i class="dripicons-user-id"></i>   
                                <span>实名认证</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                        <!--<li class="side-nav-item right_10">-->
                        <!--    <a href="/admin/otherSet.php" class="side-nav-link">-->
                        <!--        <i class="dripicons-inbox"></i>-->
                        <!--        <span>隐藏功能</span>-->
                        <!--        <span class="menu-arrow"></span>-->
                        <!--    </a>-->
                        <!--</li>-->
                    </ul>
                </li>
                <li class="side-nav-item">
                    <a href="#" class="side-nav-link">
                        <i class="dripicons-browser-upload"></i>
                        <span>核心配置</span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="metismenu side-nav">
                        <li class="side-nav-item right_10">
                            <a href="/admin/setSafe.php" class="side-nav-link">
                                <i class="dripicons-web"></i>
                                <span>安全设置</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                        
                      <li class="side-nav-item right_10">
                        <a href="/admin/setMysql.php" class="side-nav-link">
                            <i class="dripicons-stack"></i>
                            <span>Mysql设置</span>
                            <span class="menu-arrow"></span>
                        </a>
                     </li>
					  <li class="side-nav-item right_10">
                        <a href="/admin/setRedis.php" class="side-nav-link">
                            <i class="dripicons-view-apps"></i>
                            <span>Redis设置</span>
                            <span class="menu-arrow"></span>
                        </a>
                     </li>
                     <li class="side-nav-item right_10">
                        <a href="/admin/setCache.php" class="side-nav-link">
                            <i class="dripicons-vibrate"></i>
                            <span>缓存设置</span>
                            <span class="menu-arrow"></span>
                        </a>
                     </li>
                    </ul>
                </li>
                <li class="side-nav-item">
                    <a href="#" class="side-nav-link">
                        <i class="dripicons-document-edit"></i>
                        <span>发布配置</span> 
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="metismenu side-nav">
                        <li class="side-nav-item right_10">
                            <a href="/admin/setAudit.php" class="side-nav-link">
                                <i class="dripicons-inbox"></i>
                                <span>审核设置</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                        <li class="side-nav-item right_10">
                            <a href="/admin/contentViolation.php" class="side-nav-link">
                                <i class="dripicons-cloud"></i>
                                <span>内容安全</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                         <li class="side-nav-item right_10">
                            <a href="/admin/setOther.php" class="side-nav-link">
                                <i class="dripicons-scale"></i>
                                <span>更多设置</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                    </ul>
                </li>
                 <li class="side-nav-item">
                    <a href="#" class="side-nav-link">
                        <i class="dripicons-view-thumb"></i>
                        <span>应用管理</span> 
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="metismenu side-nav">
                        <li class="side-nav-item right_10">
                            <a href="/admin/appList.php" class="side-nav-link">
                                <i class="dripicons-list"></i>
                                <span>应用列表</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                         <li class="side-nav-item right_10">
                        <a href="/admin/updateAdmin.php" class="side-nav-link">
                            <i class="dripicons-cloud-upload"></i>
                            <span>版本更新</span>
                            <span class="menu-arrow"></span>
                        </a>
                        </li>
                    </ul>
                </li>
                <li class="side-nav-item">
                    <a href="#" class="side-nav-link">
                        <i class="dripicons-cloud-upload"></i>
                        <span>上传配置</span> 
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="metismenu side-nav">
                        <li class="side-nav-item right_10">
                            <a href="/admin/setUpload.php" class="side-nav-link">
                                <i class="dripicons-pamphlet"></i>
                                <span>上传设置</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                         <li class="side-nav-item right_10">
                        <a href="/admin/setCos.php" class="side-nav-link">
                            <i class="dripicons-network-3"></i>
                            <span>COS模式</span>
                            <span class="menu-arrow"></span>
                        </a>
                        </li>
                         <li class="side-nav-item right_10">
                        <a href="/admin/setOss.php" class="side-nav-link">
                            <i class="dripicons-network-2"></i>
                            <span>OSS模式</span>
                            <span class="menu-arrow"></span>
                        </a>
                        </li>
                         <li class="side-nav-item right_10">
                        <a href="/admin/setQiniu.php" class="side-nav-link">
                            <i class="dripicons-network-4"></i>
                            <span>七牛模式</span>
                            <span class="menu-arrow"></span>
                        </a>
                        </li>
                         <li class="side-nav-item right_10">
                        <a href="/admin/setFtp.php" class="side-nav-link">
                            <i class="dripicons-network-1"></i>
                            <span>FTP模式</span>
                            <span class="menu-arrow"></span>
                        </a>
                        </li>
                    </ul>
                </li>
                 <li class="side-nav-item">
                    <a href="#" class="side-nav-link">
                        <i class="dripicons-wallet"></i>
                        <span>支付配置</span> 
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="metismenu side-nav">
                        <li class="side-nav-item right_10">
                            <a href="/admin/payAlipay.php" class="side-nav-link">
                                <i class="dripicons-user-id"></i>
                                <span>支付宝支付</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                         <li class="side-nav-item right_10">
                        <a href="/admin/payWxpay.php" class="side-nav-link">
                            <i class="dripicons-ticket"></i>
                            <span>微信支付</span>
                            <span class="menu-arrow"></span>
                        </a>
                        </li>
                        <li class="side-nav-item right_10">
                            <a href="/admin/payEpay.php" class="side-nav-link">
                                <i class="dripicons-shopping-bag"></i>   
                                <span>易支付</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                    </ul>
                </li>
                 <li class="side-nav-item">
                    <a href="#" class="side-nav-link">
                        <i class="dripicons-user"></i>
                        <span>登录配置</span> 
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="metismenu side-nav">
                        <li class="side-nav-item right_10">
                            <a href="/admin/loginSms.php" class="side-nav-link">
                                <i class="dripicons-phone"></i>
                                <span>短信配置</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                         <li class="side-nav-item right_10">
                        <a href="/admin/loginWx.php" class="side-nav-link">
                            <i class="dripicons-message"></i>
                            <span>微信登录</span>
                            <span class="menu-arrow"></span>
                        </a>
                        </li>
                        <li class="side-nav-item right_10">
                            <a href="/admin/loginQQ.php" class="side-nav-link">
                                <i class="dripicons-graduation"></i>   
                                <span>QQ登录</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                    </ul>
                </li>
                <li class="side-nav-item">
                    <a href="#" class="side-nav-link">
                        <i class="dripicons-broadcast"></i>
                        <span>通知配置</span> 
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="metismenu side-nav">
                        <li class="side-nav-item right_10">
                            <a href="/admin/setPush.php" class="side-nav-link">
                                <i class="dripicons-bell"></i>
                                <span>UniPush推送</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                         <li class="side-nav-item right_10">
                        <a href="#" class="side-nav-link">
                            <i class="dripicons-calendar"></i>
                            <span>邮箱设置</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <ul class="metismenu">
                                 <li class="side-nav-item right_10">
                                        <a href="/admin/setEmail.php" class="side-nav-link">
                                            <i class="dripicons-document-remove"></i>   
                                            <span>邮箱配置</span>
                                            <span class="menu-arrow"></span>
                                        </a>
                                    </li>
                                    <li class="side-nav-item right_10">
                                        <a href="/admin/setEmailMode.php" class="side-nav-link">
                                            <i class="dripicons-article"></i>   
                                            <span>邮箱模板</span>
                                            <span class="menu-arrow"></span>
                                        </a>
                                    </li>
                                </ul>
                        </li>
                    </ul>
                </li>
                <li class="side-nav-item">
                    <a href="#" class="side-nav-link">
                        <i class="dripicons-document-new"></i>
                        <span>外观设置</span> 
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="metismenu side-nav">
                        <li class="side-nav-item right_10">
                            <a href="#" class="side-nav-link">
                                <i class="dripicons-home"></i>
                                <span>首页</span>
                                <span class="menu-arrow"></span>
                            </a>
                             <ul class="metismenu">
                                 <li class="side-nav-item right_10">
                                        <a href="/admin/homePage.php" class="side-nav-link">
                                            <i class="dripicons-home"></i>   
                                            <span>首页配置</span>
                                            <span class="menu-arrow"></span>
                                        </a>
                                    </li>
                                    <li class="side-nav-item right_10">
                                        <a href="/admin/iconAdmin.php" class="side-nav-link">
                                            <i class="dripicons-view-thumb"></i>   
                                            <span>图标模块</span>
                                            <span class="menu-arrow"></span>
                                        </a>
                                    </li>
                                </ul>
                        </li>
                        <li class="side-nav-item right_10">
                            <a href="#" class="side-nav-link">
                                <i class="dripicons-help"></i>
                                <span>圈子</span>
                                <span class="menu-arrow"></span>
                            </a>
                             <ul class="metismenu">
                                 <li class="side-nav-item right_10">
                                        <a href="/admin/circlePage.php" class="side-nav-link">
                                            <i class="dripicons-view-list-large"></i>   
                                            <span>圈子页配置</span>
                                            <span class="menu-arrow"></span>
                                        </a>
                                    </li>
                                    <li class="side-nav-item right_10">
                                        <a href="/admin/findPage.php" class="side-nav-link">
                                            <i class="dripicons-camera"></i>   
                                            <span>发现页配置</span>
                                            <span class="menu-arrow"></span>
                                        </a>
                                    </li>
                                    <li class="side-nav-item right_10">
                                        <a href="/admin/dynamicPage.php" class="side-nav-link">
                                            <i class="dripicons-photo-group"></i>   
                                            <span>动态页配置</span>
                                            <span class="menu-arrow"></span>
                                        </a>
                                    </li>
                                </ul>
                        </li>
                    </ul>
                </li>
                
                <li class="side-nav-item">
                    <a href="#" class="side-nav-link">
                        <i class="dripicons-toggles"></i>
                        <span>日常设置</span> 
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="metismenu">
                        <li class="side-nav-item right_10">
                            <a href="/admin/vipFunction.php" class="side-nav-link">
                                <i class="dripicons-star"></i>
                                <span>VIP设置</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                        <li class="side-nav-item right_10">
                            <a href="/admin/taskFunction.php" class="side-nav-link">
                                <i class="dripicons-to-do"></i>
                                <span>任务设置</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                        <li class="side-nav-item right_10">
                            <a href="/admin/signinFunction.php" class="side-nav-link">
                                <i class="dripicons-calendar"></i>   
                                <span>签到设置</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                    </ul>
                </li>
                <li class="side-nav-item">
                    <a href="#" class="side-nav-link">
                        <i class="dripicons-blog"></i>
                        <span>数据管理</span> 
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="metismenu">
                        <li class="side-nav-item right_10">
                            <a href="#" class="side-nav-link">
                             <i class="dripicons-user"></i>
                             <span>用户模块</span>
                            <span class="menu-arrow"></span>
                        </a>
                            <ul class="metismenu side-nav">
                            <li class="side-nav-item right_10">
                                <a href="/admin/userAdmin.php" class="side-nav-link">
                                    <i class="dripicons-user-group"></i>
                                    <span>用户管理</span>
                                    <span class="menu-arrow"></span>
                                </a>
                            </li>
                            <li class="side-nav-item right_10">
                                <a href="/admin/codeAdmin.php" class="side-nav-link">
                                    <i class="dripicons-user-id"></i>
                                    <span>注册码管理</span>
                                    <span class="menu-arrow"></span>
                                </a>
                            </li>
                            
                            </ul>
                        </li>
                        <li class="side-nav-item right_10">
                            <a href="/admin/adAdmin.php" class="side-nav-link">
                                <i class="dripicons-broadcast"></i>   
                                <span>广告管理</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                        <li class="side-nav-item right_10">
                            <a href="#" class="side-nav-link">
                                <i class="dripicons-help"></i>
                                <span>圈子模块</span>
                                <span class="menu-arrow"></span>
                            </a>
                             <ul class="metismenu">
                                    <li class="side-nav-item right_10">
                                        <a href="/admin/forumAdmin.php" class="side-nav-link">
                                            <i class="dripicons-document-remove"></i>   
                                            <span>帖子管理</span>
                                            <span class="menu-arrow"></span>
                                        </a>
                                    </li>
                                    <li class="side-nav-item right_10">
                                        <a href="/admin/comTzAdmin.php" class="side-nav-link">
                                            <i class="dripicons-conversation"></i>   
                                            <span>评论管理</span>
                                            <span class="menu-arrow"></span>
                                        </a>
                                    </li>
                                    <li class="side-nav-item right_10">
                                        <a href="/admin/sectionAdmin.php" class="side-nav-link">
                                            <i class="dripicons-view-list-large"></i>   
                                            <span>圈子管理</span>
                                            <span class="menu-arrow"></span>
                                        </a>
                                    </li>
                                </ul>
                        </li>
                        <li class="side-nav-item right_10">
                            <a href="#" class="side-nav-link">
                                <i class="dripicons-document"></i>
                                <span>文章模块</span>
                                <span class="menu-arrow"></span>
                            </a>
                             <ul class="metismenu">
                                    <li class="side-nav-item right_10">
                                        <a href="/admin/articleAdmin.php" class="side-nav-link">
                                            <i class="dripicons-article"></i>   
                                            <span>文章管理</span>
                                            <span class="menu-arrow"></span>
                                        </a>
                                    </li>
                                    <li class="side-nav-item right_10">
                                        <a href="/admin/comAdmin.php" class="side-nav-link">
                                            <i class="dripicons-conversation"></i>   
                                            <span>评论管理</span>
                                            <span class="menu-arrow"></span>
                                        </a>
                                    </li>
                                    <li class="side-nav-item right_10">
                                        <a href="/admin/metaAdmin.php" class="side-nav-link">
                                            <i class="dripicons-tags"></i>   
                                            <span>分类标签</span>
                                            <span class="menu-arrow"></span>
                                        </a>
                                    </li>
                                </ul>
                        </li>
                        <li class="side-nav-item right_10">
                            <a href="#" class="side-nav-link">
                                <i class="dripicons-shopping-bag"></i>   
                                <span>商品模块</span>
                                <span class="menu-arrow"></span>
                            </a>
                            <ul class="metismenu">
                                    <li class="side-nav-item right_10">
                                        <a href="/admin/shopAdmin.php" class="side-nav-link">
                                            <i class="dripicons-basket"></i>   
                                            <span>商品管理</span>
                                            <span class="menu-arrow"></span>
                                        </a>
                                    </li>
                                    <li class="side-nav-item right_10">
                                        <a href="/admin/shopTypeAdmin.php" class="side-nav-link">
                                            <i class="dripicons-message"></i>   
                                            <span>商品分类</span>
                                            <span class="menu-arrow"></span>
                                        </a>
                                    </li>
                                </ul>
                        </li>
                         <li class="side-nav-item right_10">
                            <a href="/admin/spaceAdmin.php" class="side-nav-link">
                                <i class="dripicons-photo-group"></i>   
                                <span>动态管理</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                        <li class="side-nav-item right_10">
                            <a href="/admin/.php" class="side-nav-link">
                                <i class="dripicons-checklist"></i>   
                                <span>聊天管理</span>
                                <span class="menu-arrow"></span>
                            </a>
                                <ul class="metismenu">
                                    <li class="side-nav-item right_10">
                                        <a href="/admin/chatGroupAdmin.php" class="side-nav-link">
                                            <i class="dripicons-conversation"></i>   
                                            <span>群聊管理</span>
                                            <span class="menu-arrow"></span>
                                        </a>
                                    </li>
                                    <li class="side-nav-item right_10">
                                        <a href="/admin/chatAdmin.php" class="side-nav-link">
                                            <i class="dripicons-message"></i>   
                                            <span>私聊管理</span>
                                            <span class="menu-arrow"></span>
                                        </a>
                                    </li>
                                </ul>
                            </li>
                        </li>
                        
                    </ul>
                </li>
                <li class="side-nav-item">
                    <a href="#" class="side-nav-link">
                        <i class="dripicons-wallet"></i>
                        <span>财务管理</span> 
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="metismenu side-nav">
                        <li class="side-nav-item right_10">
                            <a href="/admin/paylogAdmin.php" class="side-nav-link">
                                <i class="dripicons-graph-bar"></i>
                                <span>财务中心</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                        <li class="side-nav-item right_10">                                     
                            <a href="/admin/kmAdmin.php" class="side-nav-link">
                                <i class="dripicons-card"></i>
                                <span>卡密管理</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                        <li class="side-nav-item right_10">
                            <a href="/admin/payoutAdmin.php" class="side-nav-link">
                                <i class="dripicons-jewel"></i>
                                <span>提现管理</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                        <li class="side-nav-item right_10">
                            <a href="/admin/giftAsset.php" class="side-nav-link">
                                <i class="dripicons-ticket"></i>
                                <span>快捷充扣</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                    </ul>
                </li>
                <li class="side-nav-item">
                    <a href="#" class="side-nav-link">
                        <i class="dripicons-cloud"></i>
                        <span>扩展管理</span> 
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="metismenu side-nav">
                        <li class="side-nav-item right_10">
                            <a href="/admin/themeStore.php" class="side-nav-link">
                                <i class="dripicons-store"></i>
                                <span>主题商城</span> 
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                        <li class="side-nav-item right_10">
                            <a href="/admin/pluginStore.php" class="side-nav-link">
                                <i class="dripicons-cart"></i>
                                <span>插件商城</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                        <li class="side-nav-item right_10">
                            <a href="/admin/pluginAdmin.php" class="side-nav-link">
                                <i class="dripicons-folder-open"></i>
                                <span>插件管理</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                    </ul>
                </li>
                <li class="side-nav-item">
                    <a href="/admin/lovelist.php" class="side-nav-link">
                        <i class="dripicons-lock"></i>  
                        <span> 后台安全</span> 
                        <span class="menu-arrow"></span>
                    </a>
                         <ul class="metismenu">
                        <li class="side-nav-item right_10">
                            <a href="/admin/user.php" class="side-nav-link">
                                <i class="dripicons-user"></i>   
                                <span> 账号设置</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                        <li class="side-nav-item right_10">
                            <a href="/admin/ipList.php" class="side-nav-link">
                                <i class="dripicons-web"></i>
                                <span> 登录日志</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                        <li class="side-nav-item right_10">
                            <a href="/admin/feifa.php" class="side-nav-link">
                                <i class="dripicons-warning"></i>
                                <span> 非法操作</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                        <li class="side-nav-item right_10">
                            <a href="/admin/ipbanList.php" class="side-nav-link">
                                <i class="dripicons-wrong"></i>
                                <span> IP封禁列表</span>
                                <span class="menu-arrow"></span>
                            </a>
                        </li>
                        
                    </ul>
                    
                </li>
                <li class="side-nav-item">
                    <a href="/admin/Authorization.php" class="side-nav-link">
                        <i class="dripicons-jewel"></i>   
                        <span>授权中心</span> 
                        <span class="menu-arrow"></span>
                    </a>
                </li>
               <?php
                function getPluginsWithAdmin() {
                    $pluginsDir = realpath(__DIR__ . '/../Plugins');
                    $siteUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
                    $pluginsWithAdmin = [];
                    foreach (new DirectoryIterator($pluginsDir) as $dirInfo) {
                        if ($dirInfo->isDir() && !$dirInfo->isDot()) {
                            $pluginDir = $dirInfo->getFilename();
                            $configPath = "$pluginsDir/$pluginDir/config.ini";
                            if (file_exists($configPath)) {
                                $config = parse_ini_file($configPath, true);
                                if (isset($config['plugin']['isHaveAdmin']) && $config['plugin']['isHaveAdmin'] === 'true' && isset($config['plugin']['enabled']) && $config['plugin']['enabled'] === 'true'&& isset($config['plugin']['installed']) && $config['plugin']['installed'] === 'true') {
                                    $filename = $config['plugin']['filename'];
                                    $adminMenuUrl = "$siteUrl/Plugins/$filename/Pages/Admin/menu.json";
                                    $pluginsWithAdmin[$filename] = $adminMenuUrl;
                                }
                            }
                        }
                    }
                
                    return $pluginsWithAdmin;
                }
                
                $adminMenus = getPluginsWithAdmin();
                function renderMenu($menu, $filename, $isSubmenu = false) {
                    $html = '<li class="side-nav-item';
                    if ($isSubmenu) {
                        $html .= ' right_10';
                    }
                    $html .= '">';
                    $html .= '<a href="/Plugins/' . $filename . '/Pages/Admin/' . $menu['path'] . '" class="side-nav-link">';
                    $html .= '<i class="' . $menu['icon'] . '"></i>';
                    $html .= '<span>' . $menu['name'] . '</span>';
                    $html .= '<span class="menu-arrow"></span>';
                    $html .= '</a>';
                    
                    if (!empty($menu['submenu'])) {
                        $html .= '<ul class="metismenu side-nav">';
                        foreach ($menu['submenu'] as $submenu) {
                            $html .= renderMenu($submenu, $filename, true);
                        }
                        $html .= '</ul>';
                    }
                    $html .= '</li>';
                    return $html;
                }
                
                foreach ($adminMenus as $filename => $configFile) {
                    $config = json_decode(file_get_contents($configFile), true);
                    if ($config) {
                        echo renderMenu($config, $filename);
                    } else {
                        echo "Failed to load config from $configFile\n";
                    }
                }
                ?>

            </ul>
            <div class="clearfix"></div>
        </div>
        <div class="content-page">
            <div class="content">


                <style>
                      #editor—wrapper {
                        border: 1px solid #ccc;
                        z-index: 100;
                      }
                      #toolbar-container { border-bottom: 1px solid #ccc; }
                      #editor-container { height: 500px; }
                      #toolbar-container2 { border-bottom: 1px solid #ccc; }
                      #editor-container2 { height: 400px; }
                    .color{
                        color: #999;
                    }
                    .form-control:focus {
                        border-color: #ff5295;
                    }
                    .form-control{
                        color: #b6b6b6;
                    }
                    .side-nav .side-nav-link i {
                        height: 20px;
                    }
                    span.badge.badge-danger-lighten {
                        font-size: 0.9rem;
                        font-family: 'Noto Serif SC', serif;
                        font-weight: 400;
                    }
                    span.badge.badge-primary-lighten{
                        font-size: 0.9rem;
                        font-family: 'Noto Serif SC', serif;
                        font-weight: 400;
                    }
                    ul.text {
                        line-height: 2rem;
                        font-size: 0.9rem;
                    }

                    ul {
                        font-family: 'Noto Serif SC', serif;
                        font-weight: 400;
                    }

                    h3.mt-0, h5 {
                        font-family: 'Noto Serif SC', serif;
                        font-weight: 700;
                    }

                    .hhtext {
                        white-space: normal !important;
                    }

                    .notification-list .dropdown-menu.dropdown-menu-right {
                        border-radius: 0px 0px 10px 10px;
                        box-shadow: 0 8px 12px #efefef;
                    }

                    button.btn.btn-success.mb-2.mr-2 {
                        border-radius: 10px;
                    }

                    .badge-success-lighten {
                        font-size: 0.9rem;
                        font-family: 'Noto Serif SC', serif;
                        font-weight: 400;
                        padding: 0.4rem;
                    }

                    i {
                        font-family: 'Noto Serif SC', serif;
                        font-weight: 400;
                    }

                    .form-group.col-sm-4 {
                        margin-left: 0;
                        padding-left: 0;
                    }

                    #spotlight .header div {
                        padding-right: 40px;
                    }

                    div#test-editor {
                        z-index: 10;
                    }

                    .table td, .table th {
                        padding: 0.95rem;
                        vertical-align: middle;
                        border-top: 1px solid #e3eaef;
                    }

                    #img_url {
                        display: block;
                    }

                    .margin_0 {
                        margin: 0 !important;
                    }
                    .margin-menu-top{
                        margin-top: 20px;
                    }
                    .account-user-name {
                        text-transform: capitalize;
                    }

                    .leftbar-user .leftbar-user-name {
                        margin-left: 0px;
                    }

                    span.topnav-logo {
                        font-size: 1.8rem;
                        text-transform: capitalize;
                        font-weight: bold;
                    }
                    span.topnav-logo-app {
                        font-size: 1.5rem;
                        text-transform: capitalize;
                        font-weight: bold;
                    }
                    .card {
                        border-radius: 0.80rem;
                    }

                    .content-page {
                        padding-top: 20px;
                    }

                    h3.my-2.py-1 {
                        font-size: 2rem;
                    }

                    h3.my-2.py-1 i {
                        font-style: normal;
                        font-weight: 200;
                        font-size: 1.2rem;
                        margin-left: 0.5rem;
                    }

                    .row.footer_center {
                        width: 100%;
                        display: flex;
                        justify-content: center;
                        text-align: center;
                    }

                    .topnav-navbar {
                        padding: 0px 30px;
                        margin: 0;
                        min-height: 60px;
                    }

                    button.btn.btn-danger.mb-2.mr-2 {
                        border-radius: 0.4rem;
                    }

                    .table .action-icon:hover {
                        color: #ee7c74;
                    }

                    .form-group.mb-3.text_right {
                        text-align: right;
                    }

                    i.mdi.mdi-account-circle.mr-1.rihjt-0 {
                        margin-right: 0rem !important;
                    }

                    button.btn.btn-success {
                        border-radius: 2rem;
                    }

                    h4.header-title.mb-3 {
                        text-align: center;
                        font-size: 1.2rem;
                        font-family: 'Noto Serif SC', serif;
                        font-weight: 700;
                    }

                    label {
                        font-family: 'Noto Serif SC', serif;
                        font-weight: 400;
                    }

                    h4.header-title.mb-3.size_18 {
                        font-size: 1.8rem;
                    }

                    table.dataTable tbody td.focus, table.dataTable tbody th.focus {
                        outline: none !important;
                        outline-offset: 0;
                        /*background-color: white;*/
                    }

                    .text-muted {
                        white-space: nowrap;
                    }

                    .right_10 {
                        margin-left: 25px;
                        border-radius: 2rem;
                    }
                    .left_10 {
                        margin-right: 25px;
                        border-radius: 2rem;
                    }
                    iframe {
                        width: 100%;
                        height: 260px;
                        border-radius: 15px;
                        border: 2px solid #d9d9d9d1;
                        box-shadow: 2px 1px 15px rgb(36 37 38 / 44%);
                    }

                    quote {
                        padding: 5px 0px 5px 15px;
                        width: 100%;
                        display: block;
                        border-left: 3px solid #856f6f;
                        background: #f2f2f2;
                        border-radius: 0px 6px 6px 0px;
                        margin: 15px 0;
                    }

                    default-Code {
                        text-align: center;
                        width: 100%;
                        display: block;
                        color: #ff876c;
                    }


                    .markdown-body h1 {
                        color: #383838 !important;
                        padding-bottom: 0.3em;
                        font-size: 2.25em;
                        line-height: 1.2;
                        border-bottom: 1px solid #eee;
                    }
                    .topnav-logo-app {
                            display: none;
                        }
                    .margin_left {
                        margin-left: 10px;
                    }
                    
                    @media (min-width: 992px) {
                        .text-lg-right {
                            text-align: left !important;
                        }
                        .topnav-navbar .topnav-logo {
                            line-height: 60px;
                        }
                        .topnav-logo-app {
                            display: none;
                        }
                        .button-bg-4{
                            margin-left: 200px;
                            margin-right: 200px;
                        }
                        
                        .margin-menu-top{
                            margin-top: 20px;
                        }
                    }

                    @media (max-width: 767.98px) {
                        .navbar-custom {
                            padding: 0px;
                        }
                        
                        .topnav-navbar{
                            position: fixed;
                            z-index: 200;
                        }
                        .content-page {
                            padding-top: 80px;
                        }
                        .card {
                            box-shadow: 0 0 15px 0 rgb(191 191 191 / 26%);
                        }
                        .topnav-navbar .topnav-logo {
                            min-width: 50px;
                            display: none;
                        }
                        .margin-menu-top{
                            margin-top: 60px;
                        }
                        .topnav-logo-app {
                            min-width: 50px;
                            display: block;
                        }
                        .btn.btn-warning {
                            padding: 10px 23px;
                            border-radius: 20px;
                        }
                        .btn.btn-info {
                            padding: 10px 23px;
                            border-radius: 20px;
                        }
                        .btn.btn-primary {
                            padding: 10px 23px;
                            border-radius: 20px;
                        }
                    }
                </style>
<script>
    
    function fetchData() {
    fetch('?fetch=true')
        .then(response => response.json())
        .then(responseData => {
            if (responseData && responseData.code == 1) {
            } else if (responseData.msg == '请输入正确的访问key') {
                toastr.error('Config_DB文件中key配置错误', "提示");
            } else {
                toastr.error(responseData.msg, "提示");
            }
        })
        .catch(error => {
            toastr.error("请求失败！", "提示");
        });
    }

    fetchData();

     $(function () {
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": false,
            "progressBar": true,
            "rtl": false,
            "positionClass": "toast-top-right",
            "preventDuplicates": false,
            "onclick": null,
            "showDuration": 300,
            "hideDuration": 1000,
            "timeOut": 5000,
            "extendedTimeOut": 1000,
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };

    })
</script>
</body>
</html>