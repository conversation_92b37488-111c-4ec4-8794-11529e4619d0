<?php
session_start();
?>



<?php
include_once 'Nav.php';
$id = $_GET['id'];
$withdrawals = "SELECT * FROM typecho_forum_section WHERE type = 'sort' ORDER BY id DESC";
$withdrawalsResult = mysqli_query($connect, $withdrawals);
$withdrawals2 = "SELECT * FROM typecho_forum_section WHERE id = '$id'";
$withdrawalsResult2 = mysqli_query($connect, $withdrawals2);
$metadata = mysqli_fetch_array($withdrawalsResult2);
?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                 <?php
                    if ($metadata['type'] == 'sort') {
                        echo '<h4 class="header-title mb-3 size_18">编辑大类</h4>';
                    } else if ($metadata['type'] == 'section') {
                        echo '<h4 class="header-title mb-3 size_18">编辑圈子</h4>';
                    }       
                ?>
                <form class="needs-validation" action="sectionEditPost.php" method="post" onsubmit="return check()"
                      novalidate>
                    <div class="form-group mb-3" style="display:none;">
                        <label for="validationCustom01">类型</label>
                        <input type="text" class="form-control" id="validationCustom01" value="<?php echo $metadata['type'] ?>" placeholder="类型"
                               name="type" readonly>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom01">圈子id</label>
                        <input type="text" class="form-control" id="validationCustom01" value="<?php echo $id ?>" placeholder="圈子id"
                               name="id" readonly>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom01">名称</label>
                        <input type="text" class="form-control" id="validationCustom01" value="<?php echo $metadata['name']; ?>" placeholder="请输入名称"
                               name="name" required>
                    </div>
                     <?php
                        if ($metadata['type'] == 'sort') {
                            echo '<div class="form-group mb-3" style="display:none;">';
                        } else if ($metadata['type'] == 'section') {
                            echo '<div class="form-group mb-3">';
                        }       
                    ?>
                     
                        <label for="validationCustom01">缩略名(必须纯英文，不能有空格，不能重复)</label>
                        <input type="text" class="form-control" id="validationCustom01" value="<?php echo $metadata['slug']; ?>" placeholder="请输入缩略名(必填)"
                               name="slug">
                    </div>
                    <?php
                    if ($metadata['type'] == 'section') {
                        echo '<div style="display:block;">';
                    } else {
                        echo '<div style="display:none;">';
                    }
                    ?>
                    
                     <div class="form-group col-sm-4">
                        <label for="validationCustom01">所属大类</label>
                            <select class="form-control" id="example-select" name="parent">
                                <?php
                                while ($withdrawal = mysqli_fetch_array($withdrawalsResult)) {
                                    ?>
                                    <option value="<?php echo $withdrawal['id']; ?>" <?php if($withdrawal['id']==$metadata['parent']){echo "selected";} ?>><?php echo $withdrawal['name'] ?></option>
                                <?php
                                }
                                ?>
                            </select>
                    </div>
                     </div>
                    <div style="display:block;" style="display:none;" id="video">
                        
                    </div>
        
                    
                    <label for="validationCustom01">简介</label>
                        <textarea id="notice" class="form-control" rows="6" name="text" placeholder="请输入简介" required><?php echo $metadata['text']; ?></textarea>
                    <br />
                     <?php
                    if ($metadata['type'] == 'section') {
                        echo '<div style="display:block;">';
                    } else {
                        echo '<div style="display:none;">';
                    }
                    ?>
                     <div class="form-group col-sm-4">
                        <label for="validationCustom01">发帖权限</label>
                            <select class="form-control" id="example-select" name="restrict">
                            <?php if ($metadata['restrict']== '5') { ?>
                                <option value="5" selected>管理员</option>
                                <option value="4">圈主</option>
                                <option value="3">副圈主</option>
                                <option value="2">执行员</option>
                                <option value="1">审核员</option>
                                <option value="0">用户</option>
                            <?php } else if($metadata['restrict']== '4') { ?>
                                <option value="5">管理员</option>
                                <option value="4" selected>圈主</option>
                                <option value="3">副圈主</option>
                                <option value="2">执行员</option>
                                <option value="1">审核员</option>
                                <option value="0">用户</option>
                            <?php } else if($metadata['restrict']== '3') { ?>
                                <option value="5">管理员</option>
                                <option value="4">圈主</option>
                                <option value="3" selected>副圈主</option>
                                <option value="2">执行员</option>
                                <option value="1">审核员</option>
                                <option value="0">用户</option>
                            <?php } else if($metadata['restrict']== '2') { ?>
                                <option value="5">管理员</option>
                                <option value="4">圈主</option>
                                <option value="3">副圈主</option>
                                <option value="2" selected>执行员</option>
                                <option value="1">审核员</option>
                                <option value="0">用户</option>
                            <?php } else if($metadata['restrict']== '1') { ?>
                                <option value="5">管理员</option>
                                <option value="4">圈主</option>
                                <option value="3">副圈主</option>
                                <option value="2">执行员</option>
                                <option value="1" selected>审核员</option>
                                <option value="0">用户</option>
                            <?php } else if($metadata['restrict']== '0') { ?>
                                <option value="5">管理员</option>
                                <option value="4">圈主</option>
                                <option value="3">副圈主</option>
                                <option value="2">执行员</option>
                                <option value="1">审核员</option>
                                <option value="0" selected>用户</option>
                            <?php }?>
                            </select>
                    </div>
                    
                    <div class="form-group mb-3" id="validationCustom011">  
                        <label>图标  
                            <button type="button" id="uploadButton" class="btn btn-success2 btn-sm btn-rounded right_10"><i class="dripicons-upload"></i> 上传</button>  
                        </label>  
                        <input type="text" class="form-control" id="picLinkInput" style="display: none;" placeholder="图标链接" name="pic" value="<?php echo $metadata['pic']; ?>" readonly>  
                        <input type="file" id="uploadImage" accept="image/*" style="display: none;">  
                        <br><span class="dtr-data" id="logo1"><img style="width: 200px;object-fit: cover;margin-top:20px;margin-right:20px;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" src="<?php echo $metadata['pic'] ?>" id="picLinkInput1" class="spotlight"></span>

                    </div>  
                    <div class="form-group mb-3" id="validationCustom012">  
                        <label>背景图  
                            <button type="button" id="uploadButtonBg" class="btn btn-success2 btn-sm btn-rounded right_10"><i class="dripicons-upload"></i> 上传</button> 
                        </label>  
                        <input type="text" class="form-control" id="picLinkInputBg" style="display: none;" placeholder="背景图链接" name="bg" value="<?php echo $metadata['bg']; ?>" readonly>  
                        <input type="file" id="uploadImageBg" accept="image/*" style="display: none;">  
                        <br><span class="dtr-data" id="logo2"><img style="width: 315px;height:160px;object-fit: cover;margin-top:20px;margin-right:20px;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" src="<?php echo $metadata['bg'] ?>" id="picLinkInput2" class="spotlight"></span>
                    </div>  
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom01">排序（纯数字）</label>
                        <input type="number" class="form-control" id="validationCustom01" placeholder="请输入排序" value="<?php echo $metadata['order']; ?>"
                               name="order" required>
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-primary" type="submit" id="sectionEdithPost">修改</button>
                    </div>
                </form>

            </div> <!-- end card-body-->
        </div> <!-- end card-->
    </div> <!-- end col-->
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->

<script>
    function check() {
        let name = document.getElementsByName('name')[0].value.trim();
        let slug = document.getElementsByName('slug')[0].value.trim();
        let description = document.getElementsByName('description')[0].value.trim();
        let order = document.getElementsByName('order')[0].value.trim();
        if (name.length == 0) {
            alert("名称不能为空");
            return false;
        } else if (slug.length == 0) {
            alert("缩略名不能为空");
            return false;
        } else if (description.length == 0) {
            alert("描述不能为空");
            return false;
        } else if (order.length == 0) {
            alert("排序不能为空");
            return false;
        }
        

    }
    // 获取上传图片按钮和文件上传输入框
    var uploadButton = document.getElementById("uploadButton");
    var uploadImage = document.getElementById("uploadImage");
    var picLinkInput = document.getElementById("picLinkInput");
    var picLinkInput1 = document.getElementById("picLinkInput1");
    var picLinkInput2 = document.getElementById("picLinkInput2");
    var uploadImageBg = document.getElementById("uploadImageBg");
    var picLinkInputBg = document.getElementById("picLinkInputBg");
    var uploadButtonBg = document.getElementById("uploadButtonBg");


uploadFiles(uploadButton, uploadImage, picLinkInput, picLinkInput1, 'null');
uploadFiles(uploadButtonBg, uploadImageBg, picLinkInputBg, picLinkInput2, 'null');
<?php
include_once 'uploadJs.php';
?>

</script>

<?php
include_once 'Footer.php';
?>

</body>
</html>