<?php
session_start();
?>

<?php
include_once 'Nav.php';
$id = $_GET['id'];
$withdrawals = "SELECT * FROM typecho_shoptype WHERE parent = '0' ORDER BY id DESC";
$withdrawalsResult = mysqli_query($connect, $withdrawals);
$withdrawals2 = "SELECT * FROM typecho_shoptype WHERE id = '$id'";
$withdrawalsResult2 = mysqli_query($connect, $withdrawals2);
$metadata = mysqli_fetch_array($withdrawalsResult2);
?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3 size_18">编辑分类</h4>

                <form class="needs-validation" action="shopTypeEditPost.php" method="post" onsubmit="return check()"
                      novalidate>
                    <div class="form-group mb-3">
                        <label for="validationCustom01">分类id</label>
                        <input type="text" class="form-control" id="validationCustom01" value="<?php echo $id ?>" placeholder="分类id" name="id" readonly>
                    </div>
                    <div class="form-group mb-3" style="display:none;">
                        <label for="validationCustom01">类型</label>
                        <input type="text" class="form-control" id="type1" value="<?php echo $metadata['parent'] ?>" readonly>
                    </div>
                    <div class="form-group col-sm-4">
                        <label>类型</label>
                            <select class="form-control" name="type" readonly>
                                <?php
                                if ($metadata['parent']=='0') {
                                    echo '<option value="sort" selected>大类</option>
                                    <option value="section">小类</option>';
                                }else{
                                    echo '<option value="sort">大类</option>
                                    <option value="section" selected>小类</option>';
                                }
                                ?>
                            </select>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom01">名称</label>
                        <input type="text" class="form-control" id="validationCustom01" value="<?php echo $metadata['name'] ?>" placeholder="请输入名称"
                               name="name" required>
                    </div>
                    <div id="div2">
                        <div class="form-group col-sm-4">
                            <label for="validationCustom01">所属大类</label>
                                <select class="form-control" id="example-select" name="parent">
                                    <?php
                                    while ($withdrawal = mysqli_fetch_array($withdrawalsResult)) {
                                        ?>
                                        <option value="<?php echo $withdrawal['id'] ?>"
                                        <?php
                                        if ($withdrawal['id']==$metadata['parent']) {
                                            echo 'selected';
                                        }
                                        ?>
                                        ><?php echo $withdrawal['name'] ?></option>
                                    <?php
                                    }
                                    ?>
                                </select>
                        </div>
                     </div> 
                    <!--<label for="validationCustom01">简介</label>-->
                    <!--    <textarea id="notice" class="form-control" rows="6" name="intro" placeholder="请输入简介" ></textarea>-->
                    <!--<br />-->
                    
                   <div class="form-group mb-3" id="validationCustom011">  
                        <label>缩略图  
                            <button type="button" id="uploadButton" class="btn btn-success2 btn-sm btn-rounded right_10"><i class="dripicons-upload"></i> 上传</button>  
                        </label>  
                        <input type="text" class="form-control" id="picLinkInput" style="display: none;" placeholder="图标链接" name="pic" value="<?php echo $metadata['pic'] ?>" readonly>  
                        <input type="file" id="uploadImage" accept="image/*" style="display: none;"> 
                        <br><span class="dtr-data" id="logo1"><img style="width: 200px;object-fit: cover;margin-top:20px;margin-right:20px;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" src="<?php echo $metadata['pic'] ?>" id="picLinkInput1" class="spotlight"></span>

                   
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="validationCustom01">排序（越小越靠前）</label>
                        <input type="number" class="form-control" id="validationCustom01" placeholder="请输入排序"  value="<?php echo $metadata['orderKey'] ?>"
                               name="orderKey" required>
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-primary" type="submit" id="shopTypeAddPost">修改</button>
                    </div>
                </form>

            </div> <!-- end card-body-->
        </div> <!-- end card-->
    </div> <!-- end col-->
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->

<script>

    function check() {
        let name = document.getElementsByName('name')[0].value.trim();
        let intro = document.getElementsByName('intro')[0].value.trim();
        let pic = document.getElementsByName('pic')[0].value.trim();
        let orderKey = document.getElementsByName('orderKey')[0].value.trim();
        if (name.length == 0) {
            alert("名称不能为空");
            return false;
        } else if (intro.length == 0) {
            alert("简介不能为空");
            return false;
        } else if (pic.length == 0) {
            alert("缩略图不能为空");
            return false;
        } else if (orderKey.length == 0) {
            alert("排序不能为空");
            return false;
        }
        

    }
    // 获取上传图片按钮和文件上传输入框
    var div2 = document.getElementById("div2");
    var type1 =+ document.getElementById("type1").value;
    var uploadButton = document.getElementById("uploadButton");
    var uploadImage = document.getElementById("uploadImage");
    var picLinkInput = document.getElementById("picLinkInput");
    var picLinkInput1 = document.getElementById("picLinkInput1");
uploadFiles(uploadButton, uploadImage, picLinkInput, picLinkInput1, logo1);
<?php
include_once 'uploadJs.php';
?>
</script>

<?php
include_once 'Footer.php';
?>
</body>
</html>