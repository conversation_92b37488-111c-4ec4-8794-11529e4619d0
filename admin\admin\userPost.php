<?php
session_start();
?>

<?php
$adminName = trim($_POST['adminName']);
$pw = trim($_POST['pw']);
$file = $_SERVER['PHP_SELF'];
include_once 'connect.php';
if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }

        if ($pw) {
            $loginsql = "update Sy_login set user = '$adminName' ,pw ='" . md5($pw) . "' where id = '1'";
            session_destroy();
        } else {
            $loginsql = "update Sy_login set user = '$adminName'  where id = '1'";
        }
        $loginresult = mysqli_query($connect, $loginsql);
        if ($loginresult) {
            echo "<script>alert('修改成功');location.href = 'auth_7f9e2a8b4c6d1e3f.php';</script>";
        } else {
            echo "<script>alert('修改失败');location.href = 'auth_7f9e2a8b4c6d1e3f.php';</script>";
        }

} else {
    // 非法操作，直接跳转到警告页面，不显示弹窗
    header("Location: warning.php?route=$file");
    exit;
}

