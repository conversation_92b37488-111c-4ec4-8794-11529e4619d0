<?php
session_start();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    include_once 'connect.php';
    $forgetpopup = $_POST['Forgetpopup'];
    $postpopup = $_POST['Postpopup'];
    $shoppopup = $_POST['Shoppopup'];
    $taskpopup = $_POST['Taskpopup'];
    $signpopup = $_POST['Signpopup'];
    $alipaypopup = $_POST['Alipaypopup'];
    $wechatpopup = $_POST['Wechatpopup'];
    $camipopup = $_POST['Camipopup'];
    $yipaypopup = $_POST['Yipaypopup'];
    $loginpopup = $_POST['Loginpopup'];
    $registpopup = $_POST['Registpopup'];
    $lvtext = $_POST['lvtext'];
    $smtext = $_POST['smtext'];
    $yqtext = $_POST['yqtext'];
    $settext = $_POST['settext'];

    if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
        // 写入数据库
        $query = "UPDATE Sy_popups SET Forgetpopup=?, Postpopup=?, Shoppopup=?, Taskpopup=?, Signpopup=?, Alipaypopup=?, Wechatpopup=?, Camipopup=?, Yipaypopup=?, Loginpopup=?, Registpopup=?, lvtext=?, smtext=?, yqtext=?, settext=?";
        $stmt = $connect->prepare($query);
        $redisKeys = $connectRedis->keys('starapi_*');
            foreach ($redisKeys as $redisKey) {
                $connectRedis->del($redisKey);
            }
        if ($stmt) {
            $stmt->bind_param("sssssssssssssss", $forgetpopup, $postpopup, $shoppopup, $taskpopup, $signpopup, $alipaypopup, $wechatpopup, $camipopup, $yipaypopup, $loginpopup, $registpopup, $lvtext, $smtext, $yqtext, $settext);

            if ($stmt->execute()) {
                echo "<script>alert('更改成功');location.href = 'Popups.php';</script>";
            } else {
                echo "<script>alert('更改失败');location.href = 'Popups.php';</script>";
            }
            $stmt->close();
        } else {
            echo "<script>alert('无法连接数据库');location.href = 'Popups.php';</script>";
        }
    } else {
        echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
    }
} else {
    
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
?>
