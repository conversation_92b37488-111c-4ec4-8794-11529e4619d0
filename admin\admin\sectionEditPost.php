<?php
session_start();
?>
<?php
include_once 'connect.php';
$id = $_POST['id'];
$name = $_POST['name'];
$slug = $_POST['slug'];
$type = $_POST['type'];
$parent = $_POST['parent'];
$text = $_POST['text'];
$restrict = $_POST['restrict'];
$pic = $_POST['pic'];
$bg = $_POST['bg'];
$order = $_POST['order'];
if (empty($pic)) {
    $pic = NULL;
}
if (empty($bg)) {
    $bg = NULL;
}
if (empty($restrict)) {
    $restrict = 0;
}
if (empty($order)) {
    $order = '0';
}
if (empty($text)) {
    $text = NULL;
}
if (empty($parent)) {
    $parent = '0';
}
if($type=='sort'){
    $parent = '0';
}
$file = $_SERVER['PHP_SELF'];


if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    if($type=='sort'){
         $charu = "UPDATE typecho_forum_section SET `name`='$name' , `parent`='$parent' , `text`='$text' , `restrict`='$restrict' , `pic` = '$pic', `bg` = '$bg', `order` = '$order' WHERE id = '$id'";
    }else{
        $charu = "UPDATE typecho_forum_section SET `name`='$name' ,  `slug`='$slug' , `parent`='$parent' , `text`='$text' , `restrict`='$restrict' , `pic` = '$pic', `bg` = '$bg', `order` = '$order' WHERE id = '$id'";
    }
   
    $result = mysqli_query($connect, $charu);
    if ($result) {
            echo "<script>alert('修改成功');location.href = 'sectionAdmin.php';</script>";
        } else {
            echo "<script>alert('修改失败');history.back();</script>";   
        }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
