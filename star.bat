@echo off
setlocal enabledelayedexpansion
title YQA StarPro Service Manager
chcp 65001 >nul

:: 确保控制台不会意外关闭
if "%~1"=="" (
    cmd /k "%~f0" called
    exit /b
)

:: 处理命令行参数
if "%~1"=="called" goto menu
if "%~1"=="1" goto start_api
if "%~1"=="2" goto start_admin
if "%~1"=="3" goto start_all
if "%~1"=="4" goto check_status
if "%~1"=="5" goto stop_api
if "%~1"=="6" goto restart_api
if "%~1"=="7" goto rebuild_and_start
if "%~1"=="8" goto exit

:: 所有输出将直接显示在控制台中

echo ========================================
echo    YQA StarPro 服务管理脚本
echo    版本: 2.6.0
echo ========================================
echo.

:menu
cls
echo ========================================
echo    YQA StarPro 服务管理脚本
echo    版本: 2.6.0  
echo ========================================
echo.
echo 请选择操作:
echo 1. 启动API服务
echo 2. 启动后台管理
echo 3. 启动全部服务
echo 4. 检查服务状态
echo 5. Stop API Service
echo 6. 重启API服务
echo 7. 重新编译并启动API
echo 8. 退出
echo.
set /p choice=请输入选择 (1-8): 

:: 检查是否为空输入
if "%choice%"=="" (
    echo 请输入有效选择！
    timeout /t 2 /nobreak >nul
    goto menu
)

if "%choice%"=="1" goto start_api
if "%choice%"=="2" goto start_admin
if "%choice%"=="3" goto start_all
if "%choice%"=="4" goto check_status
if "%choice%"=="5" goto stop_api
if "%choice%"=="6" goto restart_api
if "%choice%"=="7" goto rebuild_and_start
if "%choice%"=="8" goto exit
echo 无效选择，请重新输入！
timeout /t 2 /nobreak >nul
goto menu

:rebuild_and_start
echo.
echo [重新编译并启动API服务]
echo 开始编译项目...

:: 检查API目录是否存在
if not exist "API" (
    echo [错误] 未找到API目录
    pause
    goto menu
)

:: 停止现有API服务
echo 停止现有API服务...
set found=0
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":7979" ^| findstr "LISTENING"') do (
    if not "%%a"=="" (
        echo 终止进程 %%a
        taskkill /pid %%a /f >nul 2>&1
        set found=1
    )
)
:: 关闭API服务窗口
for /f "tokens=2" %%i in ('tasklist /fi "windowtitle eq API服务*" /fo table /nh 2^>nul') do (
    if not "%%i"=="" (
        echo 关闭API服务窗口 %%i
        taskkill /pid %%i /f >nul 2>&1
    )
)
if !found!==1 (
    echo API服务已停止
    timeout /t 2 /nobreak >nul
)

echo 开始编译项目...
cd API

:: 清理之前的构建
if exist "target\StarProApi-2.6.jar" (
    echo 删除旧的jar文件...
    del "target\StarProApi-2.6.jar" >nul 2>&1
)

echo 执行编译构建...
call mvn clean package -DskipTests
if errorlevel 1 (
    echo [错误] 编译失败，请检查项目配置
    echo 常见问题：
    echo 1. 检查网络连接（下载依赖）
    echo 2. 检查Java版本是否为1.8
    echo 3. 检查项目依赖是否完整
    cd ..
    echo 按任意键返回主菜单...
    pause
    goto menu
)
cd ..

:: 检查编译后的jar文件
echo 验证构建结果...
if not exist "API\target\StarProApi-2.6.jar" (
    echo [错误] 编译完成但未找到jar文件
    echo 预期位置: API\target\StarProApi-2.6.jar
    echo 请检查Maven构建日志中的错误信息
    echo 按任意键返回主菜单...
    pause
    goto menu
)

:: 检查jar文件大小（应该大于1MB）
for %%F in ("API\target\StarProApi-2.6.jar") do (
    if %%~zF LSS 1048576 (
        echo [警告] jar文件大小异常（小于1MB），可能构建不完整
        echo 文件大小: %%~zF 字节
        if "%~1"=="" (
            echo 按任意键继续...
            pause
        ) else (
            echo 继续执行...
        )
    ) else (
        echo jar文件大小正常: %%~zF 字节
    )
)

echo [成功] 项目编译完成！
echo 正在跳转到启动服务...
goto start_api

:start_api
echo.
echo [启动API服务]
echo 检查端口7979...
netstat -an | findstr ":7979" >nul
if %errorlevel% equ 0 (
    echo API服务已在运行！
    pause
    goto menu
)

:: 检查jar文件是否存在
if not exist "API\target\StarProApi-2.6.jar" (
    echo [错误] 未找到API jar文件: API\target\StarProApi-2.6.jar
    echo 请先编译项目或检查文件路径
    pause
    goto menu
)

echo 启动API服务...
start "API服务" cmd /k "java -Xms512m -Xmx1024m -jar API\target\StarProApi-2.6.jar"
echo 等待服务启动...
timeout /t 8 /nobreak >nul

netstat -an | findstr ":7979" >nul
if %errorlevel% equ 0 (
    echo [成功] API服务启动成功！
    echo 访问地址: http://localhost:7979
) else (
    echo [失败] API服务启动失败，请查看日志
)
echo.
echo 编译和启动完成！
echo 自动返回主菜单...
timeout /t 2 /nobreak >nul
goto menu

:start_admin
echo.
echo [启动后台管理]
php -v >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到PHP，请先安装PHP
    pause
    goto menu
)

:: 检查admin目录是否存在
if not exist "admin" (
    echo [错误] 未找到admin目录
    pause
    goto menu
)

netstat -an | findstr ":6969" >nul
if %errorlevel% equ 0 (
    echo 后台管理已在运行！
    pause
    goto menu
)

echo 启动PHP服务器...
start "后台管理" cmd /k "cd admin && php -S 0.0.0.0:6969"
timeout /t 3 /nobreak >nul

echo [成功] 后台管理启动成功！
echo 访问地址: http://localhost:6969
pause
goto menu

:start_all
echo.
echo [启动全部服务]
call :start_api_silent
call :start_admin_silent
echo 全部服务启动完成！
pause
goto menu

:check_status
echo.
echo [服务状态检查]
echo 检查API服务...
netstat -an | findstr ":7979" >nul
if %errorlevel% equ 0 (
    echo [运行] API服务 - http://localhost:7979
) else (
    echo [停止] API服务未运行
)

echo 检查后台管理...
netstat -an | findstr ":6969" >nul
if %errorlevel% equ 0 (
    echo [运行] 后台管理 - http://localhost:6969
) else (
    echo [停止] 后台管理未运行
)
pause
goto menu

:stop_api
echo.
echo [停止API服务]
set found=0
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":7979" ^| findstr "LISTENING"') do (
    if not "%%a"=="" (
        echo 终止进程 %%a
        taskkill /pid %%a /f >nul 2>&1
        set found=1
    )
)
:: 关闭API服务窗口
for /f "tokens=2" %%i in ('tasklist /fi "windowtitle eq API服务*" /fo table /nh 2^>nul') do (
    if not "%%i"=="" (
        echo 关闭API服务窗口 %%i
        taskkill /pid %%i /f >nul 2>&1
    )
)
if !found!==0 (
    echo API服务未在运行
) else (
    echo API服务已停止
)
pause
goto menu

:restart_api
echo.
echo [重启API服务]
echo 停止API服务...
set found=0
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":7979" ^| findstr "LISTENING"') do (
    if not "%%a"=="" (
        echo 终止进程 %%a
        taskkill /pid %%a /f >nul 2>&1
        set found=1
    )
)
:: 关闭API服务窗口
for /f "tokens=2" %%i in ('tasklist /fi "windowtitle eq API服务*" /fo table /nh 2^>nul') do (
    if not "%%i"=="" (
        echo 关闭API服务窗口 %%i
        taskkill /pid %%i /f >nul 2>&1
    )
)
timeout /t 3 /nobreak >nul

:: 检查jar文件是否存在
if not exist "API\target\StarProApi-2.6.jar" (
    echo [错误] 未找到API jar文件: API\target\StarProApi-2.6.jar
    echo 请先编译项目或检查文件路径
    pause
    goto menu
)

echo 启动API服务...
start "API服务" cmd /k "java -Xms512m -Xmx1024m -jar API\target\StarProApi-2.6.jar"
timeout /t 8 /nobreak >nul
netstat -an | findstr ":7979" >nul
if %errorlevel% equ 0 (
    echo [成功] API服务重启成功！
) else (
    echo [失败] API服务重启失败
)
pause
goto menu

:exit
echo 感谢使用！
timeout /t 2 /nobreak >nul
exit

:start_api_silent
echo 启动API服务...
netstat -an | findstr ":7979" >nul
if %errorlevel% equ 0 (
    echo API服务已在运行！
    exit /b
)
if not exist "API\target\StarProApi-2.6.jar" (
    echo [错误] 未找到API jar文件
    exit /b
)
start "API服务" cmd /k "java -Xms512m -Xmx1024m -jar API\target\StarProApi-2.6.jar"
timeout /t 8 /nobreak >nul
netstat -an | findstr ":7979" >nul
if %errorlevel% equ 0 (
    echo [成功] API服务启动成功！
) else (
    echo [失败] API服务启动失败
)
exit /b

:start_admin_silent
echo 启动后台管理...
php -v >nul 2>&1
if %errorlevel% neq 0 (
    echo [跳过] 未安装PHP，无法启动后台管理
    exit /b
)
if not exist "admin" (
    echo [跳过] admin目录不存在
    exit /b
)
netstat -an | findstr ":6969" >nul
if %errorlevel% equ 0 (
    echo 后台管理已在运行！
    exit /b
)
start "后台管理" cmd /k "cd admin && php -S 0.0.0.0:6969"
timeout /t 3 /nobreak >nul
echo [成功] 后台管理启动成功！
exit /b