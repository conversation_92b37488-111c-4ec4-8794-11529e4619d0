<?php
session_start();
?>

<?php
include_once 'connect.php';
$sql = "select * from typecho_paylog order by pid desc";
$paylog = mysqli_query($connect, $sql);
?>

<?php
include_once 'Nav.php';
?>

<link href="/admin/assets/css/vendor/dataTables.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/responsive.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/buttons.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/select.bootstrap4.css" rel="stylesheet" type="text/css"/>
<!-- third party css end -->


<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">财务流水（积分）<a class="fabu" onclick="deleteAll()">
                        <button type="button" class="btn btn-success2 btn-sm btn-rounded right_10">
                            <i class="mdi mdi-delete-empty mr-1"></i>清空
                        </button>
                    </a></h4>
                
                <table id="basic-textlong" class="table dt-responsive nowrap" width="100%">
                    <thead>
                    <tr>
                        <th>id</th>
                        <th>订单</th>
                        <th>金额</th>
                        <th>时间</th>
                        <th>充值人</th>
                        <th>状态</th>
                        <th>单号</th>
                        <th style="width: 125px;">操作</th>
                    </tr>
                    </thead>

                    <tbody>
                    <?php
                    while ($paylogdata = mysqli_fetch_array($paylog)) {
                        ?>
                        <tr>
                            <td><?php echo $paylogdata['pid'] ?></td>
                            <td><?php echo $paylogdata['subject'] ?></td>
                            <td><?php echo $paylogdata['total_amount'] ?></td>
                            <td>
                                <small class="text-muted"><?php 
                                $timestamp = $paylogdata['created'];
                                $date = date("Y-m-d H:i:s", $timestamp);
                                echo $date?></small>
                                
                            </td>
                            <td><small class="text-muted">UID:<?php echo $paylogdata['uid'] ?></small></td>
                            <td>
                                <h5>
                                    <?php if ($paylogdata['status']== 1) { ?><span class="badge badge-success-lighten">已完成</span><?php } else { ?><span class="badge badge-warning-lighten">未支付</span><?php }?>
                                </h5>
                            </td>
                            <td>
                                <small class="text-muted"><?php echo $paylogdata['out_trade_no'] ?></small>
                            </td>
                            <td>
                                <a href="javascript:del(<?php echo $paylogdata['pid']; ?>);">
                                    <button style="white-space: nowrap;" type="button"
                                            class="btn btn-danger btn-rounded">
                                        <i class=" mdi mdi-delete-empty mr-1"></i>删除
                                    </button>
                                </a>
                            </td>
                        </tr>
                        <?php
                    }
                    ?>
                    </tbody>
                </table>

            </div>  
        </div>  
    </div> 
</div>


<script>
    function del(id) {
        if (confirm('您确认要删除该记录吗？')) {
            location.href = 'paylogDel.php?id=' + id;
        }
    }
    function deleteAll() {
        if (confirm('您确认要清空财务记录吗？')) {
            location.href = 'paylogDel.php?id=all';
        }
    }
</script>


<?php
include_once 'Footer.php';
?>

<!-- third party js -->
<script src="/admin/assets/js/vendor/jquery.dataTables.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.bootstrap4.js"></script>
<script src="/admin/assets/js/vendor/dataTables.responsive.min.js"></script>
<script src="/admin/assets/js/vendor/responsive.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.buttons.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.html5.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.flash.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.print.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.keyTable.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.select.min.js"></script>
<!-- third party js ends -->
<!-- demo app -->
<script src="/admin/assets/js/pages/demo.datatable-init.js"></script>
<!-- end demo js-->


</body>
</html>