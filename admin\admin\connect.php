<?php
// error_reporting(0);
header("Content-Type:text/html; charset=utf8");
include_once __DIR__.'/Config_DB.php';
$connect = mysqli_connect($db_address,$db_username,$db_password,$db_name);

if (!$connect) {
    die("<script>location.href = '../admin/connectDie.php';</script>");
}
$connect->query("SET NAMES 'utf8'");

$connectRedis = new Redis();
if ($redis_password) {
    $connectRedis->connect($redis_host, $redis_port);
    $connectRedis->auth($redis_password);
} else {
    $connectRedis->connect($redis_host, $redis_port);
}