<?php
session_start();
?>

<?php
include_once 'connect.php';
$sql = "SELECT * FROM typecho_metas ORDER BY `mid` DESC";
$contents = mysqli_query($connect, $sql);
$parent = $articledata['parent']; 

?>

<?php
include_once 'Nav.php';
?>

<link href="/admin/assets/css/vendor/dataTables.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/responsive.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/buttons.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/select.bootstrap4.css" rel="stylesheet" type="text/css"/>
<!-- third party css end -->


<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">分类和标签管理<a class="fabu" href="metaAdd.php">
                        <button type="button" class="btn btn-success2 btn-sm btn-rounded right_10">
                            <i class="dripicons-upload"></i> 创建
                        </button>
                    </a></h4>
                <table id="basic-textlong" class="table dt-responsive nowrap" width="100%">
                    <thead>
                    <tr>
                        <th>mid</th>
                        <th>名称</th>
                        <th>排序</th>
                        <th>类型</th>
                        <th>父级</th>
                        <th>推荐</th>
                        <th style="width: 125px;">操作</th>
                    </tr>
                    </thead>

                    <tbody>
                    <?php
                    while ($articledata = mysqli_fetch_array($contents)) {
                        ?>
                        <tr>
                            <td><?php echo $articledata['mid'] ?></td>
                           
                            <td>
                                      
                                <?php echo $articledata['name'] ?>
                            </td>
                             <td>
                                <?php echo $articledata['order'] ?>
                            </td>
                            <td>
                               <h6>
                                    <?php if ($articledata['type']== 'category') { ?><span class="badge badge-info-lighten">分类</span><?php } else { ?><span class="badge badge-warning-lighten">标签</span><?php }?>
                                </h6>
                                
                            </td>
                             <td>
                                 <small class="text-muted">
                                <?php
                                if ($articledata['parent']=='0') {
                                   echo '<span class="badge badge-info-lighten">无</span>';
                                }else{
                                     $parent = $articledata['parent'];
                                    $article = "SELECT * FROM typecho_metas WHERE mid='$parent' limit 1";
                                    $resarticle = mysqli_query($connect, $article);
                                    $mod = mysqli_fetch_array($resarticle);
                                    
                                     echo '<span class="badge badge-info-lighten">'.$mod['name'].'</span>';
                                }
                                
                                ?>
                                </small>
                            </td>
                            <td>
                               <h6>
                                    <?php if ($articledata['isrecommend']== '1') { ?><span class="badge badge-success-lighten">是</span><?php } else { ?><span class="badge badge-info-lighten">否</span><?php }?>
                                </h6>
                                
                            </td>
                            <td>
                                 <?php if ($articledata['isrecommend']== '0') { ?>
                                <a href="metaAdminPost.php?mid=<?php echo $articledata['mid']; ?>&status=Tj">
                                <button style="white-space: nowrap;" type="button"
                                        class="btn btn-primary btn-rounded">
                                    <i class="dripicons-thumbs-up"></i> 推荐
                                </button>
                                </a>
                                <?php } else {?>
                                <a href="metaAdminPost.php?mid=<?php echo $articledata['mid']; ?>&status=Qx">
                                <button style="white-space: nowrap;" type="button"
                                        class="btn btn-primary btn-rounded">
                                    <i class="dripicons-cross"></i> 取消
                                </button>
                                </a>
                                 <?php }?>
                                <a href="metaEdit.php?mid=<?php echo $articledata['mid']; ?>">
                                <button style="white-space: nowrap;" type="button"
                                        class="btn btn-info btn-rounded">
                                    <i class="dripicons-document-edit"></i> 编辑
                                </button>
                                </a>
                                <a href="javascript:del(<?php echo $articledata['mid']; ?>);">
                                    <button style="white-space: nowrap;" type="button"
                                            class="btn btn-danger btn-rounded">
                                        <i class="mdi mdi-delete-empty mr-1"></i>删除
                                    </button>
                                </a>
                            </td>
                        </tr>
                        <?php
                    }
                    ?>
                    </tbody>
                </table>

            </div>  
        </div>  
    </div> 
</div>


<script>
    function del(mid) {
        if (confirm('您确认要删除mid为' + mid + '的分类或标签吗？')) {
            location.href = 'metaAdminPost.php?mid=' + mid +'&status=Del';
        }
    }
</script>


<?php
include_once 'Footer.php';
?>

<!-- third party js -->
<script src="/admin/assets/js/vendor/jquery.dataTables.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.bootstrap4.js"></script>
<script src="/admin/assets/js/vendor/dataTables.responsive.min.js"></script>
<script src="/admin/assets/js/vendor/responsive.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.buttons.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.html5.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.flash.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.print.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.keyTable.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.select.min.js"></script>
<!-- third party js ends -->
<!-- demo app -->
<script src="/admin/assets/js/pages/demo.datatable-init.js"></script>
<!-- end demo js-->


</body>
</html>