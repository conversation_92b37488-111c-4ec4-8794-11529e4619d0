<?php
session_start();
?>

<?php
include_once 'connect.php';
$ipkiki = "select * from typecho_users order by uid desc";
$ipki = mysqli_query($connect, $ipkiki);
?>

<?php
include_once 'Nav.php';
?>

<link href="/admin/assets/css/vendor/dataTables.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/responsive.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/buttons.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/select.bootstrap4.css" rel="stylesheet" type="text/css"/>
<!-- third party css end -->

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">用户管理
                <!--<a class="fabu" href="userAdd.php">-->
                <!--        <button type="button" class="btn btn-success2 btn-sm btn-rounded right_10">-->
                <!--            <i class="dripicons-upload"></i> 添加用户-->
                <!--        </button>-->
                <!--    </a>-->
                    </h4>
                
                <table id="basic-datatable" class="table dt-responsive nowrap" width="100%">
                    <thead>
                    <tr>
                        <th>UID</th>
                        <th>用户名</th>
                        <th>用户组</th>
                        <th>IP</th>
                        <th>归属地</th>
                        <th>VIP</th>
                        <th>封禁</th>
                        <th style="width: 125px;">操作</th>
                    </tr>
                    </thead>

                    <tbody>
                    <?php
                    while ($IPinfo = mysqli_fetch_array($ipki)) {
                        ?>
                        <tr>
                            <td><?php echo $IPinfo['uid'] ?></td>
                            <td><?php echo $IPinfo['name'] ?></td>
                            <td>
                                <h5>
                                    <span class="badge badge-success-lighten">
                                    <?php
                                    if ($IPinfo['group']=='administrator') {
                                        echo '管理员';
                                    } else if($IPinfo['group']=='editor') { 
                                        echo '编辑';
                                    } else if($IPinfo['group']=='contributor') { 
                                        echo '贡献者';
                                    } else if($IPinfo['group']=='subscriber') { 
                                        echo '关注者';
                                    } else if($IPinfo['group']=='visitor') { 
                                        echo '游客';
                                    } else { 
                                        echo '错误';
                                    }
                                    ?>
                                    </span>
                                </h5>
                            </td>
                            <td><?php echo $IPinfo['ip'] ?></td>
                            <td><?php echo $IPinfo['local'] ?></td>
                             <td>
                            
                                <?php
                                $viptime = $IPinfo['vip']; 
                                
                                if ($viptime == 1) {
                                    $formattedDate = date('Y-m-d', $viptime);
                                    echo "<h5><span class='badge badge-success-lighten'>";
                                    echo "永久";
                                    echo "</span></h5>";
                                } else if ($viptime >= time()) {
                                    echo "<h5><span class='badge badge-success-lighten'>";
                                    echo "至".$formattedDate;
                                    echo "</span></h5>";
                                    
                                } else {
                                    echo "<h5><span class='badge badge-danger-lighten'>";
                                    echo "未开通";
                                    echo "</span></h5>";
                                    
                                }
                                ?>
                                
                            </td>
                            <td>
                            
                                <?php
                                $bantime = $IPinfo['bantime']; 
                                
                                if ($bantime >= time()) {
                                    $formattedDate = date('Y-m-d', $bantime);
                                    echo "<h5><span class='badge badge-danger-lighten'>";
                                    echo "至".$formattedDate;
                                    echo "</span></h5>";
                                } else {
                                    echo "<h5><span class='badge badge-success-lighten'>";
                                    echo "未封禁";
                                    echo "</span></h5>";
                                    
                                }
                                ?>
                                
                            </td>
                            <td>
                                <?php
                                $bantime = $IPinfo['bantime']; 
                                
                                if ($bantime >= time()) {
                                    $formattedDate = date('Y-m-d', $bantime);
                                    echo "<a href='javascript:banout(".$IPinfo['uid'].");'><button type='button'
                                            class='btn btn-info btn-rounded width-0'>
                                       <i class='dripicons-lock-open'></i> 解封</button></a>";
                                } else {
                                    echo "<a href='javascript:ban(".$IPinfo['uid'].");'><button type='button'
                                            class='btn btn-info btn-rounded width-0'>
                                       <i class='dripicons-warning'></i> 封禁</button></a>";
                                    
                                }
                                ?>
                                <a href="javascript:edit(<?php echo $IPinfo['uid']; ?>);">
                                    <button  type="button"
                                            class="btn btn-primary btn-rounded">
                                        <i class="dripicons-document-edit"></i> 编辑
                                    </button>
                                </a>
                                <a href="javascript:del(<?php echo $IPinfo['uid']; ?>);">
                                    <button  type="button"
                                            class="btn btn-danger btn-rounded">
                                        <i class="dripicons-trash"></i> 删除
                                    </button>
                                </a>
                            </td>
                        </tr>
                        <?php
                    }
                    ?>
                    </tbody>
                </table>

            </div>  
        </div>  
    </div> 
</div>


<script>
    function del(uid) {
        if (confirm('您确认要删除UID为 ' + uid + '的用户吗？')) {
            location.href = 'userDel.php?uid=' + uid;
        }
    }
    function ban(uid) {
        location.href = 'userBan.php?uid=' + uid;
    }
    function edit(uid) {
        location.href = 'userEdit.php?uid=' + uid;
    }
    function banout(uid) {
        if (confirm('您确认要解封UID为 ' + uid + '的用户吗？')) {
            location.href = 'userBanOut.php?uid=' + uid;
        }
    }
    

</script>


<?php
include_once 'Footer.php';
?>

<!-- third party js -->
<script src="/admin/assets/js/vendor/jquery.dataTables.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.bootstrap4.js"></script>
<script src="/admin/assets/js/vendor/dataTables.responsive.min.js"></script>
<script src="/admin/assets/js/vendor/responsive.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.buttons.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.html5.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.flash.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.print.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.keyTable.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.select.min.js"></script>
<!-- third party js ends -->
<!-- demo app -->
<script src="/admin/assets/js/pages/demo.datatable-init.js"></script>
<!-- end demo js-->


</body>
</html>