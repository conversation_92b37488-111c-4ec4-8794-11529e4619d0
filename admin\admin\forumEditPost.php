<?php
session_start();
?>
<?php
include_once 'connect.php';

$id = $_POST['id'];
$title = htmlspecialchars(trim($_POST['articletitle']),ENT_QUOTES);
$text = $_POST['articletext'];
$uid = trim($_POST['uid']);
$section = $_POST['section'];
$time = time();
$file = $_SERVER['PHP_SELF'];


if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    $withdrawals2 = "SELECT parent FROM typecho_forum_section WHERE id = '$section'";
    $withdrawalsResult2 = mysqli_query($connect, $withdrawals2);
    $withdrawal2 = mysqli_fetch_array($withdrawalsResult2);
    $typeid = $withdrawal2['parent'];
    $charu = "UPDATE typecho_forum SET title = '$title' , modified = '$time', text = '$text', section = '$section', typeid = '$typeid', authorId = '$uid' WHERE id = '$id'";
    $result = mysqli_query($connect, $charu);
    if ($result) {
            echo "<script>alert('修改成功');location.href = 'forumAdmin.php';</script>";
        } else {
            echo "<script>alert('修改失败');location.href = 'forumAdmin.php';</script>";   
        }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
