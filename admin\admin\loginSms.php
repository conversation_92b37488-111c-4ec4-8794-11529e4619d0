<?php
session_start();
?>


<?php
include_once 'Nav.php';

//获取配置有问题
$curl = curl_init();
$url = $API_GET_API_CONFIG.'?webkey='.$api_key;
curl_setopt_array($curl, array(
   CURLOPT_URL => $url,
   CURLOPT_RETURNTRANSFER => true,
   CURLOPT_ENCODING => '',
   CURLOPT_MAXREDIRS => 10,
   CURLOPT_SSL_VERIFYPEER => false,
   CURLOPT_SSL_VERIFYHOST => false,
   CURLOPT_TIMEOUT => 0,
   CURLOPT_FOLLOWLOCATION => true,
   CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
   CURLOPT_CUSTOMREQUEST => 'GET',
));
$response = curl_exec($curl);
$responseData = json_decode($response, true);  
if ($responseData && isset($responseData['code']) && $responseData['code'] == 1) {  
    $codeAccessKeyId = $responseData['data']['codeAccessKeyId'];    
    $codeAccessKeySecret = $responseData['data']['codeAccessKeySecret'];  
    $codeEndpoint = $responseData['data']['codeEndpoint'];  
    $codeTemplate = $responseData['data']['codeTemplate'];  
    $codeSignName = $responseData['data']['codeSignName'];  
    $isPhone = $responseData['data']['isPhone'];   

} 
?>
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">短信配置</h4>
                <form class="needs-validation" action="loginSmsPost.php" method="post"
                      novalidate>
                    <div class="form-group mb-3">
                          <p>目前采用阿里云短信，直接前往阿里云官方开通短信服务即可。参考文档：<a href="https://developer.aliyun.com/article/252987" target="_blank">官方文档</a></p>
                    </div>
                    <div class="form-group col-sm-4">
                        <script>
                            function isPhone2(obj) {
                                var input = document.getElementById("isPhone");
                                console.log(input);
                                if (obj.checked) {
                                    input.setAttribute("value", "1");
                                } else {
                                    input.setAttribute("value", "0");
                                }
                            }
                        </script>
                        <label for="validationCustom08">手机号登录开关</label>
                        <?php
                        if ($isPhone=='1') {
                            echo '<input type="checkbox" name="isPhone" id="isPhone" value="1" data-switch="success"
                               onclick="isPhone2(this)" checked>';
                        }else{
                            echo '<input type="checkbox" name="isPhone" id="isPhone" value="0" data-switch="success"
                               onclick="isPhone2(this)">';
                        }
                        ?>
                        
                        <label id="switchurl" style="display:block;" for="isPhone" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                    <div class="form-group mb-3">
                          <label for="codeAccessKeyId">阿里云短信AccessKeyId</label>
                          <input name="codeAccessKeyId" class="form-control" type="text" id="codeAccessKeyId" placeholder="请输入AccessKeyId" value="<?php echo $codeAccessKeyId;  ?>">
                    </div>
                    <div class="form-group mb-3">
                          <label for="codeAccessKeySecret">阿里云短信AccessKeySecret</label>
                          <input name="codeAccessKeySecret" class="form-control" type="text" id="codeAccessKeySecret" placeholder="请输入AccessKeySecret" value="<?php echo $codeAccessKeySecret;  ?>">
                    </div>
                    <div class="form-group mb-3">
                          <label for="codeEndpoint">阿里云短信请求地址</label>
                          <input name="codeEndpoint" class="form-control" type="text" id="codeEndpoint" placeholder="请输入阿里云短信请求地址" value="<?php echo $codeEndpoint;  ?>">
                    </div>
                    <div class="form-group mb-3">
                          <label for="codeTemplate">阿里云短信模板</label>
                          <input name="codeTemplate" class="form-control" type="text" id="codeTemplate" placeholder="请输入阿里云短信模板" value="<?php echo $codeTemplate;  ?>">
                    </div>
                    <div class="form-group col-sm-4">
                          <label for="codeSignName">阿里云短信签名</label>
                          <input name="codeSignName" class="form-control" type="text" id="codeSignName" placeholder="请输入阿里云短信签名" value="<?php echo $codeSignName;  ?>">
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success" type="submit" id="loginSmsPost">保存修改</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->


<?php
include_once 'Footer.php';
?>

</body>
</html>