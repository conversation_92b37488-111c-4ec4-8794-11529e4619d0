<?php
session_start();
?>

<?php
include_once 'connect.php';
$id = $_GET['id'];
$uid = $_GET['uid'];
$txje = $_GET['txje'];
$status = $_GET['status'];
$file = $_SERVER['PHP_SELF'];

if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] !== '') {
    // 清除 Redis 中的所有以 'starapi_' 开头的键
    $redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }

    if ($status === 'Pass') {
        // 查询用户的资产
        $sql = "SELECT assets FROM typecho_users WHERE uid = '$uid'";
        $result = mysqli_query($connect, $sql);

        if ($result && mysqli_num_rows($result) > 0) {
            $row = mysqli_fetch_assoc($result);
            $assets = floatval($row['assets']);  // 将 assets 转换为数字类型
            $txje = floatval($txje);             // 将 txje 转换为数字类型

            // 检查余额是否足够
            if ($assets >= $txje) {
                // 扣除提现金额
                $newAssets = $assets - $txje;
                $updateAssetsSql = "UPDATE typecho_users SET assets = '$newAssets' WHERE uid = '$uid'";

                // 更新用户资产
                if (mysqli_query($connect, $updateAssetsSql)) {
                    // 更新提现记录状态
                    $updateLogSql = "UPDATE typecho_userlog SET cid = '0' WHERE id = '$id'";
                    if (mysqli_query($connect, $updateLogSql)) {
                        echo "<script>alert('提现成功');location.href = 'payoutAdmin.php';</script>";
                    } else {
                        echo "<script>alert('提现状态更新失败');history.back();</script>";
                    }
                } else {
                    echo "<script>alert('扣除余额失败');history.back();</script>";
                }
            } else {
                // 余额不足
                echo "<script>alert('余额不足，提现失败');history.back();</script>";
            }
        } else {
            echo "<script>alert('用户不存在或查询失败');history.back();</script>";
        }
    } else if($status === 'Refuse'){
        $sql = "UPDATE typecho_userlog SET cid = '-2' WHERE id = '$id'"; 
        $result = mysqli_query($connect, $sql);
        if ($result) {
            echo "<script>alert('拒绝成功');location.href = 'payoutAdmin.php';</script>";
        } else {
            echo "<script>alert('拒绝失败');history.back();</script>";
        }
    } else if ($status === 'Del') {
        $sql = "DELETE FROM typecho_userlog WHERE id = $id";
        $result = mysqli_query($connect, $sql);
        if ($result) {
            echo "<script>alert('删除成功');location.href = 'payoutAdmin.php';</script>";
        } else {
            echo "<script>alert('删除失败')';history.back();</script>";
        }
    } else if ($status === 'all') {
        $sql = "DELETE FROM typecho_userlog WHERE type = 'withdraw'"; 
        $result = mysqli_query($connect, $sql);
        if ($result) {
            echo "<script>alert('清空成功');location.href = 'payoutAdmin.php';</script>";
        } else {
            echo "<script>alert('清空失败')';history.back();</script>";
        }
    }  else {
        echo "<script>alert('参数错误');history.back();</script>";
    }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}