<?php
session_start();
?>

<?php
include_once 'connect.php';
$uid = $_GET['uid'];
$file = $_SERVER['PHP_SELF'];

if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    if (is_numeric($uid)) {
        $updateQuery = "UPDATE typecho_users SET bantime = 0 WHERE uid = ?";
        $updateStmt = mysqli_prepare($connect, $updateQuery);
        mysqli_stmt_bind_param($updateStmt, "s", $uid);
        $updateResult = mysqli_stmt_execute($updateStmt);
        if ($updateResult) {
            echo "<script>alert('解封成功');location.href = 'userAdmin.php';</script>";
        } else {
            echo "<script>alert('解封失败')';history.back();</script>";
        }
    } else {
        echo "<script>alert('参数错误');history.back();</script>";
    }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}